<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.need.framework</groupId>
        <artifactId>need-parent</artifactId>
        <version>frp-dev.41-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>cn.need.framework.starter</groupId>
    <artifactId>need-starter</artifactId>
    <version>frp-dev.41-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>need-starter-job</module>
        <module>need-starter-mybatis</module>
        <module>need-starter-rabbitmq</module>
        <module>need-starter-web</module>
        <module>need-starter-redis</module>
        <module>need-starter-security</module>
        <module>need-starter-tenant</module>
        <module>need-starter-warehouse</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
