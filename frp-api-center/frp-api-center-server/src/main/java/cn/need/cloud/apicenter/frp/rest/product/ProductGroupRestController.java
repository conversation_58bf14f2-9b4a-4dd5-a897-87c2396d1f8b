package cn.need.cloud.apicenter.frp.rest.product;

import cn.need.cloud.apicenter.frp.client.constant.ProductGroupApiPath;
import cn.need.cloud.apicenter.frp.service.product.ProductGroupService;
import cn.need.cloud.biz.client.dto.req.base.BaseDeleteReqDTO;
import cn.need.cloud.biz.client.dto.req.product.ProductGroupCreateOrUpdateReqDTO;
import cn.need.framework.common.support.api.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(ProductGroupApiPath.PREFIX)
@Tag(name = "外部接口- product group API")
@Slf4j
public class ProductGroupRestController {
    @Resource
    private ProductGroupService productGroupService;

    @Operation(summary = "创建或更新产品组合")
    @PostMapping(value = ProductGroupApiPath.CREATE_UPDATE)

    public Result<Integer> createOrUpdate(@RequestBody ProductGroupCreateOrUpdateReqDTO reqDTO) {
        log.info("ProductGroup createOrUpdate reqDTO: {}", reqDTO);
        // 返回影响行数
        return productGroupService.createOrUpdate(reqDTO);
    }

    @Operation(summary = "删除产品组合")
    @PostMapping(value = ProductGroupApiPath.DELETE)

    public Result<Integer> remove(@RequestBody BaseDeleteReqDTO reqDTO) {

        //返回影响行数
        return productGroupService.remove(reqDTO);
    }

}
