package cn.need.cloud.apicenter.frp.rest.otb;

import cn.need.cloud.apicenter.frp.client.constant.OtbShipmentApiPath;
import cn.need.cloud.apicenter.frp.service.otb.OtbShipmentService;
import cn.need.cloud.biz.client.dto.req.inbound.BaseDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbShipmentDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbShipmentQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.otb.OtbShipmentPageRespDTO;
import cn.need.cloud.biz.client.dto.resp.otb.OtbShipmentRespDTO;
import cn.need.cloud.biz.client.dto.resp.otc.RequestProductRespDTO;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(OtbShipmentApiPath.PREFIX)
@Tag(name = "外部接口-shipmentAPI")
public class OtbShipmentRestController extends AbstractController {
    @Resource
    private OtbShipmentService otbShipmentService;


    @Operation(summary = "详情")
    @PostMapping(value = OtbShipmentApiPath.DETAIL)

    public Result<OtbShipmentRespDTO> detail(@RequestBody OtbShipmentDetailQueryReqDTO query) {

        //构建返回参数
        return otbShipmentService.detail(query);
    }

    @Operation(summary = "列表")
    @PostMapping(value = OtbShipmentApiPath.LIST)

    public Result<PageData<OtbShipmentPageRespDTO>> list(@RequestBody PageSearch<OtbShipmentQueryReqDTO> search) {

        //返回结果
        return otbShipmentService.list(search);
    }

    @PostMapping(value = OtbShipmentApiPath.REQUEST_ACTUAL_PRODUCT)
    @Operation(summary = "获取实际产品")
    Result<List<RequestProductRespDTO>> requestActualProduct(@RequestBody BaseDetailQueryReqDTO query) {
        return otbShipmentService.requestActualProduct(query);
    }

}
