package cn.need.cloud.apicenter.frp.model.entity;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 第三方接口调用记录的请求信息
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("third_record_request")
public class ThirdRecordRequest extends SuperModel {

    /**
     * 请求记录标记，取值uuid
     */
    @TableField("record_id")
    private String recordId;

    /**
     * http请求方法，GET或POST
     */
    @TableField("http_method")
    private String httpMethod;

    /**
     * 接口地址
     */
    @TableField("api_url")
    private String apiUrl;

    /**
     * 请求类型
     */
    @TableField("content_type")
    private String contentType;

    /**
     * 请求头信息
     */
    @TableField("headers")
    private String headers;

    /**
     * 请求参数信息
     */
    @TableField("params")
    private String params;
}
