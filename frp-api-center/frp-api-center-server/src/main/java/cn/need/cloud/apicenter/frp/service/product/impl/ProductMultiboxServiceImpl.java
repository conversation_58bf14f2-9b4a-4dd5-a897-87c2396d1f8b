package cn.need.cloud.apicenter.frp.service.product.impl;

import cn.need.cloud.apicenter.frp.service.product.ProductMultiboxService;
import cn.need.cloud.biz.client.api.product.ProductMultiboxClient;
import cn.need.cloud.biz.client.dto.req.base.BaseDeleteReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseProductQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.product.ProductMultiboxCreateOrUpdateReqDTO;
import cn.need.cloud.biz.client.dto.resp.product.ProductMultiboxListRespDTO;
import cn.need.framework.common.support.api.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 产品Multibox管理 服务实现类
 * <p>
 * 该接口提供产品Multibox相关的远程调用功能，包括产品Multibox组合的增删改查等操作。
 * 通过OpenFeign实现微服务间的通信。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since frp-dev.25
 */
@Service
public class ProductMultiboxServiceImpl implements ProductMultiboxService {
    @Resource
    private ProductMultiboxClient productMultiboxClient;

    @Override
    public Result<Integer> createOrUpdate(ProductMultiboxCreateOrUpdateReqDTO reqDTO){
        return productMultiboxClient.createOrUpdate(reqDTO);
    }

    @Override
    public Result<Integer> remove( BaseDeleteReqDTO baseDeleteReqDTO){
        return productMultiboxClient.remove(baseDeleteReqDTO);
    }

    @Override
    public Result<List<ProductMultiboxListRespDTO>> listByProductId(BaseProductQueryReqDTO queryReqDTO){
        return productMultiboxClient.listByProductId(queryReqDTO);
    }
}
