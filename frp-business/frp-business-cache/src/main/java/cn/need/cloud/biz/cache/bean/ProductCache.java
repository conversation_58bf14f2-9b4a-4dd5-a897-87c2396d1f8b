package cn.need.cloud.biz.cache.bean;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * <p>
 * 产品 缓存
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
public class ProductCache implements Serializable {

    @Serial
    private static final long serialVersionUID = 194211791495435145L;


    /**
     * 主键
     */
    private Long id;
    /**
     * 交易伙伴ID
     */
    private Long transactionPartnerId;

    /**
     * 唯一标识码
     */
    private String refNum;

    /**
     * 供应商SKU
     */

    private String supplierSku;

    /**
     * UPC码
     */

    private String upc;

    /**
     * 组装产品标志
     */

    private Boolean assemblyProductFlag;


    /**
     * 备注
     */
    private String note;

    /**
     * 描述
     */
    private String description;

    /**
     * 标题
     */
    private String title;


    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 多箱标志
     */
    private Boolean multiboxFlag;

    /**
     * 组类型
     */
    private String groupType;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * group 版本号
     */
    private String groupVersionRefNum;
    /**
     * multibox 版本号
     */
    private String multiboxVersionRefNum;
    /**
     * assembly 版本号
     */
    private String assemblyVersionRefNum;
    /**
     * hazmat 版本号
     */
    private String hazmatVersionRefNum;

}
