package cn.need.cloud.biz.client.constant.enums.otc;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;


/***
 * OtcPalletStatus.java
 *
 * <AUTHOR>
 * @since 2024-11-23
 */
@AllArgsConstructor
@Getter
public enum OtcPalletStatus {

    /**
     * 新建
     */
    NEW("New"),

    /**
     * 已上架
     */
    PUT_AWAY("PutAway"),

    /**
     * 已拆托
     */
    APART("Apart");

    @EnumValue
    @JsonValue
    private final String status;
}
