package cn.need.cloud.biz.client.constant.enums.otb;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Otb出库工单状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OtbPrepWorkOrderEnum {

    /**
     * 新建
     */
    NEW("New"),
    /**
     * 开始
     */
    BEGIN("Begin"),

    /**
     * 拣货中
     */
    IN_PICKING("InPicking"),

    /**
     * 完成拣货
     */

    PICKED("Picked"),

    /**
     * 处理完成
     */
    PROCESSED("Processed"),

    /**
     * 取消
     */
    CANCELLED("Cancelled"),

    /**
     * 暂停
     */
    ON_HOLD("OnHold"),

    LOCKED("Locked"),

    ;

    @EnumValue
    @JsonValue
    private final String status;
}
