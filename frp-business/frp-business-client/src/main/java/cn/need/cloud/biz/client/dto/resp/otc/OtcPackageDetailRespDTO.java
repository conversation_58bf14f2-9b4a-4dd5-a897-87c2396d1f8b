package cn.need.cloud.biz.client.dto.resp.otc;

import cn.need.cloud.biz.client.dto.req.base.BaseProductDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTC包裹详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTC包裹详情 vo对象")
public class OtcPackageDetailRespDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 2L;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 产品
     */
    @Schema(description = "产品")
    private BaseProductDTO baseProductDTO;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

}