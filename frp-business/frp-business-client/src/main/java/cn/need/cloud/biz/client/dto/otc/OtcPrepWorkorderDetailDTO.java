package cn.need.cloud.biz.client.dto.otc;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC预提工单详情 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtcPrepWorkorderDetailDTO extends SuperDTO {


    /**
     * 数量
     */
    private Integer qty;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 发货到c端预工单id
     */
    private Long otcPrepWorkorderId;

    /**
     * 行序号
     */
    private Integer lineNum;

    /**
     * 库存锁定id
     */
    private Long inventoryLockedId;

    /**
     * 拣货数量
     */
    private Integer pickedQty;

    /**
     * 上架数量
     */
    private Integer putawayQty;

}