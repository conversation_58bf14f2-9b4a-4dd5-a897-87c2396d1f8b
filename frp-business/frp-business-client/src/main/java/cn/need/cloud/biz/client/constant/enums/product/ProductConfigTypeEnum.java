package cn.need.cloud.biz.client.constant.enums.product;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;


/***
 * 产品类型枚举
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Getter
@AllArgsConstructor
public enum ProductConfigTypeEnum {

    /**
     * Multibox
     */
    MULTIBOX("Multibox"),

    /**
     * Assembly
     */
    ASSEMBLY("Assembly"),

    /**
     * Group
     */
    GROUP("Group"),

    /**
     * Hazmat
     */
    HAZMAT("Hazmat"),
    ;
    @EnumValue
    @JsonValue
    private final String type;


}
