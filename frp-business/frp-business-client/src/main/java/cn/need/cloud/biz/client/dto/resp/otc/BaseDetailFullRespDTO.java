package cn.need.cloud.biz.client.dto.resp.otc;

import cn.need.cloud.biz.client.dto.req.base.BaseProductDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;


/**
 * 基础详情vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "基础详情vo对象")
public class BaseDetailFullRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 2382678003490595306L;

    @Schema(description = "数量")
    @NotNull(message = "qty cannot be empty")
    @Min(value = 0, message = "qty must be a non-negative integer")
    private Integer qty;


    @Schema(description = "行号")
    @NotNull(message = "lineNum must not be null.")
    @Min(value = 1, message = "lineNum can not be less than 1")
    private Integer lineNum;

    @Schema(description = "基础产品信息")
    private BaseProductDTO baseProductDTO;

}