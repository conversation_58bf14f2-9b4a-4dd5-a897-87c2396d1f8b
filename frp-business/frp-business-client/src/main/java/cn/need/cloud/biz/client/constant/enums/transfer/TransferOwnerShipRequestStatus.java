package cn.need.cloud.biz.client.constant.enums.transfer;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 货权转移状态
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@AllArgsConstructor
@Getter
public enum TransferOwnerShipRequestStatus {

    /**
     * 新建
     */
    NEW("New"),

    /**
     * 拒绝
     */
    REJECTED("Rejected"),

    /**
     * 同意
     */
    APPROVED("Approved"),
    ;

    @EnumValue
    @JsonValue
    private final String status;


    public static TransferOwnerShipRequestStatus statusOf(String type) {
        return Arrays.stream(TransferOwnerShipRequestStatus.values())
                .filter(obj -> Objects.equals(obj.getStatus(), type))
                .findAny()
                .orElse(null);
    }

    public static List<String> canAudit() {
        return Arrays.asList(TransferOwnerShipRequestStatus.NEW.getStatus(), TransferOwnerShipRequestStatus.REJECTED.getStatus());
    }
}
