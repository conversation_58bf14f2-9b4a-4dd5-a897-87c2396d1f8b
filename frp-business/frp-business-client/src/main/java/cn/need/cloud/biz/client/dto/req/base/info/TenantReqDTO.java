package cn.need.cloud.biz.client.dto.req.base.info;

import cn.need.framework.common.core.lang.StringUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * Tenant vo对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@Schema(description = "Tenant dto对象")
public class TenantReqDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -9050326514403789806L;

    /**
     * 参考编码
     */
    @Schema(description = "参考编码")
    private String refNum;


    /**
     * abbrName
     */
    @Schema(description = "abbrName")
    private String abbrName;

    /**
     * 租户id
     */
    @JsonIgnore
    private Long tenantId;

    /**
     * ShowString
     */
    @JsonIgnore
    public String getShowString() {
        return StringUtil.format("Tenant(refNum:{},abbrName:{})", refNum, abbrName);
    }
}
