package cn.need.cloud.biz.client.dto;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 预请求详情 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PrepRequestDetailDTO extends SuperDTO {


    /**
     * 准备请求ID
     */
    private Long prepRequestId;

    /**
     * 准备类型
     */
    private String prepType;

    /**
     * 备注
     */
    private String note;

    /**
     * 行序号
     */
    private Integer lineNum;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 产品id
     */
    private Long productId;

}