package cn.need.cloud.biz.client.constant.enums.profile;

import lombok.AllArgsConstructor;
import lombok.Getter;

/***
 * 配置ServiceType枚举
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Getter
@AllArgsConstructor
public enum ProfileServiceTypeEnum {

    ALL("All"),

    USER("User"),

    FRP("FRP"),

    SRP("SRP"),

    SHIP("Ship"),

    CHANNEL_UNIFY("ChannelUnify"),
    ;

    private final String type;
}
