package cn.need.cloud.biz.client.constant.enums.feeconfig;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FeeCalculationTypeEnum {

    /**
     * 基础值：直接使用 detail 的基础计算结果
     */
    FIXED("FIXED"),

    /**
     * 乘法计算：detail 结果乘以 condition_type 的值
     */
    MUL("Multiply");

    @EnumValue
    @JsonValue
    private final String code;


}

