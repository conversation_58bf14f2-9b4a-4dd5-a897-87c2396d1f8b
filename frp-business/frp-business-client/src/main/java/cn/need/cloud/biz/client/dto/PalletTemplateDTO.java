package cn.need.cloud.biz.client.dto;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 打托模板 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PalletTemplateDTO extends SuperDTO {


    /**
     * 每层几个箱子
     */
    private Integer cartonPerLayer;

    /**
     * 一共多少层
     */
    private Integer layersCount;

    /**
     * 多了几个箱子
     */
    private Integer extCarton;

    /**
     * 每箱几个产品
     */
    private Integer pcsPerCarton;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 备注
     */
    private String note;

    /**
     * 唯一标识码
     */
    private String refNum;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 是否设置默认
     */
    private Boolean setDefaultFlag;

    /**
     * 产品id
     */
    private Long productVersionId;

}