package cn.need.cloud.biz.client.dto.resp.binlocation;

import cn.need.cloud.biz.client.dto.req.base.BaseProductVersionAndBinLocationRespDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 库位详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "库位详情 vo对象")
public class BinLocationDetailRespDTO extends BaseProductVersionAndBinLocationRespDTO {


    /**
     * 实际数量
     */
    @Schema(description = "实际数量")
    private Integer inStockQty;

    /**
     * 可获取数量
     */
    @Schema(description = "可获取数量")
    private Integer availableQty;

    /**
     * 锁定库存数量
     */
    @Schema(description = "锁定库存数量")
    private Integer lockedQty;

    /**
     * 锁定库存信息
     */
    @Schema(description = "锁定库存信息")
    private List<BinLocationDetailLockedRespDTO> binLocationDetailLockedList;

}