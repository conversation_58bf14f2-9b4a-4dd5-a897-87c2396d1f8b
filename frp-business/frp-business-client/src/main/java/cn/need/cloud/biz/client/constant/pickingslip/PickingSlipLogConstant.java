package cn.need.cloud.biz.client.constant.pickingslip;

/**
 * 拣货单日志常量
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public class PickingSlipLogConstant {

    /**
     * 拣货描述前缀
     */
    public static final String IN_PICKING_DESCRIPTION_PREFIX = "Build in ";

    /**
     * PrepPickingSlip OnHold 描述
     */
    public static final String PREP_ON_HOLD_DESCRIPTION_FORMAT = "PrepWorkOrder: %s, Qty: %d";

    /**
     * 上架完成 PutAway status
     */
    public static final String PUT_AWAY_COMPLETED_STATUS = "PutAway Completed";

    /**
     * PrepPickingSlip BuildPickingSlip
     */
    public static final String PREP_BUILD_PICKING_SLIP_STATUS = "BuildPickingSlip";

    /**
     * PickingSlip Pick
     */
    public static final String PICK_STATUS = "Pick";

    /**
     * PickingSlip Picked
     */
    public static final String PICKED_DESCRIPTION = "All units Picked";

    /**
     * PickingSlip ReadyToShip
     */
    public static final String READY_TO_SHIP_DESCRIPTION = "All Package ReadyToShip";

    /**
     * PickingSlip Package
     */
    public static final String PACKAGE_STATUS = "Package";
}
