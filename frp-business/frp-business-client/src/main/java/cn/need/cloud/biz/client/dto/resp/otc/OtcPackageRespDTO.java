package cn.need.cloud.biz.client.dto.resp.otc;

import cn.need.cloud.biz.client.dto.req.base.BaseProductDTO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * OTC包裹 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC包裹 vo对象")
public class OtcPackageRespDTO extends OtcPackagePageRespDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 2L;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 构建运输策略
     */
    @Schema(description = "构建运输策略")
    private String buildShipStrategy;

    /**
     * 行号
     */
    @Schema(description = "行号")
    private Integer lineNum;

    /**
     * 是否为快递运输
     */
    @Schema(description = "是否为快递运输，isShipExpress")
    private Boolean shipExpressFlag;

    /**
     * 快递号
     */
    @Schema(description = "快递号")
    private String trackingNum;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    @Schema(description = "拣货单")
    private RefNumRespDTO otcPickingSlip;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 包裹状态
     */
    @Schema(description = "包裹状态")
    private String packageStatus;

    // ShipToAddress 字段

    /**
     * 收件人名称
     */
    @Schema(description = "收件人名称")
    private String shipToAddressName;

    /**
     * 收件公司
     */
    @Schema(description = "收件公司")
    private String shipToAddressCompany;

    /**
     * 收件国家
     */
    @Schema(description = "收件国家")
    private String shipToAddressCountry;

    /**
     * 收件州
     */
    @Schema(description = "收件州")
    private String shipToAddressState;

    /**
     * 收件城市
     */
    @Schema(description = "收件城市")
    private String shipToAddressCity;

    /**
     * 收件邮政编码
     */
    @Schema(description = "收件邮政编码")
    private String shipToAddressZipCode;

    /**
     * 收件地址1
     */
    @Schema(description = "收件地址1")
    private String shipToAddressAddr1;

    /**
     * 收件地址2
     */
    @Schema(description = "收件地址2")
    private String shipToAddressAddr2;

    /**
     * 收件地址3
     */
    @Schema(description = "收件地址3")
    private String shipToAddressAddr3;

    /**
     * 收件邮件信息
     */
    @Schema(description = "收件邮件信息")
    private String shipToAddressEmail;

    /**
     * 收件电话信息
     */
    @Schema(description = "收件电话信息")
    private String shipToAddressPhone;

    /**
     * 收件备注
     */
    @Schema(description = "收件备注")
    private String shipToAddressNote;

    // ShipFromAddress 字段

    /**
     * 发件人名称
     */
    @Schema(description = "发件人名称")
    private String shipFromAddressName;

    /**
     * 发件公司
     */
    @Schema(description = "发件公司")
    private String shipFromAddressCompany;

    /**
     * 发件国家
     */
    @Schema(description = "发件国家")
    private String shipFromAddressCountry;

    /**
     * 发件州
     */
    @Schema(description = "发件州")
    private String shipFromAddressState;

    /**
     * 发件城市
     */
    @Schema(description = "发件城市")
    private String shipFromAddressCity;

    /**
     * 发件邮政编码
     */
    @Schema(description = "发件邮政编码")
    private String shipFromAddressZipCode;

    /**
     * 发件地址1
     */
    @Schema(description = "发件地址1")
    private String shipFromAddressAddr1;

    /**
     * 发件地址2
     */
    @Schema(description = "发件地址2")
    private String shipFromAddressAddr2;

    /**
     * 发件地址3
     */
    @Schema(description = "发件地址3")
    private String shipFromAddressAddr3;

    /**
     * 发件邮件
     */
    @Schema(description = "发件邮件")
    private String shipFromAddressEmail;

    /**
     * 发件电话
     */
    @Schema(description = "发件电话")
    private String shipFromAddressPhone;

    /**
     * 发件备注
     */
    @Schema(description = "发件备注")
    private String shipFromAddressNote;

    /**
     * 打包详情集合
     */
    @Schema(description = "打包详情集合")
    private List<OtcPackageDetailRespDTO> detailList;

    /**
     * 打印标签集合
     */
    @Schema(description = "打印标签集合")
    private List<OtcPackageLabelRespDTO> labelList;

    /**
     * 流程类型
     */
    @Schema(description = "流程类型")
    private String processType;

    /**
     * 包裹类型
     */
    @Schema(description = "包裹类型")
    private String packageType;

    /**
     * 准备运输时间
     */
    @Schema(description = "准备运输时间")
    private LocalDateTime readyToShipTime;

    /**
     * 已运输时间
     */
    @Schema(description = "已运输时间")
    private LocalDateTime shippedTime;

    /**
     * 多盒包裹行号
     */
    @Schema(description = "多盒包裹行号")
    private Integer packageMultiboxLineNum;

    /**
     * 多盒包裹产品版本id
     */
    @Schema(description = "多盒包裹产品版本id")
    @JsonIgnore
    private Long packageMultiboxProductId;

    /**
     * 多盒包裹父产品
     */
    @Schema(description = "多盒包裹父产品")
    private BaseProductDTO packageMultiboxProduct;

    /**
     * 多盒包裹编码
     */
    @TableField("package_multibox_upc")
    private String packageMultiboxUpc;

    /**
     * 多盒包裹版本
     */
    @Schema(description = "多盒包裹版本")
    private Integer packageMultiboxVersionInt;

    /**
     * 运输箱子-长度单位
     */
    @Schema(description = "运输箱子-长度单位")
    private String shipSizeDimensionUnit;

    /**
     * 运输箱子-重量单位
     */
    @Schema(description = "运输箱子-重量单位")
    private String shipSizeWeightUnit;

    /**
     * 运输箱子-长
     */
    @Schema(description = "运输箱子-长")
    private BigDecimal shipSizeLength;

    /**
     * 运输箱子-宽
     */
    @Schema(description = "运输箱子-宽")
    private BigDecimal shipSizeWidth;

    /**
     * 运输箱子-高
     */
    @Schema(description = "运输箱子-高")
    private BigDecimal shipSizeHeight;

    /**
     * 运输箱子-重量
     */
    @Schema(description = "运输箱子-重量")
    private BigDecimal shipSizeWeight;
}