package cn.need.cloud.biz.client.dto.req.binlocation;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class BinLocationInfoDTO implements Serializable {
    /**
     * 参考编码
     */
    @Schema(description = "参考编码")
    private String refNum;

    /**
     * 库位名称
     */
    @Schema(description = "库位名称")
    private String locationName;

    /**
     * 产品id
     */
    @JsonIgnore
    private Long binLocationId;
}
