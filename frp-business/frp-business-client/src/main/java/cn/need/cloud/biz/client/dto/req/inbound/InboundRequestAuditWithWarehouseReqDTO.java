package cn.need.cloud.biz.client.dto.req.inbound;

import cn.need.cloud.biz.client.dto.req.base.BaseRequestWithWarehouseReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 入库请求审批 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema( description = "入库请求审批 vo对象")
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class InboundRequestAuditWithWarehouseReqDTO extends BaseRequestWithWarehouseReqDTO {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;


    /**
     * 预计到达日期
     */
    @Schema(description = "预计到达日期")
    private LocalDateTime estimateArrivalDate;

    /**
     * 审批状态 1：同意，0：拒绝
     */
    @Schema(description = "审批状态 1：同意，0：拒绝")
    private String auditResultType;

    /**
     * 审批备注
     */
    @Schema(description = "审批备注")
    private String note;

}
