package cn.need.cloud.biz.client.constant.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * Otc预拣货单类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PrepWorkOrderTypeEnum {

    /**
     * PrepPack
     */
    PREPPACK("PrepPack"),

    /**
     * PrepMultiBox
     */
    PREPMULTIBOX("PrepMultiBox"),

    /**
     * PrepConvert
     */
    PREPCONVERT("PrepConvert"),

    /**
     * PrepConvertPack
     */
    PREPCONVERTPACK("PrepConvertPack"),

    NONE("None"),

    /**
     * PrepConvertMultiBox
     */
    PREPCONVERTMULTIBOX("PrepConvertMultiBox");

    @EnumValue
    @JsonValue
    private final String status;

    /**
     * 需要两步操作
     */
    public static List<String> getTwoStep() {
        return List.of(PREPCONVERTPACK.status, PREPCONVERTMULTIBOX.status);
    }

    /**
     * 需要一步操作
     */
    public static List<String> getOneStep() {
        return List.of(PREPPACK.status, PREPMULTIBOX.status, PREPCONVERT.status);
    }
}

