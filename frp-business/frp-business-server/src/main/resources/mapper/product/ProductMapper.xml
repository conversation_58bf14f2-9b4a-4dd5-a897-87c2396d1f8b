<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.product.ProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.product.Product">
        <result column="id" property="id" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="assembly_product_flag" property="assemblyProductFlag" />
        <result column="assembly_version_ref_num" property="assemblyVersionRefNum" />
        <result column="deleted_note" property="deletedNote" />
        <result column="description" property="description" />
        <result column="group_type" property="groupType" />
        <result column="group_version_ref_num" property="groupVersionRefNum" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="multibox_flag" property="multiboxFlag" />
        <result column="multibox_version_ref_num" property="multiboxVersionRefNum" />
        <result column="note" property="note" />
        <result column="product_type" property="productType" />
        <result column="ref_num" property="refNum" />
        <result column="supplier_sku" property="supplierSku" />
        <result column="title" property="title" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="upc" property="upc" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.transaction_partner_id,
        t.ref_num,
        t.supplier_sku,
        t.upc,
        t.assembly_product_flag,
        t.note,
        t.description,
        t.title,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.multibox_flag,
        t.group_type,
        t.product_type,
        t.multibox_version_ref_num,
        t.group_version_ref_num,
        t.assembly_version_ref_num,
        t.hazmat_version_ref_num
    </sql>

    <select id="checkGlobalUniqueUpc"  resultType="java.lang.Integer">
        SELECT
            CASE
                WHEN NOT EXISTS (SELECT 1 FROM product WHERE upc = #{upc} AND transaction_partner_id=#{transactionPartnerId} AND tenant_id = #{tenantId} AND remove_flag = 0 )
                    AND NOT EXISTS (SELECT 1 FROM product_multibox WHERE upc = #{upc} AND transaction_partner_id=#{transactionPartnerId} AND tenant_id = #{tenantId} AND remove_flag = 0)
                    THEN 1
                ELSE 0
                END AS upc_unique
    </select>

    <select id="checkGlobalUniqueSku" resultType="java.lang.Integer">
        SELECT
            CASE
                WHEN NOT EXISTS (SELECT 1 FROM product WHERE supplier_sku = #{supplierSku} AND transaction_partner_id=#{transactionPartnerId} AND tenant_id = #{tenantId} AND remove_flag = 0 )
                    THEN 1
                ELSE 0
                END AS sku_unique
    </select>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.product.ProductVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            product t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <select id="checkInitGlobalUniqueUpc" resultType="cn.need.cloud.biz.model.vo.product.UpcCountVO">
        SELECT upc, COUNT(*) AS count
        FROM (
            SELECT upc FROM product t WHERE t.remove_flag = 0 And t.transaction_partner_id = #{transactionPartnerId} AND tenant_id = #{tenantId}
            UNION ALL
            SELECT upc FROM product_multibox m WHERE m.remove_flag = 0 AND m.transaction_partner_id = #{transactionPartnerId} AND tenant_id = #{tenantId}
            ) AS combined_upcs
        GROUP BY upc
        HAVING COUNT(*) > 1
    </select>


    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.transactionPartnerId != null">
            AND t.transaction_partner_id = #{qo.transactionPartnerId}
        </if>
        <if test="qo.transactionPartnerIdList != null and qo.transactionPartnerIdList.size > 0 ">
            AND t.transaction_partner_id in
            <foreach collection="qo.transactionPartnerIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.supplierSku != null and qo.supplierSku != ''">
            AND t.supplier_sku = #{qo.supplierSku}
        </if>
        <if test="qo.supplierSkuList != null and qo.supplierSkuList.size > 0 ">
            AND t.supplier_sku in
            <foreach collection="qo.supplierSkuList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.upc != null and qo.upc != ''">
            AND t.upc = #{qo.upc}
        </if>
        <if test="qo.upcList != null and qo.upcList.size > 0 ">
            AND t.upc in
            <foreach collection="qo.upcList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.assemblyProductFlag != null">
            AND t.assembly_product_flag = #{qo.assemblyProductFlag}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.multiboxFlag != null">
            AND t.multibox_flag = #{qo.multiboxFlag}
        </if>
        <if test="qo.groupType != null and qo.groupType != ''">
            AND t.group_type = #{qo.groupType}
        </if>
        <if test="qo.groupTypeList != null and qo.groupTypeList.size > 0 ">
            AND t.group_type in
            <foreach collection="qo.groupTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.productType != null and qo.productType != ''">
            AND t.product_type = #{qo.productType}
        </if>
        <if test="qo.productTypeList != null and qo.productTypeList.size > 0 ">
            AND t.product_type in
            <foreach collection="qo.productTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>