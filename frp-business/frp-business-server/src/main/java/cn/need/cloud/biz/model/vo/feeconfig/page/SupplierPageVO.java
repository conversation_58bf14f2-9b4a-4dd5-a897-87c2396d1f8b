package cn.need.cloud.biz.model.vo.feeconfig.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseTransactionPartnerAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 供应商信息 分页列表VO对象
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商信息 分页列表VO对象")
public class SupplierPageVO extends BaseSuperVO implements BaseTransactionPartnerAware {

    @Serial
    private static final long serialVersionUID = -6751521158760491034L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)")
    private Boolean activeFlag;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id")
    private Long transactionPartnerId;


}