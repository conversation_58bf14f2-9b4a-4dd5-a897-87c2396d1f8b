package cn.need.cloud.biz.service.otc.workorder;

import cn.need.cloud.biz.model.bo.otc.OtcPackageRollbackSingleWorkorderBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.param.base.WorkorderFinishUpdateParam;
import cn.need.cloud.biz.model.param.base.WorkorderStartUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelContext;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelParam;
import cn.need.cloud.biz.model.query.base.SplitWorkorderParam;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.pickingslip.PickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderFinishConfirmVO;

import java.util.List;

/**
 * <p>
 * OTC工单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcWorkorderSpecialService {

    /**
     * 拣货单触发
     *
     * @param pickingSlipIdList 拣货单
     */
    void cancelWithPickingSlip(List<Long> pickingSlipIdList);

    /**
     * Start Rollback
     *
     * @param query 启动参数
     * @return /
     */
    boolean startRollback(WorkorderStartUpdateParam query);

    /**
     * Finish Rollback
     *
     * @param query 完成参数
     * @return /
     */
    boolean finishRollback(WorkorderFinishUpdateParam query);

    /**
     * Rollback
     *
     * @param param 上架参数
     */
    void rollback(OtcPutawaySlipPutAwayBO param);

    /**
     * Rollback ReadyToShipQty
     *
     * @param rollbackList rollback参数
     */
    void rollbackByPackage(List<OtcPackageRollbackSingleWorkorderBO> rollbackList);

    /**
     * 获取工单Rollback列表
     *
     * @param query query
     * @return /
     */
    List<WorkorderConfirmDetailVO> confirmDetailList(WorkorderRollbackListQuery query);

    /**
     * Start Cancel
     *
     * @param query 启动参数
     */
    void startCancel(WorkorderStartUpdateParam query);

    /**
     * Finish Cancel
     *
     * @param query 完成参数
     * @return /
     */
    boolean finishCancel(WorkorderFinishUpdateParam query);

    /**
     * unpick列表
     *
     * @param workorderIds 工单id
     * @return /
     */
    List<PickingSlipUnpickDetailVO> unpickList(List<Long> workorderIds);

    /**
     * 完成上架单列表
     *
     * @param query query
     * @return /
     */
    WorkorderFinishConfirmVO finishConfirm(WorkorderRollbackListQuery query);

    /**
     * 拆单
     *
     * @param query 拆单参数
     * @return /
     */
    boolean split(List<SplitWorkorderParam> query);

    /**
     * 全部取消
     *
     * @param param 取消参数
     */
    List<OtcWorkorder> allCancel(OtcRequestCancelParam param);

    void cancelByRequest(OtcRequestCancelContext context);
}