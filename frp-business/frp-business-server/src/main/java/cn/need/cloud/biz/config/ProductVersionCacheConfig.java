package cn.need.cloud.biz.config;


import cn.need.cloud.biz.cache.ProductVersionCacheRepertory;
import cn.need.cloud.biz.cache.ProductVersionCacheService;
import cn.need.cloud.biz.cache.impl.RedisProductVersionCacheRepertoryImpl;
import cn.need.cloud.biz.cache.impl.RedisProductVersionCacheServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;


/**
 * 日志缓存配置
 *
 * <AUTHOR>
 */
@Configuration
public class ProductVersionCacheConfig {

    /**
     * 注入日志配置缓存操作工具
     */
    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public ProductVersionCacheService productVersionCacheService(RedisTemplate redisTemplate) {
        return new RedisProductVersionCacheServiceImpl(redisTemplate);
    }

    /**
     * 注入日志配置缓存操作工具
     */
    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public ProductVersionCacheRepertory productVersionCacheRepertory(RedisTemplate redisTemplate) {
        return new RedisProductVersionCacheRepertoryImpl(redisTemplate);
    }
}
