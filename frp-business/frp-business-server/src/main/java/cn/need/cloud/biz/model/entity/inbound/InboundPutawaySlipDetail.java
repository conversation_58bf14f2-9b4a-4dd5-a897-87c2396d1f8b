package cn.need.cloud.biz.model.entity.inbound;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * <p>
 * 上架详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inbound_putaway_slip_detail")
public class InboundPutawaySlipDetail extends SuperModel {


    @TableField("inbound_workorder_detail_id")
    private Long inboundWorkorderDetailId;

    @TableField("inbound_workorder_id")
    private Long inboundWorkorderId;

    @TableField("inbound_unload_id")
    private Long inboundUnloadId;

    @TableField("inbound_putaway_slip_id")
    private Long inboundPutawaySlipId;

    @TableField("pick_book_qty")
    private Integer pickBookQty;

    @TableField("pick_finish_qty")
    private Integer pickFinishQty;

    /**
     * 库位id
     */
    @TableField("bin_location_id")
    private Long binLocationId;

    /**
     * 库位详情id
     */
    @TableField("bin_location_detail_id")
    private Long binLocationDetailId;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 产品版本id
     */
    @TableField("product_version_id")
    private Long productVersionId;

}
