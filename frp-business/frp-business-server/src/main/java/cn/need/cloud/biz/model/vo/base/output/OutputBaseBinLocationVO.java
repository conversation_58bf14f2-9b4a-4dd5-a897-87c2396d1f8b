package cn.need.cloud.biz.model.vo.base.output;

import cn.need.cloud.biz.model.vo.base.BaseBinLocationVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 库位输出VO对象，用于封装库位信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(description = "Output库位 vo对象")
public class OutputBaseBinLocationVO implements Serializable, BaseBinLocationAware {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 库位ID
     */
    @Schema(description = "库位ID")
    private Long binLocationId;

    /**
     * 库位基本信息
     */
    @Schema(description = "库位基本信息")
    private BaseBinLocationVO baseBinLocationVO;

    /**
     * 通过库位ID构造对象
     *
     * @param binLocationId 库位ID
     */
    public OutputBaseBinLocationVO(Long binLocationId) {
        this.binLocationId = binLocationId;
    }

    /**
     * 获取库位基本信息
     * 如果baseBinLocationVO为空且binLocationId不为空，则通过接口默认实现获取
     *
     * @return 库位基本信息
     */
    @Override
    public BaseBinLocationVO getBaseBinLocationVO() {
        // 如果库位ID为空，直接返回null
        if (ObjectUtil.isEmpty(binLocationId)) {
            return null;
        }

        // 如果已有库位信息，直接返回
        if (ObjectUtil.isNotEmpty(baseBinLocationVO)) {
            return baseBinLocationVO;
        }

        // 通过接口默认实现获取库位信息并设置
        setBaseBinLocationVO(BaseBinLocationAware.super.getBaseBinLocationVO());
        return baseBinLocationVO;
    }

    /**
     * 重写toString方法，提供更友好的字符串表示
     *
     * @return 格式化的库位信息字符串
     */
    @Override
    public String toString() {
        BaseBinLocationVO locationVO = getBaseBinLocationVO();

        if (ObjectUtil.isEmpty(locationVO)) {
            return StringUtil.format(" BinLocation:{}", ObjectUtil.isEmpty(binLocationId) ? "null" : binLocationId);
        }

        return StringUtil.format(" BinLocation:{} ",
                ObjectUtil.nullToDefault(locationVO.getLocationName(), ""));
    }
}
