package cn.need.cloud.biz.service.warehouse.impl;

import cn.hutool.core.collection.CollUtil;
import cn.need.cloud.biz.cache.WarehouseCacheRepertory;
import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.mapper.warehouse.WarehouseOperationMapper;
import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.model.entity.warehouse.WarehouseOperation;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseOperationCreateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseOperationQuery;
import cn.need.cloud.biz.model.vo.page.WarehouseOperationPageVO;
import cn.need.cloud.biz.model.vo.page.WarehousePageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseDropVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseOperationRemoveVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseOperationVO;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.cloud.upms.cache.util.UserCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.map.Maps;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.dict.entity.UserCache;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 仓库分配 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Service
public class WarehouseOperationServiceImpl extends SuperServiceImpl<WarehouseOperationMapper, WarehouseOperation> implements cn.need.cloud.biz.service.warehouse.WarehouseOperationService {

    @Resource
    private WarehouseCacheRepertory warehouseCacheRepertory;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertByParam(WarehouseOperationCreateParam createParam) {
        // 检查传入仓库分配参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new IllegalArgumentException("Parameter cannot be empty");
        }

        // 判断仓库的状态，如果仓库为空或者被禁用，则抛出异常
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(createParam.getWarehouseId());
        if (ObjectUtil.isNull(warehouseCache) || !warehouseCache.getActiveFlag()) {
            throw new BusinessException("warehouse is null or disable");
        }

        //删除仓库操作人
        removeByWarehouseId(createParam.getWarehouseId());
        //插入仓库操作人关系
        super.insertBatch(buildWarehouse(createParam.getWarehouseId(), createParam.getOperationIdList()));
    }

    @Override
    public List<WarehouseOperationPageVO> listByQuery(WarehouseOperationQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<WarehouseOperationPageVO> pageByQuery(PageSearch<WarehouseOperationQuery> search) {
        //获取分页参数
        Page<WarehouseOperation> page = Conditions.page(search, entityClass);
        //获取分页列表
        List<WarehouseOperationPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        //获取操作人id
        Set<Long> operatorIdList = dataList.stream().map(WarehouseOperationPageVO::getOperatorId).collect(Collectors.toSet());
        //填充操作人名称
        Map<Long, String> operationNameMap = getOperationNameMap(operatorIdList);
        //填充操作人名称
        dataList.forEach(item -> item.setOperatorName(operationNameMap.get(item.getOperatorId())));
        //返回分页列表
        return new PageData<>(dataList, page);
    }

    @Override
    public List<WarehouseOperation> listByWarehouseId(Long id) {
        return super.list(Wrappers.<WarehouseOperation>lambdaQuery().eq(WarehouseOperation::getWarehouseId, id));
    }

    @Override
    public boolean exist(Long id) {
        return mapper.exists(Wrappers.<WarehouseOperation>lambdaQuery().eq(WarehouseOperation::getWarehouseId, id));
    }

    @Override
    public void removeByWarehouseId(Long warehouseId) {
        super.remove(Wrappers.<WarehouseOperation>lambdaQuery().eq(WarehouseOperation::getWarehouseId, warehouseId));
    }

    @Override
    public List<WarehouseDropVO> listByOperationId(Long id) {

        WarehouseContextHolder.setIgnore(Boolean.TRUE);
        //获取该操作人下仓库
        List<Warehouse> warehouseList = mapper.dropList(id);
        // 排序逻辑
        warehouseList.sort(Comparator.comparing(warehouse -> {
            boolean nameContainsLA03 = warehouse.getName().contains("LA03");
            boolean codeContainsLA03 = warehouse.getCode().contains("LA03");
            // 包含LA03的仓库返回0，其他返回1，确保包含LA03的排在前面
            return (nameContainsLA03 || codeContainsLA03) ? 0 : 1;
        }));
        //返回该操作人下仓库id
        return BeanUtil.copyNew(warehouseList, WarehouseDropVO.class);
    }

    @Override
    public List<WarehouseOperation> listByWarehouseIds(List<Long> warehouseIds) {
        return lambdaQuery().in(WarehouseOperation::getWarehouseId, warehouseIds).list();
    }

    @Override
    public void fillOperationInfo(List<WarehousePageVO> dataList) {
        //获取仓库id
        List<Long> warehouseIds = dataList.stream().map(WarehousePageVO::getId).toList();
        //获取操作人id
        List<WarehouseOperationVO> warehouseOperationList = BeanUtil.copyNew(listByWarehouseIds(warehouseIds), WarehouseOperationVO.class);
        //获取操作人id
        Set<Long> operatorIdList = warehouseOperationList.stream().map(WarehouseOperationVO::getOperatorId).collect(Collectors.toSet());
        //填充操作人名称
        Map<Long, String> operationNameMap = getOperationNameMap(operatorIdList);
        //填充操作人名称
        warehouseOperationList.forEach(item -> item.setOperatorName(operationNameMap.get(item.getOperatorId())));
        //按仓库id映射操作人信息
        Map<Long, List<WarehouseOperationVO>> warehouseOperationMap = ObjectUtil.toMapList(warehouseOperationList, WarehouseOperationVO::getWarehouseId);
        //填充操作人信息
        dataList.forEach(item -> item.setWarehouseOperationList(warehouseOperationMap.get(item.getId())));
    }

    @Override
    public void removeByOperationId(WarehouseOperationRemoveVO warehouseOperationRemoveVO) {
        lambdaUpdate()
                .eq(WarehouseOperation::getWarehouseId, warehouseOperationRemoveVO.getWarehouseId())
                .in(WarehouseOperation::getOperatorId, warehouseOperationRemoveVO.getOperationIdList())
                .set(WarehouseOperation::getRemoveFlag, 1)
                .update();
        //更新缓存
        removeRedis(warehouseOperationRemoveVO.getWarehouseId(), warehouseOperationRemoveVO.getOperationIdList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOperation(Long userTenantId, Long warehouseId) {
        //创建操作人仓库关系对象
        WarehouseOperation warehouseOperation = new WarehouseOperation();
        //填充仓库id
        warehouseOperation.setWarehouseId(warehouseId);
        //填充操作人id
        warehouseOperation.setOperatorId(userTenantId);
        //持久化数据库
        super.insert(warehouseOperation);
    }

    @Override
    public Map<Long, Set<Long>> getOperatorId(Collection<Long> warehouseIdList) {
        //判空校验
        if (ObjectUtil.isEmpty(warehouseIdList)) {
            return Maps.hashMap();
        }
        //获取操作人列表
        List<WarehouseOperation> operationList = listByWarehouseIds(CollUtil.newArrayList(warehouseIdList));
        //判空
        if (ObjectUtil.isEmpty(operationList)) {
            return Maps.hashMap();
        }
        //返回操作人id
        return operationList.stream()
                .collect(Collectors.groupingBy(WarehouseOperation::getWarehouseId, Collectors.mapping(WarehouseOperation::getOperatorId, Collectors.toSet())));
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 填充操作人名称
     * 该方法用于填充操作人名称，传入操作人vo对象集合
     *
     * @param warehouseOperationIdList 仓库操作人id
     * @return 根据操作人id映射操作人名称
     */
    private Map<Long, String> getOperationNameMap(Collection<Long> warehouseOperationIdList) {
        //判空
        if (ObjectUtil.isEmpty(warehouseOperationIdList)) {
            return Maps.hashMap();
        }
        //获取操作人id
        log.info("======================================operationId{}", warehouseOperationIdList);
        //根据用于id映射用户名称
        Map<Long, String> userNameMap = ObjectUtil.toMap(UserCacheUtil.listByIds(warehouseOperationIdList), UserCache::getId, UserCache::getName);
        log.info("======================================userNameMap{}", JsonUtil.toJson(userNameMap));
        //填充操作人名称
        return userNameMap;
    }

    /**
     * 构建仓库操作人关系列表
     * 该方法用于构建仓库操作人关系列表，传入操作人，仓库关系信息
     *
     * @param operatorIdList 操作人id
     * @param warehouseId    仓库id
     * @return 仓库操作人关系列表
     */
    private List<WarehouseOperation> buildWarehouse(Long warehouseId, Set<Long> operatorIdList) {
        //操作人对象集合
        List<WarehouseOperation> operationList = Lists.arrayList();
        operatorIdList.forEach(item -> {
            //操作人对象
            WarehouseOperation warehouseOperation = new WarehouseOperation();
            //填充仓库id
            warehouseOperation.setWarehouseId(warehouseId);
            //填充操作人id
            warehouseOperation.setOperatorId(item);
            operationList.add(warehouseOperation);
        });
        //更新缓存
        updateRedis(warehouseId, CollUtil.newHashSet(operatorIdList));
        //返回操作人对象
        return operationList;
    }

    /**
     * 更新仓库缓存
     * 该方法用于更新仓库缓存，传入仓库信息
     *
     * @param warehouseId 仓库id
     * @param operatorIds 操作人id集合
     */
    private void updateRedis(Long warehouseId, Set<Long> operatorIds) {
        //获取仓库缓存
        WarehouseCache cache = WarehouseCacheUtil.getById(warehouseId);
        //获取操作人id
        if (ObjectUtil.isEmpty(cache)) {
            return;
        }
        // //获取缓存操作人
        // Set<Long> operationIds = cache.getOperationIds();
        // if (ObjectUtil.isEmpty(operationIds)) {
        //     operationIds = CollUtil.newHashSet();
        // }
        // //增加操作人
        // operationIds.addAll(operatorIds);
        //填充操作人缓存
        cache.setOperationIds(operatorIds);
        //添加到缓存
        warehouseCacheRepertory.addWarehouse(cache);
    }

    /**
     * 更新仓库缓存
     * 该方法用于更新仓库缓存，传入仓库信息
     *
     * @param warehouseId 仓库id
     * @param operatorIds 操作人id集合
     */
    private void removeRedis(Long warehouseId, Set<Long> operatorIds) {
        //获取仓库缓存
        WarehouseCache cache = WarehouseCacheUtil.getById(warehouseId);
        //判空校验
        if (ObjectUtil.isEmpty(cache)) {
            return;
        }
        //获取操作人id
        Set<Long> operationIds = cache.getOperationIds();
        //判空校验
        if (ObjectUtil.isEmpty(operationIds)) {
            operationIds = CollUtil.newHashSet();
        }
        operationIds.removeAll(operatorIds);
        warehouseCacheRepertory.addWarehouse(cache);
    }

}
