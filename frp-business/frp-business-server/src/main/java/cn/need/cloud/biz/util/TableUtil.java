package cn.need.cloud.biz.util;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

/**
 * 获取实体表信息工具类
 *
 * <AUTHOR>
 * @since 2024/12/11
 */
public class TableUtil {
    /**
     * 获取当前表所有字段名称
     *
     * @param entityClass 表持久化实体
     * @return 字段名集合
     */
    public static List<String> getPropertyList(Class<?> entityClass) {
        Field[] fields = entityClass.getDeclaredFields();
        return Arrays.stream(fields)
                .map(item -> {
                    item.setAccessible(Boolean.TRUE);
                    return item.getName();
                })
                .toList();
    }
}
