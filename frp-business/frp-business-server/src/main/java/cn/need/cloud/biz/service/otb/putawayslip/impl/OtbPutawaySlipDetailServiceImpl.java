package cn.need.cloud.biz.service.otb.putawayslip.impl;

import cn.need.cloud.biz.mapper.otb.OtbPutawaySlipDetailMapper;
import cn.need.cloud.biz.model.entity.base.putawayslip.PutawaySlipDetailModel;
import cn.need.cloud.biz.model.entity.otb.OtbPutawaySlipDetail;
import cn.need.cloud.biz.service.otb.putawayslip.OtbPutawaySlipDetailService;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 上架详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Service
public class OtbPutawaySlipDetailServiceImpl extends SuperServiceImpl<OtbPutawaySlipDetailMapper, OtbPutawaySlipDetail>
        implements OtbPutawaySlipDetailService {

    @Override
    public List<OtbPutawaySlipDetail> listAvailableByWorkorderIds(Collection<Long> workorderIds) {
        return mapper.listAvailableByWorkorderIds(workorderIds);
    }

    @Override
    public Map<Long, List<OtbPutawaySlipDetail>> groupByPutawaySlipId(List<Long> headerIds) {
        if (ObjectUtil.isEmpty(headerIds)) {
            return Collections.emptyMap();
        }
        return lambdaQuery()
                .in(PutawaySlipDetailModel::getPutawaySlipId, headerIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(PutawaySlipDetailModel::getPutawaySlipId));
    }
}
