package cn.need.cloud.biz.model.query.inbound;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 入库工单卸货表 根据这个来生成上架单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库工单卸货表 根据这个来生成上架单 query对象")
public class InboundUnloadQuery extends SuperQuery {

    /**
     * 入库工单
     */
    @Schema(description = "入库工单")
    private Long inboundWorkorderId;

    /**
     * 入库工单详情
     */
    @Schema(description = "入库工单详情")
    private Long inboundWorkorderDetailId;

    /**
     * 上架单
     */
    @Schema(description = "上架单")
    private Long putawaySlipId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * palletQty
     */
    @Schema(description = "palletQty")
    private Integer palletQty;

    /**
     * palletPutawayQty
     */
    @Schema(description = "palletPutawayQty")
    private Integer palletPutawayQty;

    /**
     * regularPutawayQty
     */
    @Schema(description = "regularPutawayQty")
    private Integer regularPutawayQty;

    /**
     * 卸货状态
     */
    @Schema(description = "卸货状态")
    private String inboundUnloadStatus;

    /**
     * 卸货状态
     */
    @Schema(description = "卸货状态集合")
    @Condition(value = Keyword.IN, fields = {"inboundUnloadStatus"})
    private List<String> inboundUnloadStatusList;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 入库请求
     */
    @Schema(description = "入库请求")
    private Long requestId;


}