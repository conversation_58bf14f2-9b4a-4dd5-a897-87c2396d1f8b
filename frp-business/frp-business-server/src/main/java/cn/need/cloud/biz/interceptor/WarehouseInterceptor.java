package cn.need.cloud.biz.interceptor;

import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WarehouseInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        //获取仓库缓存
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(WarehouseContextHolder.getWarehouseId());
        if (ObjectUtil.isEmpty(warehouseCache)) {
            // 设置响应内容类型为 JSON
            response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
            // 设置 HTTP 状态码
            response.setStatus(HttpStatus.OK.value());
            // 将 JSON 数据写入响应体
            response.getWriter().write(JsonUtil.toJson(Result.fail("warehouse cache is empty")));
            // 中断请求处理
            return false;
        }
        //获取仓库操作人
        Set<Long> operationIds = warehouseCache.getOperationIds();
        Validate.isTrue(ObjectUtil.isNotEmpty(operationIds), "current operator don't have permission");
        Validate.isTrue(operationIds.contains(Objects.requireNonNull(Users.getUser()).getId()), "current operator don't have warehouse permission");
        Validate.isTrue(warehouseCache.getActiveFlag(), "The current warehouse has been disabled");
        return true;
    }
}
