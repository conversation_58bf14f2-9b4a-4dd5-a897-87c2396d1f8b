package cn.need.cloud.biz.converter.binlocation;

import cn.need.cloud.biz.client.dto.binlocation.BinLocationReserveDTO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationReserve;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationReserveVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 预留库位 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class BinLocationReserveConverter extends AbstractModelConverter<BinLocationReserve, BinLocationReserveVO, BinLocationReserveDTO> {

}
