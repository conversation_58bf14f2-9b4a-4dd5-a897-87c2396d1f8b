package cn.need.cloud.biz.model.vo.otc.pickingslip;

import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseFullProductVersionAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseProductAware;
import cn.need.framework.common.core.lang.ObjectUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTC预提货单详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTC预提货单详情 vo对象")
public class OtcPrepPickingSlipDetailVO implements Serializable, BaseProductAware, BaseBinLocationAware, BaseFullProductVersionAware {

    @Serial
    private static final long serialVersionUID = -7840401341724457055L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 上架数量
     */
    @Schema(description = "上架数量")
    private Integer putawayQty;

    /**
     * 已经分配的数量
     */
    @Schema(description = "已经分配的数量")
    private Integer allocateQty;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 待拣数量
     */
    @Schema(description = "待拣数量")
    public Integer getPickQty() {
        return ObjectUtil.nullToDefault(qty, 0) - ObjectUtil.nullToDefault(pickedQty, 0);
    }
}