package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbRequestDTO;
import cn.need.cloud.biz.model.entity.otb.OtbRequest;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB请求 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbRequestConverter extends AbstractModelConverter<OtbRequest, OtbRequestVO, OtbRequestDTO> {

}
