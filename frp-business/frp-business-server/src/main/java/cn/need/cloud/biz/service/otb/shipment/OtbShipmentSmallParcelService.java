package cn.need.cloud.biz.service.otb.shipment;

import cn.need.cloud.biz.model.param.otb.update.pallet.OtbBuildPalletParam;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbBuildPalletVO;

/**
 * <p>
 * OTB小件装运 service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface OtbShipmentSmallParcelService {
    /**
     * 构建大托
     *
     * @param param 构建参数
     * @return 打印信息
     */
    OtbBuildPalletVO createPallet(OtbBuildPalletParam param);

    /**
     * 小件发货完成
     *
     * @param id 发货单ID
     */
    void processed(Long id);
}
