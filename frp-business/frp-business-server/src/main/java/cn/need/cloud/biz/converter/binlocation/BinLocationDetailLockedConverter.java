package cn.need.cloud.biz.converter.binlocation;

import cn.need.cloud.biz.client.dto.binlocation.BinLocationDetailLockedDTO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailLockedVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 锁定 库位详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class BinLocationDetailLockedConverter extends AbstractModelConverter<BinLocationDetailLocked, BinLocationDetailLockedVO, BinLocationDetailLockedDTO> {

}
