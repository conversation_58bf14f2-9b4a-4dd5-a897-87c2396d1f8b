package cn.need.cloud.biz.service.product.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.product.ProductMultiboxDetailConverter;
import cn.need.cloud.biz.mapper.product.ProductMultiboxDetailMapper;
import cn.need.cloud.biz.model.entity.product.ProductMultiboxDetail;
import cn.need.cloud.biz.model.param.product.create.ProductMultiboxDetailCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductMultiboxCreateOrUpdateParam;
import cn.need.cloud.biz.model.param.product.update.ProductMultiboxDetailUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductMultiboxDetailQuery;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxDetailVO;
import cn.need.cloud.biz.model.vo.product.page.ProductMultiboxDetailPageVO;
import cn.need.cloud.biz.service.product.ProductMultiboxDetailService;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品多箱详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class ProductMultiboxDetailServiceImpl extends SuperServiceImpl<ProductMultiboxDetailMapper, ProductMultiboxDetail> implements ProductMultiboxDetailService {

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(ProductMultiboxDetailCreateParam createParam) {
        // 检查传入产品多箱详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取产品多箱详情转换器实例，用于将产品多箱详情参数对象转换为实体对象
        ProductMultiboxDetailConverter converter = Converters.get(ProductMultiboxDetailConverter.class);

        // 将产品多箱详情参数对象转换为实体对象并初始化
        ProductMultiboxDetail entity = initProductMultiboxDetail(converter.toEntity(createParam));

        // 插入产品多箱详情实体对象到数据库
        super.insert(entity);

        // 返回产品多箱详情ID
        return entity.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(ProductMultiboxDetailUpdateParam updateParam) {
        // 检查传入产品多箱详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取产品多箱详情转换器实例，用于将产品多箱详情参数对象转换为实体对象
        ProductMultiboxDetailConverter converter = Converters.get(ProductMultiboxDetailConverter.class);

        // 将产品多箱详情参数对象转换为实体对象
        ProductMultiboxDetail entity = converter.toEntity(updateParam);

        // 执行更新产品多箱详情操作
        return super.update(entity);

    }

    @Override
    public List<ProductMultiboxDetailPageVO> listByQuery(ProductMultiboxDetailQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<ProductMultiboxDetailPageVO> pageByQuery(PageSearch<ProductMultiboxDetailQuery> search) {
        Page<ProductMultiboxDetail> page = Conditions.page(search, entityClass);
        List<ProductMultiboxDetailPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public ProductMultiboxDetailVO detailById(Long id) {
        ProductMultiboxDetail entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "ProductMultiboxDetail", id));
        }
        return buildProductMultiboxDetailVO(entity);
    }


    @Override
    public void deleteByProductMultiboxIds(List<Long> productMultiboxIdList) {
        lambdaUpdate().set(ProductMultiboxDetail::getRemoveFlag, DataState.ENABLED)
                .in(ProductMultiboxDetail::getProductMultiboxId, productMultiboxIdList).update();
    }

    @Override
    public void insertBatchMultiboxDetailByMultiboxParam(Map<Long, ProductMultiboxCreateOrUpdateParam> map) {
        // 获取产品多箱详情转换器实例，用于将产品多箱详情参数对象转换为实体对象
        ProductMultiboxDetailConverter detailConverter = Converters.get(ProductMultiboxDetailConverter.class);
        //填充ProductMultiboxId
        List<ProductMultiboxDetail> detailEntityList = map.entrySet().stream().map(entry -> {
            List<ProductMultiboxDetail> entity = detailConverter.toEntity(entry.getValue().getDetailList());
            entity.forEach(detailEntity -> detailEntity.setProductMultiboxId(entry.getKey()));
            return entity;
        }).flatMap(Collection::stream).toList();
        super.insertBatch(detailEntityList);
    }

    @Override
    public List<ProductMultiboxDetail> getListByMultiboxId(Long multiboxId) {
        return lambdaQuery().eq(ProductMultiboxDetail::getProductMultiboxId, multiboxId).list();
    }

    @Override
    public Map<Long, List<ProductMultiboxDetail>> groupByMultiBoxIdList(List<Long> multiBoxIdList) {
        if (ObjectUtil.isEmpty(multiBoxIdList)) {
            return Collections.emptyMap();
        }
        List<ProductMultiboxDetail> detailList = mapper.findByBoxIds(multiBoxIdList);
        // 根据多箱id分组返回
        return detailList
                .stream()
                .collect(Collectors.groupingBy(ProductMultiboxDetail::getProductMultiboxId));
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////
    /**
     * 构建产品多箱详情VO对象
     *
     * @param entity 产品多箱详情对象
     * @return 返回包含详细信息的产品多箱详情VO对象
     */
    private ProductMultiboxDetailVO buildProductMultiboxDetailVO(ProductMultiboxDetail entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的产品多箱详情VO对象
        return Converters.get(ProductMultiboxDetailConverter.class).toVO(entity);
    }

    /**
     * 初始化产品多箱详情对象
     * 此方法用于设置产品多箱详情对象的必要参数，确保其处于有效状态
     *
     * @param entity 产品多箱详情对象，不应为空
     * @return 返回初始化后的产品多箱详情
     * @throws BusinessException 如果传入的产品多箱详情为空，则抛出此异常
     */
    private ProductMultiboxDetail initProductMultiboxDetail(ProductMultiboxDetail entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "ProductMultiboxDetail"));
        }


        // 返回初始化后的配置对象
        return entity;
    }
}
