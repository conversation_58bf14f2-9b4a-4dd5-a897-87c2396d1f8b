package cn.need.cloud.biz.model.vo.otc.pkg;

import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderDetailPickVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC包裹拣货详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC 包裹拣货对象")
public class OtcPackageDetailPickVO extends OtcWorkorderDetailPickVO {

    @Schema(description = "包裹id")
    private Long otcPackageId;

    /**
     * 工单详情id
     */
    @Schema(description = "工单详情id")
    private Long otcWorkorderDetailId;
}