package cn.need.cloud.biz.service.otc.pkg.impl;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPackageStatusEnum;
import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.otc.OtcPackageRollbackSingleWorkorderBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitDetailBO;
import cn.need.cloud.biz.model.entity.base.ProductModel;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.param.otc.update.pkg.OtcPackageBatchRollbackUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelContext;
import cn.need.cloud.biz.model.query.base.PackageRollbackListQuery;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.auditlog.BaseProductLogVO;
import cn.need.cloud.biz.model.vo.base.pkg.PackageConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.pkg.PackageConfirmVO;
import cn.need.cloud.biz.model.vo.base.pkg.PackageRollbackConfirmVO;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPackageAuditLogHelper;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipDetailService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipSpecialService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageBinLocationService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageDetailService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageSpecialService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderSpecialService;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC包裹特殊服务实现类
 * </p>
 * <p>
 * 该类负责处理OTC（Outbound To Customer）出库包裹的特殊业务逻辑，
 * 主要包括包裹状态变更、回滚、挂起等特殊操作。
 * 这些操作通常涉及与工单、拣货单等多个业务实体的交互。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor(onConstructor = @__(@Lazy))
public class OtcPackageSpecialServiceImpl implements OtcPackageSpecialService {

    // ======================================== 常量定义 ========================================

    private static final String OPERATION_FAILED_UPDATE_PACKAGE = "Update Package rollback is fail";
    private static final String OPERATION_FAILED_UPDATE_PACKAGE_DETAIL = "Update PackageDetail rollback is fail";
    private static final String OPERATION_FAILED_UPDATE_PACKAGE_BIN_LOCATION = "Update PackageBinLocation rollback is fail";
    private static final String OPERATION_FAILED_UPDATE_PACKAGE_STATUS = "Update Package status {} fail";
    private static final String OPERATION_FAILED_UPDATE_PACKAGE_SPLIT = "Update Package split is fail";

    // ======================================== 依赖注入 ========================================

    private final OtcWorkorderService otcWorkorderService;
    private final OtcWorkorderDetailService otcWorkorderDetailService;
    private final OtcWorkorderSpecialService otcWorkorderSpecialService;
    private final OtcPickingSlipSpecialService otcPickingSlipSpecialService;
    private final OtcPickingSlipDetailService otcPickingSlipDetailService;
    private final OtcPickingSlipService otcPickingSlipService;
    private final OtcPackageService otcPackageService;
    private final OtcPackageDetailService otcPackageDetailService;
    private final OtcPackageBinLocationService otcPackageBinLocationService;

    // ======================================== 公共方法 ========================================

    /**
     * 校验包裹是否可以回滚
     *
     * @param packageList 需要校验的包裹列表
     */
    private static void checkCanRollback(List<OtcPackage> packageList) {
        packageList.forEach(pkg -> {
            ProcessType.checkAbnormal(pkg.getProcessType(), pkg.refNumLog(), "rollback");

            Validate.isTrue(!Objects.equals(pkg.getPackageStatus(), OtcPackageStatusEnum.SHIPPED.getStatus()),
                    "{} is {} status can not rollback", pkg.refNumLog(), OtcPackageStatusEnum.SHIPPED.getStatus());
        });
    }

    @Override
    public void processTriggering(WorkorderProcessBO process) {
        List<Long> workorderIds = process.getIdList();

        if (ObjectUtil.isEmpty(workorderIds)) {
            return;
        }

        // 获取相关包裹并更新流程类型
        List<OtcPackage> packageList = otcPackageService.listByWorkOrderIdList(workorderIds);

        List<OtcPackageDetail> otcPackageDetails = otcPackageDetailService.listByPackageIds(packageList.stream().map(IdModel::getId).toList());

        process.setPackageDetailList(otcPackageDetails);

        process.setPackageList(packageList);

        updatePackageProcessType(packageList, process);

        // 记录审计日志
        recordProcessLog(packageList, process);
    }

    /**
     * 更新包裹流程类型
     *
     * @param packageList 包裹列表
     * @param process     流程参数
     */
    private void updatePackageProcessType(List<OtcPackage> packageList, WorkorderProcessBO process) {
        String type = process.getProcessType().getType();
        packageList.forEach(pkg -> pkg.setProcessType(type));

        Validate.isTrue(otcPackageService.updateBatch(packageList) == packageList.size(),
                OPERATION_FAILED_UPDATE_PACKAGE_STATUS, type);
    }

    /**
     * 记录流程日志
     *
     * @param packageList 包裹列表
     * @param process     流程参数
     */
    private void recordProcessLog(List<OtcPackage> packageList, WorkorderProcessBO process) {
        String type = process.getProcessType().getType();
        OtcPackageAuditLogHelper.recordLog(packageList, type, null, process.getNote(), BaseTypeLogEnum.PROCESS_TYPE.getType());
    }

    /**
     * 回滚单个工单关联的包裹
     * <p>
     * 此方法将指定工单关联的包裹状态回滚，并重置包裹明细中的已拣货数量。
     * 根据工单的配置决定回滚后的包裹状态（新建或取消）。
     * </p>
     *
     * @param workorder 需要回滚的工单
     * @param param     上架单上架更新参数，包含回滚说明
     *                  <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        TODO: 考虑添加事务支持，确保包裹状态和明细同时更新成功
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        TODO: 添加更详细的日志记录，包括回滚前的状态和数量信息
     */
    @Override
    public void rollback(OtcWorkorder workorder, OtcPutawaySlipPutAwayBO param) {
        List<OtcPackage> cancelList = otcPackageService.listByWorkOrderIdList(Collections.singletonList(workorder.getId()))
                .stream()
                .filter(obj -> OtcPackageStatusEnum.canCancelStatus().contains(obj.getPackageStatus()))
                .toList();
        // 用户指定包裹改New
        boolean shippingLabelFlag = ObjectUtil.nullToDefault(workorder.getRequestSnapshotProvideShippingLabelFlag(), false);
        cancelList.forEach(obj -> obj.setPackageStatus(shippingLabelFlag
                ? OtcPackageStatusEnum.NEW.getStatus() : OtcPackageStatusEnum.CANCELLED.getStatus())
        );

        Validate.isTrue(otcPackageService.updateBatch(cancelList) == cancelList.size(),
                "Failed to update package status fail"
        );

        // 包裹详情
        List<OtcPackageDetail> details = otcPackageDetailService.listByPackageIds(StreamUtils.distinctMap(cancelList, IdModel::getId));
        details.forEach(obj -> obj.setPickedQty(0));
        Validate.isTrue(otcPackageDetailService.updateBatch(details) == details.size(),
                "Failed to update package detail pickedQty"
        );

        // 日志
        cancelList.forEach(cancel -> {
            // 包裹 记录日志
            OtcPackageAuditLogHelper.recordLog(cancel, cancel.getPackageStatus(),
                    StringUtil.format("Rollback By {}", workorder.refNumLog()),
                    param.getNote(), BaseTypeLogEnum.PROCESS_TYPE.getType()
            );
        });
    }

    /**
     * 批量回滚包裹
     * <p>
     * 此方法处理多个包裹的批量回滚操作，包括：
     * 1. 更新关联工单的准备发货数量
     * 2. 更新拣货单的准备发货数量
     * 3. 更新包裹状态和关联数据
     * </p>
     *
     * @param query 包裹批量回滚参数，包含包裹ID列表和回滚说明
     * @return 回滚操作是否成功
     * <p>
     * TODO: 注释中提到但未实现的"rollbackWorkorderPackedQty"方法需要评估是否需要实现
     * TODO: 考虑添加回滚操作的结果验证，确保所有相关实体都正确更新
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchRollback(OtcPackageBatchRollbackUpdateParam query) {
        List<OtcPackage> rollbackPkgList = otcPackageService.listByIds(query.getIdList());

        Validate.notEmpty(rollbackPkgList, "Packages IdList {} cannot be empty", query.getIdList());

        // 构建Rollback参数
        List<OtcPackageRollbackSingleWorkorderBO> rollbackList = this.buildRollbackWithOwnerWorkorderList(rollbackPkgList);

        rollbackList.forEach(rollback -> rollback.setNote(query.getNote()));

        // 工单更新readyToShipQty、packedQty
        otcWorkorderSpecialService.rollbackByPackage(rollbackList);

        // 拣货单更新readyToShipQty
        otcPickingSlipSpecialService.rollbackByPackage(rollbackList);

        // 更新包裹
        this.rollbackAndUpdate(rollbackList);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCancel(OtcPackageBatchRollbackUpdateParam query) {
        return this.batchRollback(query);
    }

    @Override
    public void finishCancel(List<Long> workorderIdList) {
        var packageList = otcPackageService.listByWorkOrderIdList(workorderIdList);
        // 校验是否都是CANCELLED
        packageList.forEach(obj -> {
            Validate.isTrue(Objects.equals(obj.getPackageStatus(), OtcPackageStatusEnum.CANCELLED.getStatus()),
                    "{} is not Cancelled", obj.refNumLog()
            );
        });
    }

    @Override
    public PackageRollbackConfirmVO rollbackConfirm(PackageRollbackListQuery param) {
        // 获取工单下包裹
        List<OtcPackage> packageList = otcPackageService.listByWorkorderIdAndIds(param.getIdList(), param.getWorkorderId());
        Map<Long, OtcPackage> packageMap = StreamUtils.toMap(packageList, IdModel::getId);

        // 包裹详情
        List<Long> pkgIdList = StreamUtils.distinctMap(packageList, IdModel::getId);
        List<OtcPackageDetail> packageDetails = otcPackageDetailService.listByPackageIds(pkgIdList);

        // 封装返回结果
        List<PackageConfirmDetailVO> details = packageDetails.stream()
                .map(obj -> {
                    PackageConfirmDetailVO detail = BeanUtil.copyNew(obj, PackageConfirmDetailVO.class);
                    detail.setPkg(BeanUtil.copyNew(packageMap.get(obj.getOtcPackageId()), PackageConfirmVO.class));
                    return detail;
                })
                .toList();

        // 工单Rollback条件
        WorkorderRollbackListQuery query = new WorkorderRollbackListQuery();
        query.setIdList(StreamUtils.distinctMap(packageList, OtcPackage::getOtcWorkorderId));

        // 返回结果
        PackageRollbackConfirmVO rollback = new PackageRollbackConfirmVO();
        rollback.setDetailList(details);
        rollback.setWorkorderDetailList(otcWorkorderSpecialService.confirmDetailList(query));
        return rollback;
    }

    @Override
    public void split(List<OtcWorkorderSplitBO> splitHolders) {
        // 用户指定包裹的工单
        var hasShippingSplitWorkorderList = splitHolders.stream()
                .map(OtcWorkorderSplitBO::getWorkorder)
                .filter(obj -> ObjectUtil.nullToDefault(obj.getRequestSnapshotProvideShippingLabelFlag(), false))
                .toList();
        // 没有用户指定包裹
        if (ObjectUtil.isEmpty(hasShippingSplitWorkorderList)) {
            return;
        }

        var workorderIds = StreamUtils.distinctMap(hasShippingSplitWorkorderList, IdModel::getId);

        // 用户指定包裹
        var packageList = otcPackageService.listByWorkOrderIdList(workorderIds).stream()
                .filter(obj -> Objects.equals(obj.getPackageStatus(), OtcPackageStatusEnum.NEW.getStatus()))
                .toList();

        var hasShippingSplitHolders = splitHolders.stream()
                .filter(holder -> workorderIds.contains(holder.getWorkorder().getId()))
                .toList();

        // 包裹都被拣货了不能拆
        Validate.notEmpty(packageList, "Workorder: [{}] Cannot split orders, all packages are not new status",
                hasShippingSplitHolders.stream()
                        .map(OtcWorkorderSplitBO::getWorkorder)
                        .map(OtcWorkorder::getRefNum)
                        .collect(Collectors.joining(StringPool.COMMA))
        );

        var pkgIds = StreamUtils.distinctMap(packageList, IdModel::getId);
        var packageDetails = new ArrayList<>(otcPackageDetailService.listByPackageIds(pkgIds));

        // MultiBox
        this.dealWithMultiBox(packageList, packageDetails);

        var pkgsGroupMap = StreamUtils.groupBy(packageList, OtcPackage::getOtcWorkorderId);
        var pkgDetailsMap = StreamUtils.groupBy(packageDetails, OtcPackageDetail::getOtcPackageId);
        var moveList = hasShippingSplitHolders.stream()
                .flatMap(splitHolder -> {
                    // 拆单产品数量
                    var productSplitQtyMap = splitHolder.getDetailHolders().stream()
                            .collect(Collectors.groupingBy(obj -> obj.getDetail().getProductId(),
                                    Collectors.mapping(OtcWorkorderSplitDetailBO::getSplitDetail, Collectors.summingInt(ProductModel::getQty))
                            ));

                    // Step: 一个包裹需要拆到一个工单中
                    var workorder = splitHolder.getWorkorder();
                    var currentPkgList = pkgsGroupMap.getOrDefault(workorder.getId(), Collections.emptyList());
                    var currentMoveList = currentPkgList.stream()
                            // 只要拆单数量还有就继续过滤符合条件的包裹
                            .filter(pkg -> productSplitQtyMap.values().stream().anyMatch(obj -> obj > 0))
                            // 拆出来的单必须是包裹产品的整数倍
                            .filter(pkg -> {
                                var currentPkgDetails = pkgDetailsMap.get(pkg.getId());
                                // 包裹内所有产品都拆出来了
                                return currentPkgDetails.stream()
                                        .allMatch(obj -> productSplitQtyMap.containsKey(obj.getProductId())
                                                // 拆单数量足够
                                                && productSplitQtyMap.getOrDefault(obj.getProductId(), 0) >= obj.getQty());
                            })
                            // 扣除符合条件的包裹
                            .peek(pkg -> {
                                var currentPkgDetails = pkgDetailsMap.get(pkg.getId());
                                currentPkgDetails.forEach(obj ->
                                        // 扣掉当前的数量，后续
                                        productSplitQtyMap.put(obj.getProductId(), productSplitQtyMap.get(obj.getProductId()) - obj.getQty())
                                );

                                // 设置拆单后的工单、拣货单
                                var splitWorkorder = splitHolder.getSplitWorkorder();
                                pkg.setOtcWorkorderId(splitWorkorder.getId());
                                pkg.setOtcPickingSlipId(splitWorkorder.getOtcPickingSlipId());
                            })
                            .toList();

                    // productSplitQtyMap 需要全部扣除
                    Validate.isTrue(productSplitQtyMap.values().stream().allMatch(obj -> obj == 0),
                            "{} Cannot split the package, these products [{}] cannot be fully split",
                            workorder.refNumLog(), productSplitQtyMap.entrySet().stream()
                                    .filter(entry -> entry.getValue() > 0)
                                    .map(Map.Entry::getKey)
                                    .map(ProductCacheUtil::getById)
                                    .map(obj -> BeanUtil.copyNew(obj, BaseProductLogVO.class))
                                    .map(BaseProductLogVO::toLog)
                                    .collect(Collectors.joining(StringPool.COMMA))
                    );

                    // 没有符合的包裹进行拆单
                    Validate.notEmpty(currentMoveList, "{} Cannot split orders, all packages are not new status",
                            workorder.refNumLog()
                    );

                    return currentMoveList.stream();
                })
                .toList();

        Validate.isTrue(otcPackageService.updateBatch(moveList) == moveList.size(), "Update Package split is fail");
    }

    /**
     * @param context
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelByRequest(OtcRequestCancelContext context) {

        var workorderList = context.getWorkorderList();
        var workorderIds = StreamUtils.distinctMap(workorderList, IdModel::getId);

        var packageList = otcPackageService.listByWorkOrderIdList(workorderIds);
        context.setPackageList(packageList);

        for (OtcPackage pck : packageList) {
            pck.setPackageStatus(OtcPackageStatusEnum.CANCELLED.getStatus());
        }

        //日志
        OtcPackageAuditLogHelper.recordLog(packageList, "Start Cancel",
                BaseTypeLogEnum.PROCESS_TYPE.getType(),
                context.getNote(),
                StringUtil.format("Cancel By {}", context.getNote())
        );

        Validate.isTrue(otcPackageService.updateBatch(packageList) == packageList.size(), "Update Package cancel is fail");

    }

    /**
     * 处理MultiBox详情信息
     *
     * @param packageList    包裹
     * @param packageDetails 包裹详情
     */
    private void dealWithMultiBox(List<OtcPackage> packageList, List<OtcPackageDetail> packageDetails) {
        var multiBoxPkgList = packageList.stream()
                .filter(obj -> ObjectUtil.isNotEmpty(obj.getPackageMultiboxProductId()))
                .toList();

        var multiBoxPkgMap = StreamUtils.toMap(multiBoxPkgList, IdModel::getId);
        packageDetails.removeIf(obj -> multiBoxPkgMap.containsKey(obj.getOtcPackageId()));

        var multiBoxVirtualDetails = otcPackageService.findMultiBoxVirtualDetails(multiBoxPkgList);
        // 添加MultiBox虚拟详情参与逻辑拆单
        packageDetails.addAll(multiBoxVirtualDetails);
    }

    /**
     * 构建回滚参数
     * <p>
     * 根据需要回滚的包裹列表，构建按工单分组的回滚参数对象。
     * 此方法会收集回滚操作所需的所有相关数据，包括工单、工单明细、
     * 拣货单、拣货单明细、包裹、包裹明细和包裹库位信息。
     * </p>
     *
     * @param rollbackPkgList 需要回滚的包裹列表
     * @return 按工单分组的回滚参数对象列表
     * <p>
     * TODO: 数据收集过程复杂，考虑拆分为多个子方法以提高可维护性
     * TODO: 添加缓存机制减少重复查询，特别是对于共享同一工单或拣货单的包裹
     */
    @NotNull
    private List<OtcPackageRollbackSingleWorkorderBO> buildRollbackWithOwnerWorkorderList(List<OtcPackage> rollbackPkgList) {
        // 非Rollback 状态的包裹不能回滚
        checkCanRollback(rollbackPkgList);

        Map<Long, List<OtcPackage>> pkgGroupByWkMap = StreamUtils.groupBy(rollbackPkgList, OtcPackage::getOtcWorkorderId);
        // 包裹详情
        List<Long> pkgIdList = StreamUtils.distinctMap(rollbackPkgList, OtcPackage::getId);
        List<OtcPackageDetail> rollbackDetailList = otcPackageDetailService.listByPackageIds(pkgIdList);
        Map<Long, OtcPackage> packageMap = StreamUtils.toMap(rollbackPkgList, IdModel::getId);
        Map<Long, List<OtcPackageDetail>> pkgDetailsGroupByWkMap = rollbackDetailList.stream()
                .collect(Collectors.groupingBy(obj -> packageMap.get(obj.getOtcPackageId()).getOtcWorkorderId()));

        // 包裹拣货分配库位
        List<OtcPackageBinLocation> pkgBinLocationList = otcPackageBinLocationService.listByOtcPackageIdList(pkgIdList);
        Map<Long, List<OtcPackageBinLocation>> pkgBinGroupByWkMap = StreamUtils.groupBy(pkgBinLocationList, OtcPackageBinLocation::getOtcWorkorderId);

        // 工单
        List<OtcWorkorder> workorderList = otcWorkorderService.listByIds(StreamUtils.distinctMap(rollbackPkgList, OtcPackage::getOtcWorkorderId));
        List<Long> workorderIds = StreamUtils.distinctMap(workorderList, IdModel::getId);
        Map<Long, List<OtcWorkorderDetail>> wkDetailsMap = otcWorkorderDetailService.groupByOtcWorkOrderIdList(workorderIds);

        // 拣货单
        List<Long> pickingSlipIds = StreamUtils.distinctMap(workorderList, OtcWorkorder::getOtcPickingSlipId);
        Map<Long, OtcPickingSlip> pickingSlipMap = StreamUtils.toMap(otcPickingSlipService.listByIds(pickingSlipIds), IdModel::getId);

        // 拣货单详情
        List<Long> psDetailIds = StreamUtils.distinctMap(pkgBinLocationList, OtcPackageBinLocation::getOtcPickingSlipDetailId);
        Map<Long, List<OtcPickingSlipDetail>> psDetailsGroupByPsMap
                = StreamUtils.groupBy(otcPickingSlipDetailService.listByIds(psDetailIds), OtcPickingSlipDetail::getOtcPickingSlipId);

        // 按工单分组 Rollback
        return workorderList.stream()
                .map(wk -> {
                    OtcPackageRollbackSingleWorkorderBO rollback = new OtcPackageRollbackSingleWorkorderBO();
                    rollback.setWorkorder(wk);
                    rollback.setWorkorderDetailList(wkDetailsMap.getOrDefault(wk.getId(), Collections.emptyList()));
                    rollback.setPickingSlip(pickingSlipMap.get(wk.getOtcPickingSlipId()));
                    rollback.setPickingSlipDetailList(psDetailsGroupByPsMap.getOrDefault(wk.getOtcPickingSlipId(), Collections.emptyList()));
                    rollback.setPkgList(pkgGroupByWkMap.getOrDefault(wk.getId(), Collections.emptyList()));
                    rollback.setPkgDetailList(pkgDetailsGroupByWkMap.getOrDefault(wk.getId(), Collections.emptyList()));
                    rollback.setPkgBinLocationList(pkgBinGroupByWkMap.getOrDefault(wk.getId(), Collections.emptyList()));

                    return rollback;
                })
                .toList();
    }

    /**
     * 执行工单级别的回滚操作
     * <p>
     * 根据工单的配置，更新关联包裹的状态，并记录相应的审计日志。
     * 此方法处理单个工单关联的所有包裹的回滚逻辑。
     * </p>
     *
     * @param rollback 回滚工单参数对象
     *                 <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 TODO: 考虑提取状态设置逻辑到单独的方法中，提高代码复用性
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 TODO: 添加批量日志记录机制，减少循环中的重复操作
     */
    public void doRollbackOwnerWorkorder(OtcPackageRollbackSingleWorkorderBO rollback) {

        // 用户指定回滚到New，非用户指定回滚到Cancelled
        boolean shippingLabelFlag = ObjectUtil.nullToDefault(rollback.getWorkorder().getRequestSnapshotProvideShippingLabelFlag(), false);
        // Cancel直接取消掉
        var notCancelled = !Objects.equals(rollback.getWorkorder().getProcessType(), ProcessType.CANCELLING.getType())
                && shippingLabelFlag;
        for (OtcPackage pkg : rollback.getPkgList()) {
            // 仓库构建标记取消
            pkg.setPackageStatus(notCancelled
                    ? OtcPackageStatusEnum.NEW.getStatus()
                    : OtcPackageStatusEnum.CANCELLED.getStatus()
            );
            // 包裹 记录日志
            OtcPackageAuditLogHelper.recordLog(pkg, pkg.getPackageStatus(),
                    StringUtil.format("Rollback By {}", rollback.getWorkorder().refNumLog()),
                    rollback.getNote(), BaseTypeLogEnum.PROCESS_TYPE.getType()
            );
        }
    }

    /**
     * 执行回滚更新操作
     * <p>
     * 将回滚对象中的数据更新到数据库，包括：
     * 1. 包裹状态更新
     * 2. 包裹明细更新
     * 3. 移除包裹库位分配信息
     * </p>
     *
     * @param rollbackList 回滚对象列表
     *                     <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 TODO: 包裹明细的更新可能需要重置更多字段，当前只重置了已拣货数量
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 TODO: 考虑添加批量删除机制，而不是仅通过ID列表删除库位分配信息
     */
    private void rollbackAndUpdate(List<OtcPackageRollbackSingleWorkorderBO> rollbackList) {
        // 包裹 Rollback
        rollbackList.forEach(this::doRollbackOwnerWorkorder);

        // 包裹
        List<OtcPackage> packageList = rollbackList.stream()
                .flatMap(rollback -> rollback.getPkgList().stream())
                .toList();
        Validate.isTrue(otcPackageService.updateBatch(packageList) == packageList.size(),
                "Update Package rollback is fail"
        );
        List<OtcPackageDetail> packageDetailList = rollbackList.stream()
                .flatMap(rollback -> rollback.getPkgDetailList().stream())
                .toList();
        Validate.isTrue(otcPackageDetailService.updateBatch(packageDetailList) == packageDetailList.size(),
                "Update PackageDetail rollback is fail"
        );

        // 包裹拣货分配库位
        List<Long> packageBinLocationList = rollbackList.stream()
                .flatMap(rollback -> rollback.getPkgBinLocationList().stream())
                .map(IdModel::getId)
                .toList();
        Validate.isTrue(otcPackageBinLocationService.removeByIds(packageBinLocationList) == packageBinLocationList.size(),
                "Update PackageBinLocation rollback is fail"
        );

    }

}
