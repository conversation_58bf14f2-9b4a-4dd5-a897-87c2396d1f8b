package cn.need.cloud.biz.model.entity.otc;

import cn.need.cloud.biz.model.entity.base.PrepWorkorderModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * OTC预提工单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otc_prep_workorder")
public class OtcPrepWorkorder extends PrepWorkorderModel {


    @Serial
    private static final long serialVersionUID = -8873448622930319402L;
    /**
     * 预发货到c端工单id
     */
    @TableField("otc_workorder_id")
    private Long otcWorkorderId;

    /**
     * 预发货到c端工单详情id
     */
    @TableField("otc_workorder_detail_id")
    private Long otcWorkorderDetailId;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;

    /**
     * 预工单状态
     */
    @TableField("prep_workorder_status")
    private String prepWorkorderStatus;

    /**
     * 预工单类型
     */
    @TableField("prep_workorder_type")
    private String prepWorkorderType;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;

    /**
     * 产品类型
     */
    @TableField("detail_product_type")
    private String detailProductType;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @TableField("hazmat_version_ref_num")
    private String hazmatVersionRefNum;

    /**
     * 发货到c端预拣货id
     */
    @TableField("otc_prep_picking_slip_id")
    private Long otcPrepPickingSlipId;

    /**
     * 上架数量
     */
    @TableField("putaway_qty")
    private Integer putawayQty;

    /**
     * 库存预定id
     */
    @TableField("inventory_reserve_id")
    private Long inventoryReserveId;

    /**
     * 订单类型
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 拣货到那里
     */
    @TableField("pick_to_station")
    private String pickToStation;

    /**
     * 是否有特定运输要求
     */
    @TableField("has_cus_ship_require")
    private Boolean hasCusShipRequire;

    /**
     * 现场包装标志
     */
    @TableField("on_site_pack_flag")
    private Boolean onSitePackFlag;

    /**
     * 库位id
     */
    @TableField("bin_location_id")
    private Long binLocationId;

    /**
     * 锁定前
     */
    @TableField("locked_before")
    private String lockedBefore;

    /**
     * 预工单产品版本
     */
    @TableField("prep_workorder_version_int")
    private Integer prepWorkorderVersionInt;

    /**
     * 供应商id
     */
    @TableField("transaction_partner_id")
    private Long transactionPartnerId;

    /**
     * otb预工单产品类型
     */
    @TableField("prep_workorder_product_type")
    private String prepWorkorderProductType;
}
