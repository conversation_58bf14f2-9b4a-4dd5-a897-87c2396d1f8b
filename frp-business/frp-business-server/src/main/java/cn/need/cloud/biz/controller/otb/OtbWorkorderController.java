package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbWorkorderConverter;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorder;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkOrderBinLocationQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkOrderListQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderAdjustShipQtyQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.page.OtbWorkorderBinLocationPageVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderVO;
import cn.need.cloud.biz.model.vo.page.OtbWorkorderPageVO;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderBinLocationService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * OTB工单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-workorder")
@Tag(name = "OTB工单")
public class OtbWorkorderController extends AbstractRestController<OtbWorkorderService, OtbWorkorder, OtbWorkorderConverter, OtbWorkorderVO> {

    @Resource
    private OtbWorkorderBinLocationService otbWorkorderBinLocationService;

    @Operation(summary = "根据id获取OTB工单详情", description = "根据数据主键id，从数据库中获取其对应的OTB工单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbWorkorderVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTB工单详情
        OtbWorkorderVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTB工单详情", description = "根据数据RefNum，从数据库中获取其对应的OTB工单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbWorkorderVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTB工单详情
        OtbWorkorderVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "获取OTB工单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTB工单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbWorkorderPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbWorkOrderListQuery> search) {

        // 获取OTB工单分页
        PageData<OtbWorkorderPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "开始处理工单", description = "开始处理工单")
    @PostMapping(value = "/begin")
    public Result<Boolean> begin(@RequestBody @Parameter(description = "工单id集合", required = true) Set<Long> ids) {

        // 开始处理工单
        service.begin(ids);
        // 返回结果
        return success(Boolean.TRUE);
    }

    @Operation(summary = "count 数量", description = "根据传入的搜索条件参数，获取过滤构建拣货单数量")
    @PostMapping(value = "/count")
    public Result<Integer> count(@RequestBody @Parameter(description = "搜索条件参数", required = true) OtbWorkOrderListQuery query) {

        // 返回结果
        return success(service.filterBuildPickingSlipCount(query));
    }

    @Operation(summary = "adjustShipQty 调整发货数量", description = "根据传入的搜索条件参数，调整发货数量")
    @PostMapping(value = "/adjust-ship-qty")
    public Result<Boolean> adjustShipQty(@RequestBody @Valid @Parameter(description = "调整发货条件参数", required = true) OtbWorkorderAdjustShipQtyQuery query) {

        // 返回结果
        return success(service.adjustShipQty(query));
    }

    @Operation(summary = "下拉列表Pro", description = "下拉列表")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValuePro(@RequestBody @Parameter(description = "查询条件", required = true) OtbWorkorderQuery query) {

        return success(service.distinctValuePro(query));
    }

    @Operation(summary = "Dashboard CountPreDay", description = "下拉列表")
    @PostMapping(value = "/count-pre-day")
    public Result<List<DropProVO>> countPreDay(@RequestBody @Parameter(description = "查询条件", required = true) OtbWorkorderQuery query) {

        return success(service.countPreDay(query));
    }


    @Operation(summary = "获取OTB Prep工单仓储位置分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTB Prep工单仓储位置列表")
    @PostMapping(value = "/bin-location/list")
    public Result<PageData<OtbWorkorderBinLocationPageVO>> binLocationList(
            @Valid @RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbWorkOrderBinLocationQuery> search) {

        // 获取OTC工单仓储位置分页
        PageData<OtbWorkorderBinLocationPageVO> resultPage = otbWorkorderBinLocationService.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
