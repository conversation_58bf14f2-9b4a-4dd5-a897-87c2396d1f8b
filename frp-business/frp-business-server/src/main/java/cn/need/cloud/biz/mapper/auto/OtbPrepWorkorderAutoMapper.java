package cn.need.cloud.biz.mapper.auto;

import java.util.List;
import java.util.Map;

import cn.need.cloud.biz.model.bo.common.DropColumnInfoBO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder;
import cn.need.cloud.biz.model.query.auto.OtbPrepWorkorderAutoQuery;
import cn.need.cloud.biz.model.vo.page.OtbPrepWorkorderPageVO;
import org.apache.ibatis.annotations.Param;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
/**
 * <p>
 * OTB预提工单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-10
 */
@Mapper
public interface OtbPrepWorkorderAutoMapper extends SuperMapper<OtbPrepWorkorder> {

    /**
    *  根据条件获取OTB预提工单列表
    *
    * @param query 查询条件
    * @return OTB预提工单集合
    */
    default
        List<OtbPrepWorkorderPageVO> listByQuery (@Param("qoopw") OtbPrepWorkorderAutoQuery query){
        return listByQuery(query,null);
    }

    /**
      *  根据条件获取OTB预提工单分页列表
      *
      * @param query 查询条件
      * @param page 分页
      * @return OTB预提工单集合
      */
    List<OtbPrepWorkorderPageVO> listByQuery (
            @Param("qoopw") OtbPrepWorkorderAutoQuery query,
            @Param("page") Page <?> page);

    /**
     *  根据条件获取OTB预提工单列表
     *
     * @param query 查询条件
     * @return OTB预提工单集合
     */
    default
        List<OtbPrepWorkorderPageVO> listByQueryPro (@Param("qoopw") OtbPrepWorkorderAutoQuery query){
        return listByQuery(query,null);
    }

    /**
     *  根据条件获取OTB预提工单分页列表
     *
     * @param query 查询条件
     * @param page 分页
     * @return OTB预提工单集合
     */
    List<OtbPrepWorkorderPageVO> listByQueryPro (
            @Param("qoopw") OtbPrepWorkorderAutoQuery query,
            @Param("page") Page <?> page);

    /**
     * OTB预提工单下拉列表
     *
     * @param columnList 查询字段名
     * @param query 查询条件
     * @return OTB预提工单下拉列表
     */
    List<Map<String, Object>> dropProList(
        @Param("columnList") List<DropColumnInfoBO> columnList,
        @Param("qoopw") OtbPrepWorkorderAutoQuery query
    );
}