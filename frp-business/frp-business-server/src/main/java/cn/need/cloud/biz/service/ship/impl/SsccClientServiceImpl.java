package cn.need.cloud.biz.service.ship.impl;

import cn.need.cloud.biz.service.ship.SsccClientService;
import cn.need.cloud.ship.client.api.SSCCClient;
import cn.need.cloud.ship.client.dto.sscc.SsccRespDTO;
import cn.need.framework.common.core.exception.unchecked.FeignClientException;
import cn.need.framework.common.support.api.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * ship生成ssccNum service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Service
@Slf4j
public class SsccClientServiceImpl implements SsccClientService {
    @Resource
    private SSCCClient ssccClient;

    @Override
    public String getSsccNum(String refNum) {
        SsccRespDTO ssccResp = getSsccResp(refNum);
        return ssccResp.getSsccNum();
    }

    @Override
    public String getShortSsccNum(String refNum) {
        SsccRespDTO ssccResp = getSsccResp(refNum);
        return ssccResp.getShortSsccNum();
    }

    private SsccRespDTO getSsccResp(String refNum) {
        log.info("-------------->>>>>>> getSsccNum refNum={}", refNum);
        Result<SsccRespDTO> ssccNumResult = ssccClient.getNextSsccByRefNum(refNum);
        log.info("-------------->>>>>>> getSsccNum ssccNumResult={}", ssccNumResult);
        if (ssccNumResult.getCode() == 200) {
            return ssccNumResult.getData();
        }
        throw new FeignClientException(ssccNumResult.getCode(), ssccNumResult.getMessage());
    }
}
