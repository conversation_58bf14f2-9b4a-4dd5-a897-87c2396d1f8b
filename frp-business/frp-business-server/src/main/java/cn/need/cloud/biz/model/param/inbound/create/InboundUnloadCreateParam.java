package cn.need.cloud.biz.model.param.inbound.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 入库工单卸货表 根据这个来生成上架单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "入库工单卸货表 根据这个来生成上架单 vo对象")
public class InboundUnloadCreateParam implements Serializable {


    /**
     * 入库工单
     */
    @Schema(description = "入库工单")
    private Long inboundWorkorderId;

    /**
     * 入库工单详情
     */
    @Schema(description = "入库工单详情")
    private Long inboundWorkorderDetailId;

    /**
     * 上架单
     */
    @Schema(description = "上架单")
    private Long putawaySlipId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * palletQty
     */
    @Schema(description = "palletQty")
    private Integer palletQty;

    /**
     * palletPutawayQty
     */
    @Schema(description = "palletPutawayQty")
    private Integer palletPutawayQty;

    /**
     * regularPutawayQty
     */
    @Schema(description = "regularPutawayQty")
    private Integer regularPutawayQty;

    /**
     * 卸货状态
     */
    @Schema(description = "卸货状态")
    private String inboundUnloadStatus;


    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 入库请求
     */
    @Schema(description = "入库请求")
    private Long requestId;

}