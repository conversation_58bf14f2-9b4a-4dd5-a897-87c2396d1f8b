package cn.need.cloud.biz.model.query.inbound;

import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;
import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;


/**
 * 入库单打托 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库单打托 query对象")
public class InboundPalletQuery extends SuperQuery {

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private Set<String> refNumList;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 库位名称
     */
    @Schema(description = "库位名称")
    private String locationName;

    /**
     * 库位名称
     */
    @Schema(description = "库位名称")
    private Set<String> locationNameList;

    /**
     * 入库工单请求refNum快照
     */
    @Schema(description = "入库工单refNum")
    private String workOrderOfRefNum;

    /**
     * 入库工单refNum
     */
    @Schema(description = "入库工单refNum")
    private Set<String> workOrderOfRefNumList;

    /**
     * 入库请求单refNum
     */
    @Schema(description = "入库请求单refNum")
    private String requestOfRefNum;

    /**
     * 入库请求单refNum
     */
    @Schema(description = "入库请求单refNum")
    private Set<String> requestOfRefNumList;

    /**
     * 入库请求单请求refNum
     */
    @Schema(description = "入库请求单请求refNum")
    private String requestOfRequestRefNum;

    /**
     * 入库请求单请求refNum
     */
    @Schema(description = "入库请求单请求refNum")
    private Set<String> requestOfRequestRefNumList;

    /**
     * 入库请求单物流跟踪号
     */
    @Schema(description = "入库请求单物流跟踪号")
    private String requestTrackingNum;

    /**
     * 入库请求单物流跟踪号
     */
    @Schema(description = "入库请求单物流跟踪号")
    private Set<String> requestTrackingNumList;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 打托状态
     */
    @Schema(description = "打托状态")
    private String palletStatus;

    /**
     * 打托状态
     */
    @Schema(description = "打托状态集合")
    @Condition(value = Keyword.IN, fields = {"palletStatus"})
    private List<String> palletStatusList;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态集合")
    @Condition(value = Keyword.IN, fields = {"printStatus"})
    private List<String> printStatusList;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 入库工单id
     */
    private Set<Long> inboundWorkOrderIdList;

    /**
     * 库位id
     */
    private Set<Long> binLocationIdList;

    /**
     * 入库工单id
     */
    @Schema(description = "入库工单id")
    private Long inboundWorkOrderId;

    @Schema(description = "入库工单 query对象")
    private InboundWorkorderQuery inboundWorkorderQuery;

    @Schema(description = "库位 query对象")
    private BinLocationQuery binLocationQuery;
}