package cn.need.cloud.biz.model.vo.otb.pkg;

import cn.need.cloud.biz.model.vo.base.BaseFullProductVO;
import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * OTB包裹详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB包裹详情 vo对象")
public class OtbPackageDetailVO extends BaseSuperVO {


    @Serial
    private static final long serialVersionUID = 7713820425288131146L;
    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    /**
     * 产品基础信息
     */
    @Schema(description = "产品基础信息")
    private BaseFullProductVO baseFullProductVO;

    /**
     * otb包裹id
     */
    @Schema(description = "otb包裹id")
    private Long otbPackageId;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @Schema(description = "渠道要求的需要贴的产品标识码")
    private String productBarcode;

    /**
     * 渠道要求的需要贴的sku
     */
    @Schema(description = "渠道要求的需要贴的产品sku")
    private String productChannelSku;

}