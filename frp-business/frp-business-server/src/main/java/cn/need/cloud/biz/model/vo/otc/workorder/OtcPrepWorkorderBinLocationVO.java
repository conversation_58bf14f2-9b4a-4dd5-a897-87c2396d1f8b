package cn.need.cloud.biz.model.vo.otc.workorder;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * OTC预提工单仓储位置 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC预提工单仓储位置 vo对象")
public class OtcPrepWorkorderBinLocationVO extends BaseSuperVO {


    @Serial
    private static final long serialVersionUID = -6942288888013246706L;
    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long otcWorkorderId;

    /**
     * 工单详情id
     */
    @Schema(description = "工单详情id")
    private Long otcWorkorderDetailId;

    /**
     * 预工单id
     */
    @Schema(description = "预工单id")
    private Long otcPrepWorkorderId;

    /**
     * 预工单详情id
     */
    @Schema(description = "预工单详情id")
    private Long otcPrepWorkorderDetailId;

    /**
     * 预拣货id
     */
    @Schema(description = "预拣货id")
    private Long otcPrepPickingSlipId;

    /**
     * 预拣货详情id
     */
    @Schema(description = "预拣货详情id")
    private Long otcPrepPickingSlipDetailId;

    /**
     * 库位详情id
     */
    @Schema(description = "库位详情id")
    private Long binLocationDetailId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

}