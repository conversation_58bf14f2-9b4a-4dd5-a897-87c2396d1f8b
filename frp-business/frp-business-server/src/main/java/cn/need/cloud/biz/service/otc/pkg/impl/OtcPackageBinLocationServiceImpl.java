package cn.need.cloud.biz.service.otc.pkg.impl;

import cn.need.cloud.biz.mapper.otc.OtcPackageBinLocationMapper;
import cn.need.cloud.biz.model.entity.otc.OtcPackageBinLocation;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageBinLocationQuery;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.otc.page.OtcPackageBinLocationPageVO;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageBinLocationService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC包裹库位服务实现类
 * </p>
 * <p>
 * 该类负责处理OTC（Outbound To Customer）出库包裹与库位关联的业务逻辑，
 * 包括包裹库位信息的查询、关联填充等功能。包裹库位记录了包裹中商品的具体存放位置，
 * 是物流管理的重要组成部分。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 分页查询包裹库位信息
 * 2. 根据包裹ID查询关联库位
 * 3. 关联数据的填充与处理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcPackageBinLocationServiceImpl extends SuperServiceImpl<OtcPackageBinLocationMapper, OtcPackageBinLocation> implements OtcPackageBinLocationService {

    /**
     * 工单服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private OtcWorkorderService otcWorkorderService;

    /**
     * 拣货单服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private OtcPickingSlipService otcPickingSlipService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * 分页查询包裹库位信息
     * <p>
     * 该方法根据查询条件和分页参数获取OTC包裹库位列表，并填充关联的工单和拣货单信息。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的OTC包裹库位分页视图对象
     * <p>
     * TODO: 方法名与返回值描述不匹配，实际返回的是分页对象而非列表
     * 优化建议：将方法名更改为pageByQuery以与实际功能匹配
     */
    @Override
    public PageData<OtcPackageBinLocationPageVO> pageByQuery(PageSearch<OtcPackageBinLocationQuery> search) {
        Page<OtcPackageBinLocation> page = Conditions.page(search, entityClass);
        // 查询列表
        List<OtcPackageBinLocationPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        // 产品填充
        fillField(dataList);
        return new PageData<>(dataList, page);
    }

    /**
     * 根据包裹ID列表获取库位信息
     * <p>
     * 该方法查询指定包裹ID列表下的所有库位记录。
     * 如果ID列表为空，则返回空列表。
     * </p>
     *
     * @param packageIdList 包裹ID列表
     * @return A相关的包裹库位列表
     * <p>
     * TODO: 查询结果没有经过排序，可能影响下游处理
     * 优化建议：考虑添加orderBy子句，例如按创建时间或其他关键字段排序
     */
    @Override
    public List<OtcPackageBinLocation> listByOtcPackageIdList(List<Long> packageIdList) {
        if (ObjectUtil.isEmpty(packageIdList)) {
            return Collections.emptyList();
        }

        return lambdaQuery().in(OtcPackageBinLocation::getOtcPackageId, packageIdList).list();
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 填充包裹库位视图对象的关联信息
     * <p>
     * 该方法为包裹库位视图对象列表填充关联的工单和拣货单信息，
     * 通过批量查询的方式提高性能。
     * </p>
     *
     * @param dataList 需要填充的包裹库位视图对象列表
     *                 <p>
     *                                                                                                                 TODO: 方法名不够准确，实际是在填充关联信息而非产品信息
     *                                                                                                                 优化建议：将方法名更改为fillAssociatedInfo或fillRelationData以更准确地反映其功能
     */
    private void fillField(List<OtcPackageBinLocationPageVO> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }
        // 工单
        Map<Long, RefNumVO> workOrderMap = otcWorkorderService.refNumMapByIds(StreamUtils.distinctMap(dataList, OtcPackageBinLocationPageVO::getOtcWorkorderId));
        // 拣货单
        Map<Long, RefNumVO> psMap = otcPickingSlipService.refNumMapByIds(StreamUtils.distinctMap(dataList, OtcPackageBinLocationPageVO::getOtcPickingSlipId));

        dataList.forEach(obj -> {
            // 工单填充
            obj.setOtcWorkOrder(workOrderMap.get(obj.getOtcWorkorderId()));
            // 拣货单填充
            obj.setOtcPickingSlip(psMap.get(obj.getOtcPickingSlipId()));
        });
    }

}
