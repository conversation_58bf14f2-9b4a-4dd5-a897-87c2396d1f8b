package cn.need.cloud.biz.service.otc.pkg;

import cn.need.cloud.biz.client.dto.req.otc.OtcPackageDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPackage;
import cn.need.cloud.biz.model.entity.otc.OtcPackageDetail;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcMultiBoxPackageWarehouseCreateParam;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcPackageBatchCreateParam;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcPackageWarehouseCreateParam;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcSompPackageWarehouseCreateParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageListQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageShippedQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageTrackingNumQuery;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageByTrackingNumVO;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageFullOutputVO;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderDetailPickVO;
import cn.need.cloud.biz.model.vo.page.OtcPackagePageVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * OTC包裹 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcPackageService extends
        RefNumService<OtcPackage, OtcPackageService>,
        SuperService<OtcPackage> {

    /**
     * 批量新增
     *
     * @param createParamList 入参
     */
    void batchInsertByParam(List<OtcPackageBatchCreateParam> createParamList);

    /**
     * 根据查询条件获取OTC包裹列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC包裹对象的列表(分页)
     */
    List<OtcPackagePageVO> listByQuery(OtcPackageListQuery query);

    /**
     * 根据查询条件获取OTC包裹列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC包裹对象的列表(分页)
     */
    PageData<OtcPackagePageVO> pageByQuery(PageSearch<OtcPackageListQuery> search);

    /**
     * 根据ID获取OTC包裹
     *
     * @param id OTC包裹ID
     * @return 返回OTC包裹VO对象
     */
    OtcPackageVO detailById(Long id);

    List<OtcPackageVO> listDetailByRequestId(Long requestId);

    /**
     * 根据OTC包裹唯一编码获取OTC包裹
     *
     * @param refNum OTC包裹唯一编码
     * @return 返回OTC包裹VO对象
     */
    OtcPackageVO detailByRefNum(String refNum);

    /**
     * 发货
     *
     * @param query 发货查询
     * @return 成功/否
     */
    Boolean markSipped(OtcPackageShippedQuery query);

    /**
     * 根据拣货单ID获取包裹列表
     *
     * @param id 拣货单ID
     * @return 包裹列表
     */
    List<OtcPackage> listByPickingSlipId(Long id);

    /**
     * 仓库人员创建包裹
     *
     * @param warehouseCreateParam 仓库人员创建构建参数
     * @return 创建返回vo
     */
    OtcPackageFullOutputVO buildByWarehouse(OtcPackageWarehouseCreateParam warehouseCreateParam);

    /**
     * 仓库人员创建包裹
     *
     * @param warehouseCreateParam 仓库人员创建构建参数
     * @return 创建返回vo
     */
    OtcPackageFullOutputVO multiBoxBuildByWarehouse(OtcMultiBoxPackageWarehouseCreateParam warehouseCreateParam);

    /**
     * 仓库人员创建包裹
     *
     * @param sompWarehouseCreateParam 仓库人员创建构建参数
     * @return 创建返回vo
     */
    OtcPackageFullOutputVO sompBuildByWarehouse(OtcSompPackageWarehouseCreateParam sompWarehouseCreateParam);

    /**
     * 根据搜索条件进行markShipped操作
     *
     * @param query 搜索条件
     * @return 成功
     */
    boolean filterMarkShipped(OtcPackageListQuery query);

    /**
     * 标记打印
     *
     * @param query MarkPrinted查询条件
     * @return 成功
     */
    boolean labelMarkPrinted(PrintQuery query);

    /**
     * 根据工单id集合获取包裹集合
     *
     * @param workOrderIdList 工单id集合
     * @return 包裹集合
     */
    List<OtcPackage> listByWorkOrderIdList(List<Long> workOrderIdList);

    /**
     * 根据工单拣货信息进行拣货
     *
     * @param pickList 工单拣货信息
     */
    List<OtcPackage> pick(List<OtcWorkorderDetailPickVO> pickList);

    /**
     * 根据trackingNum查询包裹详情
     *
     * @param query trackingNum查寻
     * @return /
     */
    List<OtcPackageByTrackingNumVO> detailByTrackingNum(OtcPackageTrackingNumQuery query);

    /**
     * 根据trackingNum查询包裹详情
     *
     * @param pkg 包裹
     * @return /
     */
    List<OtcPackage> findIsAllBuildOwnerLineWorkorderPkgList(OtcPackage pkg);

    /**
     * 获取处理过MultiBox后的Detail
     *
     * @param pkg pkg
     * @return /
     */
    List<OtcPackageDetail> afterProcessingMultiBox(OtcPackage pkg);

    /**
     * 自定义分页获取数据
     *
     * @param query 条件
     * @param page  分页
     * @return /
     */
    List<OtcPackagePageVO> pageByQuery(OtcPackageListQuery query, int page, int size);

    /**
     * 拣货单获取包裹集合
     *
     * @param pickingSlipIds 拣货单
     * @return /
     */
    List<OtcPackage> listByPickingSlipIdList(List<Long> pickingSlipIds);

    /**
     * 根据工单和包裹id获取
     *
     * @param idList      包裹
     * @param workorderId 工单
     * @return /
     */
    List<OtcPackage> listByWorkorderIdAndIds(List<Long> idList, Long workorderId);

    /**
     * 获取当前包裹是否全部构建完成
     *
     * @param packageList 需要构建的包裹
     * @return /
     */
    List<OtcPackageDetail> findShippedPkgDetails(List<OtcPackage> packageList);

    /**
     * 获取MultiBox虚拟包裹详情
     *
     * @param multiBoxPkgList multiBoxPkgList
     * @return /
     */
    List<OtcPackageDetail> findMultiBoxVirtualDetails(List<OtcPackage> multiBoxPkgList);

    /**
     * 填充包裹id
     *
     * @param otcPackageDetailDTO 包裹详情信息
     */
    void fillPackageId(OtcPackageDetailDTO otcPackageDetailDTO);
}