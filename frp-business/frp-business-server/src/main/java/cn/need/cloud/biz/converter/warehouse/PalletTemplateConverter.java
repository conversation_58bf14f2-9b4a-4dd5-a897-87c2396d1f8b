package cn.need.cloud.biz.converter.warehouse;

import cn.need.cloud.biz.client.dto.PalletTemplateDTO;
import cn.need.cloud.biz.model.entity.warehouse.PalletTemplate;
import cn.need.cloud.biz.model.vo.warehouse.PalletTemplateVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 打托模板 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class PalletTemplateConverter extends AbstractModelConverter<PalletTemplate, PalletTemplateVO, PalletTemplateDTO> {

}
