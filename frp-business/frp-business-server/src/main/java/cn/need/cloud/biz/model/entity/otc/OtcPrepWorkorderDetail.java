package cn.need.cloud.biz.model.entity.otc;

import cn.need.cloud.biz.model.entity.base.PrepWorkorderDetailModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * OTC预提工单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otc_prep_workorder_detail")
public class OtcPrepWorkorderDetail extends PrepWorkorderDetailModel {


    @Serial
    private static final long serialVersionUID = 601183669077407094L;
    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @TableField("hazmat_version_ref_num")
    private String hazmatVersionRefNum;

    /**
     * 发货到c端预工单id
     */
    @TableField("otc_prep_workorder_id")
    private Long otcPrepWorkorderId;

    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;

    /**
     * 库存锁定id
     */
    @TableField("inventory_locked_id")
    private Long inventoryLockedId;

    /**
     * 拣货数量
     */
    @TableField("picked_qty")
    private Integer pickedQty;

    /**
     * 上架数量
     */
    @TableField("putaway_qty")
    private Integer putawayQty;

    /**
     * 预工单详情产品版本
     */
    @TableField("prep_workorder_detail_version_int")
    private Integer prepWorkorderDetailVersionInt;

    /**
     * 预工单详情类型
     */
    @TableField("prep_workorder_detail_type")
    private String prepWorkorderDetailType;

    /**
     * 父节点id
     */
    @TableField("parent_id")
    private Long parentId;

}
