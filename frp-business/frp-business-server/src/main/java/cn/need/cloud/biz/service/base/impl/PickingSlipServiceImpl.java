package cn.need.cloud.biz.service.base.impl;

import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.PickFromType;
import cn.need.cloud.biz.client.constant.enums.base.PrepWordorderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinLocationLogEnum;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinTypeEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductGroupTypeEnum;
import cn.need.cloud.biz.model.bo.base.BaseModelInventoryReserveBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailBatchCreateBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailChangeBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailLockedBatchCreateBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailLockedChangeBO;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.bo.common.WorkOrderMembershipBO;
import cn.need.cloud.biz.model.entity.base.BaseWorkorderBinLocationModel;
import cn.need.cloud.biz.model.entity.base.ProductModel;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.product.ProductGroup;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.query.base.BaseBinLocationQuery;
import cn.need.cloud.biz.model.query.product.ProductTreeQuery;
import cn.need.cloud.biz.model.vo.base.*;
import cn.need.cloud.biz.model.vo.base.product.PrepComponentGroupVO;
import cn.need.cloud.biz.model.vo.base.product.PrepFullProductVO;
import cn.need.cloud.biz.model.vo.base.product.PrepGroupVO;
import cn.need.cloud.biz.model.vo.base.product.PrepMultiBoxGroupVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderDetailPutAwayVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkOrderNoEnoughAvailQtyVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.inventory.InventoryReserveService;
import cn.need.cloud.biz.service.product.ProductGroupService;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.cloud.biz.util.AllocationUtil;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.model.SuperModel;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 拣货单服务实现类
 * </p>
 * <p>
 * 该类实现了拣货单的核心业务逻辑，负责拣货单的处理、库存管理和相关操作。
 * 主要功能包括：
 * 1. 库存移动到ReadyToGo区域，包括OTC和OTB的库存移动
 * 2. 库存锁定和释放管理，包括工单库位锁定和预留锁定
 * 3. 拣货类型判断，如普通拣货、预处理拣货等
 * 4. 库存不足判断和处理，包括查找库存不足的工单和产品
 * 5. 预处理产品结构管理，包括多箱产品、组件产品等
 * </p>
 * <p>
 * 该类在拣货流程中起到关键作用，处理库存移动、锁定和释放等操作，
 * 确保拣货过程中的库存准确性和一致性。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-09
 */
@Service
@AllArgsConstructor
public class PickingSlipServiceImpl implements PickingSlipService {

    /**
     * 产品服务，用于获取产品信息和产品结构
     */
    private final ProductService productService;

    /**
     * 产品组服务，用于管理产品组关系，如多箱产品、组件产品等
     */
    private final ProductGroupService productGroupService;

    /**
     * 库位详情服务，用于管理库位详情信息，如库存数量、产品版本等
     */
    private final BinLocationDetailService binLocationDetailService;

    /**
     * 库位详情锁定服务，用于管理库位详情的锁定信息，如锁定数量、完成数量等
     */
    private final BinLocationDetailLockedService binLocationDetailLockedService;

    /**
     * 库位服务，用于管理库位信息，如库位名称、库位类型等
     */
    private final BinLocationService binLocationService;

    /**
     * 库存预留服务，用于管理库存预留信息，如预留数量、预留状态等
     */
    private final InventoryReserveService inventoryReserveService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////


    /**
     * 将OTC库存移动到ReadyToGo区域
     * <p>
     * 该方法用于将OTC的库存移动到ReadyToGo虚拟库位。
     * 先获取OTC的ReadyToGo虚拟库位，然后调用通用的库存移动方法。
     * </p>
     * <p>
     * ReadyToGo区域是一个虚拟库位，用于存放已拣货并准备发货的商品。
     * </p>
     *
     * @param pickList 拣货列表，包含要移动的产品、库位和数量信息
     * @param <T>      拣货对象类型，必须继承ReadyToGoProductBinLocationPickVO
     */
    @Override
    public <T extends ReadyToGoProductBinLocationPickVO> void otcMoveBinLocationInventoryToReadyToGo(List<T> pickList) {
        // 获取ReadyToGo的库位
        BinLocation readyToGo = binLocationService.findVirtualBinLocationByType(BinTypeEnum.OTC_READYTOGO);
        moveBinLocationInventoryToReadyToGo(pickList, readyToGo);
    }

    /**
     * 将OTB库存移动到ReadyToGo区域
     * <p>
     * 该方法用于将OTB的库存移动到ReadyToGo虚拟库位。
     * 先获取OTB的ReadyToGo虚拟库位，然后调用通用的库存移动方法。
     * </p>
     * <p>
     * 与OTC类似，OTB的ReadyToGo区域也是一个虚拟库位，用于存放已拣货并准备发货的商品。
     * </p>
     *
     * @param pickList 拣货列表，包含要移动的产品、库位和数量信息
     * @param <T>      拣货对象类型，必须继承ReadyToGoProductBinLocationPickVO
     */
    @Override
    public <T extends ReadyToGoProductBinLocationPickVO> void otbMoveBinLocationInventoryToReadyToGo(List<T> pickList) {
        // 获取ReadyToGo的库位
        BinLocation readyToGo = binLocationService.findVirtualBinLocationByType(BinTypeEnum.OTB_READYTOGO);
        moveBinLocationInventoryToReadyToGo(pickList, readyToGo);
    }

    /**
     * 将库存移动到ReadyToGo区域
     * <p>
     * 该方法是库存移动到ReadyToGo区域的通用实现，用于将已拣货的商品从原库位移动到ReadyToGo虚拟库位。
     * 主要流程包括：
     * 1. 在ReadyToGo虚拟库位创建库位详情
     * 2. 将ReadyToGo库位详情绑定到拣货对象上
     * 3. 构建库位锁定信息
     * 4. 将ReadyToGo库位锁定绑定到拣货对象上
     * 5. 更新库位锁定和库存数量
     * </p>
     * <p>
     * 该方法在拣货过程中起到关键作用，实现了库存从原库位到ReadyToGo区域的移动，
     * 便于后续的发货操作。
     * </p>
     *
     * @param pickList  拣货列表，包含要移动的产品、库位和数量信息
     * @param readyToGo ReadyToGo虚拟库位
     * @param <T>       拣货对象类型，必须继承ReadyToGoProductBinLocationPickVO
     */
    @Override
    public <T extends ReadyToGoProductBinLocationPickVO> void moveBinLocationInventoryToReadyToGo(List<T> pickList, BinLocation readyToGo) {

        // 批量创建库位详情
        List<BinLocationDetailBatchCreateBO> binLocationDetailBatchCreates = BeanUtil.copyNew(pickList, BinLocationDetailBatchCreateBO.class);

        // 需要移动到ReadyToGo的库位详情
        List<BinLocationDetail> readyToGoDetailList = binLocationDetailService.batchCreateIfAbsent(readyToGo.getId(), binLocationDetailBatchCreates);

        // 绑定ReadyToGo库位详情
        Map<Long, BinLocationDetail> productVersionToBinLocationDetailMap = readyToGoDetailList.stream()
                .collect(Collectors.toMap(BinLocationDetail::getProductVersionId, Function.identity()));
        pickList.forEach(obj -> obj.setReadyToGoBinLocationDetail(productVersionToBinLocationDetailMap.get(obj.getProductVersionId())));

        // 构建库位锁
        List<BinLocationDetailLockedBatchCreateBO> lockedCreateList = pickList.stream()
                .map(pick -> {
                    BinLocationDetailLockedBatchCreateBO batchCreate = new BinLocationDetailLockedBatchCreateBO();
                    batchCreate.setBinLocationDetail(pick.getReadyToGoBinLocationDetail());
                    RefTableBO refTable = new RefTableBO();
                    BeanUtil.copy(pick, refTable);
                    batchCreate.setLockedRefTable(refTable);
                    return batchCreate;
                })
                .toList();

        List<BinLocationDetailLocked> detailLockedList = binLocationDetailLockedService.batchCreateIfAbsent(lockedCreateList);
        // 填充ReadyToGo库位锁
        boundReadyToGoLocked(readyToGoDetailList, pickList, detailLockedList);

        // 需要扣减的库位
        List<Long> binLocationDetailIdList = StreamUtils.distinctMap(pickList, ReadyToGoProductBinLocationPickVO::getBinLocationDetailId);
        List<BinLocationDetail> binLocationDetailList = binLocationDetailService.listByIds(binLocationDetailIdList);
        Map<Long, BinLocationDetail> binLocationDetailMap = StreamUtils.toMap(binLocationDetailList, IdModel::getId);

        // 获取源库位锁
        List<Long> lockedIds = StreamUtils.distinctMap(pickList, ProductBinLocationPickVO::getBinLocationDetailLockedId);
        List<BinLocationDetailLocked> lockedList = binLocationDetailLockedService.listByIds(lockedIds);
        Map<Long, BinLocationDetailLocked> lockedMap = StreamUtils.toMap(lockedList, IdModel::getId);

        List<BinLocationDetailChangeBO> changeList = pickList.stream()
                .map(pick -> {
                    BinLocationDetailChangeBO change = new BinLocationDetailChangeBO();
                    change.setChangeType(BinLocationLogEnum.PICK.getStatus());
                    change.setChangeQty(pick.getChangePickQty());
                    change.setRefTable(pick.getPickLogInfo());
                    // 源库位
                    change.setSource(binLocationDetailMap.get(pick.getBinLocationDetailId()));
                    // 源库位锁
                    change.setSourceLock(lockedMap.get(pick.getBinLocationDetailLockedId()));
                    // ReadyToGo库位
                    change.setDest(pick.getReadyToGoBinLocationDetail());
                    change.setDestLock(pick.getReadyToGoLocked());
                    return change;
                })
                .toList();
        // 更新锁
        binLocationDetailLockedService.updateByChange(changeList);
        // 更新库存
        binLocationDetailService.updateInStockByChange(changeList);
    }

    /**
     * 查找库存不足的工单列表（产品模型版本）
     * <p>
     * 该方法用于查找库存不足的工单和产品，适用于产品模型类型的详情。
     * 该方法将调用通用的findNoEnoughList方法，并传入适当的参数。
     * </p>
     * <p>
     * 该方法在拣货前进行库存检查，确保有足够的库存来满足工单需求。
     * </p>
     *
     * @param detailMap                  工单ID到工单详情列表的映射
     * @param productInStockMap          产品ID到库位详情列表的映射
     * @param hasStockHeaderIdListGetter 获取有足够库存的工单ID列表的函数
     * @param noEnoughConsumer           处理库存不足情况的消费者函数
     * @param <Detail>                   工单详情类型，必须继承ProductModel
     * @return 库存不足的工单列表
     */
    @Override
    public <Detail extends ProductModel> List<WorkOrderNoEnoughAvailQtyVO> findNoEnoughList(Map<Long, List<Detail>> detailMap,
                                                                                            Map<Long, List<BinLocationDetail>> productInStockMap,
                                                                                            Function<Map<Long, Integer>, List<Long>> hasStockHeaderIdListGetter,
                                                                                            BiConsumer<WorkOrderNoEnoughAvailQtyVO, Detail> noEnoughConsumer) {
        Map<Long, Integer> productInStockQtyMap = productInStockMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> entry.getValue().stream().mapToInt(BinLocationDetail::getInStockQty).sum()));
        // 使用ProductModel 快捷方法
        return findNoEnoughList(detailMap, productInStockQtyMap, ProductModel::getProductId, ProductModel::getQty, hasStockHeaderIdListGetter, noEnoughConsumer);
    }

    /**
     * 查找库存不足的工单列表（通用版本）
     * <p>
     * 该方法是查找库存不足的工单和产品的通用实现，可以处理任意类型的详情对象。
     * 主要流程包括：
     * 1. 获取有足够库存的工单ID列表
     * 2. 过滤出库存不足的工单
     * 3. 对每个工单的详情进行处理，模拟扣减库存并记录库存不足的情况
     * 4. 填充产品信息并返回库存不足的列表
     * </p>
     * <p>
     * 该方法通过函数式编程的方式，实现了对不同类型详情对象的通用处理。
     * 调用者需要提供产品ID获取器、数量获取器等函数，以适应不同类型的详情对象。
     * </p>
     *
     * @param detailMap                  工单ID到工单详情列表的映射
     * @param productInStockMap          产品ID到库存数量的映射
     * @param productIdGetter            从详情对象中获取产品ID的函数
     * @param needQtyGetter              从详情对象中获取所需数量的函数
     * @param hasStockHeaderIdListGetter 获取有足够库存的工单ID列表的函数
     * @param noEnoughConsumer           处理库存不足情况的消费者函数
     * @param <Detail>                   工单详情类型
     * @return 库存不足的工单列表
     */
    @Override
    public <Detail> List<WorkOrderNoEnoughAvailQtyVO> findNoEnoughList(Map<Long, List<Detail>> detailMap,
                                                                       Map<Long, Integer> productInStockMap,
                                                                       Function<Detail, Long> productIdGetter,
                                                                       Function<Detail, Integer> needQtyGetter,
                                                                       Function<Map<Long, Integer>, List<Long>> hasStockHeaderIdListGetter,
                                                                       BiConsumer<WorkOrderNoEnoughAvailQtyVO, Detail> noEnoughConsumer) {
        // 拥有库存的头单id列表
        List<Long> hasStockHeaderIdList = hasStockHeaderIdListGetter.apply(productInStockMap);

        // 过滤库存不足的工单
        List<WorkOrderNoEnoughAvailQtyVO> noEnoughList = detailMap.entrySet()
                .stream()
                // 工单不足的部分
                .filter(entry -> !hasStockHeaderIdList.contains(entry.getKey()))
                .map(Map.Entry::getValue)
                .map(details -> details.stream()
                        // 扣减产品库存
                        .peek(obj -> {
                            // 产品id
                            Long productId = productIdGetter.apply(obj);
                            productInStockMap.put(productId, productInStockMap.getOrDefault(productId, 0) - needQtyGetter.apply(obj));
                        })
                        // 库存不足
                        .filter(obj -> productInStockMap.get(productIdGetter.apply(obj)) < 0)
                        .map(obj -> {
                            WorkOrderNoEnoughAvailQtyVO noEnough = new WorkOrderNoEnoughAvailQtyVO();
                            noEnough.setAvailQty(productInStockMap.get(productIdGetter.apply(obj)));
                            noEnough.setNeedQty(needQtyGetter.apply(obj));
                            noEnough.setProductId(productIdGetter.apply(obj));
                            noEnoughConsumer.accept(noEnough, obj);
                            return noEnough;
                        })
                        // 将库存置为0，方便后续扣减
                        .peek(obj -> productInStockMap.put(obj.getProductId(), 0))
                        .toList()
                )
                .flatMap(Collection::stream)
                .toList();

        // 填充产品
        ProductCacheUtil.filledProduct(noEnoughList);
        return noEnoughList;
    }

    /**
     * 获取拣货类型
     * <p>
     * 该方法用于根据库位ID列表和当前拣货类型确定拣货类型。
     * 拣货类型可以是以下几种：
     * - PREP：全部从预处理虚拟库位拣货
     * - NORMAL：全部从普通库位拣货
     * - BOTH：同时从预处理虚拟库位和普通库位拣货
     * </p>
     * <p>
     * 判断逻辑如下：
     * 1. 获取所有虚拟库位，并计算库位ID列表中虚拟库位的数量
     * 2. 判断是否是首次拣货或当前拣货类型为NONE
     * 3. 判断是否全部从预处理虚拟库位拣货
     * 4. 判断是否全部从普通库位拣货
     * 5. 根据以上判断返回相应的拣货类型
     * </p>
     *
     * @param binLocationIdList   库位ID列表
     * @param currentPickFromType 当前拣货类型
     * @return 拣货类型，PREP/NORMAL/BOTH之一
     */
    @Override
    public String getPickFromType(List<Long> binLocationIdList, String currentPickFromType) {
        // 虚拟库位
        Map<Long, BinLocation> virtualBinLocationMap = binLocationService.allVirtualBinLocationList();
        // 是虚拟库位数量
        long virtualCount = binLocationIdList
                .stream()
                .filter(virtualBinLocationMap::containsKey)
                .count();
        // 首次拣货
        boolean isFirstPick = StringUtil.isBlank(currentPickFromType)
                || PickFromType.NONE.getType().equals(currentPickFromType);
        // 全部Prep库位拣货
        boolean allPrep = virtualCount == binLocationIdList.size()
                // 首次拣货 或者 当前拣货单拣货类型为Prep
                && (isFirstPick || PickFromType.PREP.getType().equals(currentPickFromType));
        // 全部正常库位拣货
        boolean allNormal = virtualCount == 0
                // 首次拣货 或者 当前拣货单拣货类型为Normal
                && (isFirstPick || PickFromType.NORMAL.getType().equals(currentPickFromType));

        return allPrep ? PickFromType.PREP.getType()
                : allNormal ? PickFromType.NORMAL.getType() : PickFromType.BOTH.getType();
    }

    /**
     * 处理全部拣货完成的情况
     * <p>
     * 该方法用于处理全部拣货完成的情况，判断每个工单是否全部拣货完成，并调用相应的处理函数。
     * 主要流程包括：
     * 1. 根据拣货列表和工单详情，判断每个工单是否全部拣货完成
     * 2. 对每个工单调用处理函数，传入工单ID和是否全部拣货完成的标志
     * </p>
     * <p>
     * 该方法在拣货完成时调用，用于更新工单状态。
     * </p>
     *
     * @param pickList                  拣货列表
     * @param headerIdFunction          从详情对象中获取工单ID的函数
     * @param detailGroupByProductIdMap 按产品ID分组的工单详情映射
     * @param allPickedConsumer         处理全部拣货完成情况的消费者函数，接收工单ID和是否全部拣货完成的标志
     * @param <Detail>                  工单详情类型，必须继承ProductModel
     */
    @Override
    public <Detail extends ProductModel> void dealWithAllPicked(List<? extends BasePickVO> pickList,
                                                                Function<Detail, Long> headerIdFunction,
                                                                Map<Long, List<Detail>> detailGroupByProductIdMap,
                                                                BiConsumer<Long, Boolean> allPickedConsumer) {
        // 更新工单状态
        detailGroupByProductIdMap
                .values()
                .stream()
                .flatMap(Collection::stream)
                // 按工单id分组
                .collect(Collectors.groupingBy(headerIdFunction))
                .forEach((workId, wkDetailList) -> {
                    // 全部拣货完
                    boolean allPicked = wkDetailList.stream()
                            .allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()))
                            || ObjectUtil.isEmpty(wkDetailList);
                    allPickedConsumer.accept(workId, allPicked);
                });
    }

    @Override
    public <WorkOrder extends SuperModel, WorkOrderDetail extends ProductModel> List<Long> filterHasInStockWorkOrder(
            List<WorkOrder> dataList, Map<Long, List<WorkOrderDetail>> detailGroupByWorkOrderMap, BaseBinLocationQuery binLocationQuery) {
        // 产品id集合
        List<Long> productIdList = detailGroupByWorkOrderMap.values().stream()
                .flatMap(Collection::stream)
                .map(ProductModel::getProductId)
                .distinct()
                .toList();
        // 产品可用库存映射
        Map<Long, Integer> productAvailableStockMap = binLocationDetailService.realAvailableInStockGroupByProductId(binLocationQuery, productIdList);
        return StreamUtils.filterHasProductStock(StreamUtils.sortByList(detailGroupByWorkOrderMap, dataList), productAvailableStockMap);
    }

    @Override
    public PrepFullProductVO findAndFillWithPrepConvert(ProductTreeQuery productTreeQuery, List<WorkOrderMembershipBO> memberships) {
        // 构建产品结构
        PrepFullProductVO prepProduct = productService.getPrepProductTree(productTreeQuery);

        // 工单 中产品要么是Child 要么就是Parent
        Map<Long, List<WorkOrderMembershipBO>> productMap = StreamUtils.groupBy(memberships, WorkOrderMembershipBO::getProductId);
        List<PrepGroupVO> fixGroupList = ObjectUtil.<List<PrepGroupVO>>nullToDefault(prepProduct.getGroupList(), Collections.emptyList())
                .stream()
                // 1.工单产品是父产品，子产品必须是工单Header产品, 2. 工单产品是子产品
                .filter(group -> (productMap.containsKey(group.getParentProductId()) && group.getChildProductId().equals(productTreeQuery.getProductId()))
                        || productMap.containsKey(group.getChildProductId()))
                .toList();
        prepProduct.setGroupList(fixGroupList);

        // 填充PrepConvert
        fillWithPrepConvert(prepProduct, memberships);
        return prepProduct;
    }

    @Override
    public void fillWithPrepConvert(PrepFullProductVO productTree, List<WorkOrderMembershipBO> detailList) {
        // 可以处理的类型
        List<PrepWordorderTypeEnum> canProcessTypes = Arrays.asList(
                // PrepConvertMultiBox
                PrepWordorderTypeEnum.PREP_CONVERT_MULTI_BOX,
                // PrepConvertPack
                PrepWordorderTypeEnum.PREP_CONVERT_PACK
        );
        if (!canProcessTypes.contains(productTree.getType())) {
            return;
        }
        Map<Long, List<ProductGroup>> groupMap = findGroupsWithMembership(detailList);
        // 孩子节点
        List<Long> childIdList = groupMap.values().stream()
                .flatMap(Collection::stream)
                .map(ProductGroup::getChildProductId)
                .distinct()
                .toList();

        List<Long> productIdList = new ArrayList<>(childIdList);
        productIdList.addAll(groupMap.keySet());
        // 产品缓存获取
        Map<Long, BaseProductVO> baseProductMap = ProductCacheUtil.listByIds(productIdList)
                .stream()
                .map(obj -> BeanUtil.copyNew(obj, BaseProductVO.class))
                .collect(Collectors.toMap(BaseProductVO::getId, Function.identity()));

        // 赋值MultiBox Group
        ObjectUtil.nullToDefault(productTree.getMultiBoxList(), Collections.<PrepMultiBoxGroupVO>emptyList())
                .stream()
                .map(PrepMultiBoxGroupVO::getDetailList)
                .flatMap(Collection::stream)
                .filter(obj -> groupMap.containsKey(obj.getProductId()))
                .forEach(obj -> obj.setGroupList(groupMap.get(obj.getProductId())
                        .stream()
                        .map(group -> {
                            PrepGroupVO groupVO = new PrepGroupVO();
                            BeanUtil.copy(group, groupVO);
                            groupVO.setParentBaseProductVO(baseProductMap.get(group.getParentProductId()));
                            groupVO.setChildBaseProductVO(baseProductMap.get(group.getChildProductId()));
                            return groupVO;
                        })
                        .toList())
                );

        // 赋值Combo Group
        ObjectUtil.nullToDefault(productTree.getComponentList(), Collections.<PrepComponentGroupVO>emptyList())
                .stream()
                .filter(obj -> groupMap.containsKey(obj.getComponentProductId()))
                .forEach(obj -> obj.setGroupList(groupMap.get(obj.getComponentProductId()).stream()
                        .map(group -> {
                            PrepGroupVO groupVO = new PrepGroupVO();
                            BeanUtil.copy(group, groupVO);
                            groupVO.setParentBaseProductVO(baseProductMap.get(group.getParentProductId()));
                            groupVO.setChildBaseProductVO(baseProductMap.get(group.getChildProductId()));
                            return groupVO;
                        })
                        .toList())
                );
    }

    @Override
    public void releaseWorkorderVirtualLocked(Map<Long, List<BinLocationDetailLocked>> lockedGroupByWkDetailMap) {
        if (ObjectUtil.isEmpty(lockedGroupByWkDetailMap)) {
            return;
        }
        // 释放虚拟库位锁
        List<BinLocationDetailLockedChangeBO> changeList = lockedGroupByWkDetailMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(lock -> {
                    BinLocationDetailLockedChangeBO change = new BinLocationDetailLockedChangeBO();
                    change.setSourceLock(lock);
                    change.setChangeQty(lock.getQty() - lock.getFinishQty());
                    return change;
                })
                .toList();
        // 释放
        binLocationDetailLockedService.updateByChange(changeList);
    }

    @Override
    public Map<Long, List<ProductGroup>> findGroupsWithMembership(List<WorkOrderMembershipBO> memberships) {
        // 获取产品id
        List<Long> productIdList = memberships
                .stream()
                .map(WorkOrderMembershipBO::getProductId)
                .distinct()
                .toList();
        List<ProductCache> productCacheList = ProductCacheUtil.listByIds(productIdList);
        // 父产品id
        List<Long> parentIdList = productCacheList.stream()
                .filter(obj -> Objects.equals(obj.getGroupType(), ProductGroupTypeEnum.PARENT.getType()))
                .map(ProductCache::getId)
                .distinct()
                .toList();
        // 子产品id
        List<Long> childIdList = productCacheList.stream()
                .filter(obj -> Objects.equals(obj.getGroupType(), ProductGroupTypeEnum.CHILD.getType()))
                .map(ProductCache::getId)
                .distinct()
                .toList();

        // Group版本列表
        List<Integer> groupVersionIntList = memberships
                .stream()
                .map(WorkOrderMembershipBO::getPrepWorkorderDetailVersionInt)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .toList();

        // 工单详情映射
        Map<Long, WorkOrderMembershipBO> detailMap = memberships
                .stream()
                .collect(Collectors.toMap(WorkOrderMembershipBO::getId, Function.identity()));

        final String versionFormat = "%d:%d:%d";
        // [ParentId + ChildId + VersionInt] 过滤子产品, 父子关系指的是工单的父子关系并非是产品父子关系
        Map<String, List<WorkOrderMembershipBO>> sameProductWithVersionIntMap = memberships
                .stream()
                .filter(obj -> detailMap.containsKey(obj.getParentId()))
                // PrepConvert 这种情况下才会有 groupVersionInt
                .collect(Collectors.groupingBy(obj -> String.format(versionFormat,
                        detailMap.get(obj.getParentId()).getProductId(),
                        obj.getProductId(),
                        detailMap.get(obj.getParentId()).getPrepWorkorderDetailVersionInt()
                )));
        // 尽可能一次查询、过滤出符合条件的Group
        List<ProductGroup> productGroupList
                = productGroupService.findByParentAndChildProductIdAndVersionInt(parentIdList, childIdList, groupVersionIntList);

        // 通过子产品作为父工单的Group
        Map<Long, List<ProductGroup>> childGroupsMap = productGroupList.stream()
                .filter(obj -> sameProductWithVersionIntMap.containsKey(String.format(versionFormat,
                        obj.getChildProductId(), obj.getParentProductId(), obj.getGroupVersionInt())
                ))
                .collect(Collectors.groupingBy(ProductGroup::getChildProductId));

        // 通过父产品作为父工单的Group
        Map<Long, List<ProductGroup>> parentGroupsMap = productGroupList.stream()
                .filter(obj -> sameProductWithVersionIntMap.containsKey(String.format(versionFormat,
                        obj.getParentProductId(), obj.getChildProductId(), obj.getGroupVersionInt())
                ))
                .collect(Collectors.groupingBy(ProductGroup::getParentProductId));

        // 聚合父工单产品id返回
        return Stream.concat(parentGroupsMap.entrySet().stream(), childGroupsMap.entrySet().stream())
                .collect(Collectors.groupingBy(Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.flatMapping(Collection::stream, Collectors.toList())))
                );
    }

    @Override
    public void releaseLockAndReduceInStock(Collection<BinLocationDetailLockedChangeBO> changeList) {
        // 设置变更库位信息
        List<BinLocationDetailLocked> lockedList = StreamUtils.distinctMap(changeList, BinLocationDetailLockedChangeBO::getSourceLock);
        List<Long> detailIdList = StreamUtils.distinctMap(lockedList, BinLocationDetailLocked::getBinLocationDetailId);
        Map<Long, BinLocationDetail> binLocationDetailMap = StreamUtils.toMap(binLocationDetailService.listByIds(detailIdList), IdModel::getId);
        changeList.forEach(change -> {
            BinLocationDetail source = binLocationDetailMap.get(change.getSourceLock().getBinLocationDetailId());
            change.setSource(source);
            change.setDest(source);
        });

        // 按照关联表的纬度记录日志
        Map<String, BinLocationDetailLockedChangeBO> tempMap = new LinkedHashMap<>();
        for (BinLocationDetailLockedChangeBO bo : changeList) {
            String key = bo.getRefTable().getRefTableShowRefNum() + StringPool.COLON + bo.getSource().getId();
            if (tempMap.containsKey(key)) {
                BinLocationDetailLockedChangeBO existing = tempMap.get(key);
                existing.setChangeQty(existing.getChangeQty() + bo.getChangeQty());
            } else {
                BinLocationDetailLockedChangeBO binLocationDetailLockedChangeBO = BeanUtil.copyNew(bo, BinLocationDetailLockedChangeBO.class);
                tempMap.put(key, binLocationDetailLockedChangeBO);
            }
        }
        List<BinLocationDetailLockedChangeBO> fixChangeList = new ArrayList<>(tempMap.values());
        // 释放锁
        binLocationDetailLockedService.updateByChange(changeList);

        // 更新库存
        binLocationDetailService.updateInStockByChange(fixChangeList);

    }

    @Override
    public <T extends BaseWorkorderBinLocationModel, R extends PrepWorkorderDetailPutAwayVO> void putAwayReleaseLocked(
            List<R> wkDetailPutAwayList,
            List<T> prepWorkorderBinLocationList,
            Function<T, Long> releaseLockedGroupFunction,
            BiConsumer<Long, BinLocationDetailLockedChangeBO> changeConsumer) {
        // 工单详情上架映射
        Map<Long, PrepWorkorderDetailPutAwayVO> wkDetailMap = wkDetailPutAwayList.stream()
                .filter(obj -> obj.getChangePutAwayQty() > 0)
                .collect(Collectors.toMap(BasePutAwayVO::getId, Function.identity()));

        // 释放锁
        workorderBinLocationReleaseLockedAndReduceInStock(prepWorkorderBinLocationList,
                releaseLockedGroupFunction,
                detailId -> {
                    PrepWorkorderDetailPutAwayVO putAwayVO = wkDetailMap.get(detailId);
                    return putAwayVO.getChangePutAwayQty();
                },
                changeConsumer
        );
    }

    @Override
    public <T extends BaseWorkorderBinLocationModel> void workorderBinLocationReleaseLockedAndReduceInStock(
            List<T> prepWorkorderBinLocationList,
            Function<T, Long> releaseLockedGroupFunction,
            Function<Long, Integer> allocatedQtyFunction,
            BiConsumer<Long, BinLocationDetailLockedChangeBO> changeConsumer) {
        // 分配锁
        List<Long> lockedIdList = StreamUtils.distinctMap(prepWorkorderBinLocationList, BaseWorkorderBinLocationModel::getBinLocationDetailLockedId);
        List<BinLocationDetailLocked> lockedList = binLocationDetailLockedService.listByLocked(lockedIdList);

        // 待分配锁释放信息
        Map<Long, BinLocationLockedReleaseVO> lockedReleaseMap
                = StreamUtils.toMap(BeanUtil.copyNew(lockedList, BinLocationLockedReleaseVO.class), BinLocationLockedReleaseVO::getId);

        Map<Long, BinLocationDetailLocked> lockedMap = StreamUtils.toMap(lockedList, IdModel::getId);

        // 释放锁的数量映射
        List<BinLocationDetailLockedChangeBO> changeList = new ArrayList<>();
        Map<Long, Set<Long>> groupedLocks = prepWorkorderBinLocationList.stream()
                .collect(Collectors.groupingBy(releaseLockedGroupFunction,
                        Collectors.mapping(BaseWorkorderBinLocationModel::getBinLocationDetailLockedId, Collectors.toSet())));

        for (Map.Entry<Long, Set<Long>> entry : groupedLocks.entrySet()) {
            Long key = entry.getKey();
            int qty = allocatedQtyFunction.apply(key);

            List<BinLocationLockedReleaseVO> lockedRealeseList = new ArrayList<>();
            for (Long lockId : entry.getValue()) {
                if (lockedReleaseMap.containsKey(lockId)) {
                    BinLocationLockedReleaseVO vo = lockedReleaseMap.get(lockId);
                    vo.setBeforeFinishQty(vo.getFinishQty());
                    lockedRealeseList.add(vo);
                }
            }

            AllocationUtil.checkAndAllocationQty(lockedRealeseList, qty);

            for (BinLocationLockedReleaseVO obj : lockedRealeseList) {
                if (obj.getChangeReleaseQty() > 0) {
                    BinLocationDetailLockedChangeBO change = new BinLocationDetailLockedChangeBO();
                    change.setChangeQty(obj.getChangeReleaseQty());
                    change.setSourceLock(lockedMap.get(obj.getId()));
                    changeConsumer.accept(key, change);
                    changeList.add(change);
                }
            }
        }

        // 释放锁并更新库存
        releaseLockAndReduceInStock(changeList);
    }

    @Override
    public void releaseReserveLocked(List<BaseModelInventoryReserveBO> reserveList) {
        Validate.notEmpty(reserveList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "ReserveLocked"));
        // 没有释放锁的工单详情
        List<BaseModelInventoryReserveBO> notReserveWorkorderDetailList = reserveList.stream()
                .filter(obj -> ObjectUtil.isNull(obj.getInventoryReserveId()))
                .toList();
        // 存在没有锁的详情
        if (ObjectUtil.isNotEmpty(notReserveWorkorderDetailList)) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Workorder inventory reserve lock is missing"));
        }

        // 校验工单详情锁是否存在
        List<InventoryReleaseLockedParam> reserveInventoryReleaseList = reserveList
                .stream()
                .map(obj -> {
                    InventoryReleaseLockedParam lockedParam = new InventoryReleaseLockedParam();
                    lockedParam.setId(obj.getInventoryReserveId());
                    lockedParam.setQty(obj.getQty());
                    return lockedParam;
                })
                .toList();
        // 释放预定锁
        inventoryReserveService.releaseReserveInventory(reserveInventoryReleaseList);
    }

    @Override
    public Map<Long, List<BinLocationDetail>> fullCanBuildVirtualBinLocation(
            BaseBinLocationQuery binLocationQuery,
            List<Long> productIdList,
            Map<Long, List<BinLocationDetailLocked>> lockedGroupByWkDetailMap) {
        // 获取可分配的库位详情
        Map<Long, List<BinLocationDetail>> realAvailableBinLocationDetailGroupByProductMap = binLocationDetailService.realAvailableGroupByProductId(binLocationQuery, productIdList);

        // 锁住的虚拟库位锁
        Set<Long> binLocationDetailIdList = lockedGroupByWkDetailMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(BinLocationDetailLocked::getBinLocationDetailId)
                .collect(Collectors.toSet());

        // 锁住的库位库存
        Map<Long, Integer> binLocationDetailLockedQtyMap = lockedGroupByWkDetailMap.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(BinLocationDetailLocked::getBinLocationDetailId,
                        Collectors.mapping(lock -> lock.getQty() - lock.getFinishQty(), Collectors.summingInt(Integer::intValue)))
                );

        // 获取锁住的库位详情
        List<BinLocationDetail> lockedBinLocationDetailList = binLocationDetailService.listByIds(binLocationDetailIdList);

        // 聚合虚拟已经锁定的库位
        List<BinLocationDetail> realAvialableList = Stream.concat(
                        lockedBinLocationDetailList.stream()
                                .peek(bin -> bin.setInStockQty(bin.getInStockQty() + binLocationDetailLockedQtyMap.getOrDefault(bin.getId(), 0)))
                                .filter(bin -> bin.getInStockQty() > 0),
                        realAvailableBinLocationDetailGroupByProductMap.values()
                                .stream()
                                .flatMap(Collection::stream)
                )
                .toList();
        return binLocationDetailService.groupByProductAndSortByInStockAndLocationName(realAvialableList);
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////


    /**
     * 填充ReadyToGo锁到拣货信息上
     *
     * @param readyToGoDetailList ReadyToGo库位
     * @param pickList            拣货信息
     * @param detailLockedList    ReadyToGo锁
     */
    private <T extends ReadyToGoProductBinLocationPickVO> void boundReadyToGoLocked(List<BinLocationDetail> readyToGoDetailList, List<T> pickList, List<BinLocationDetailLocked> detailLockedList) {
        pickList.stream()
                // 根据ref_table_id分组，这里是工单id
                .collect(Collectors.groupingBy(ReadyToGoProductBinLocationPickVO::getRefTableId))
                .forEach((key, value) -> {
                    // 产品版本拣货映射
                    Map<Long, Integer> productVersionMap = value.stream()
                            .collect(Collectors.groupingBy(ProductBinLocationPickVO::getProductVersionId,
                                    // 根据产品版本id累加拣货数量
                                    Collectors.reducing(0, ProductBinLocationPickVO::getChangePickQty, Integer::sum)));

                    // 获取该工单下单锁
                    List<BinLocationDetailLocked> lockedInRefTableList = detailLockedList.stream()
                            // 过滤该工单下单锁
                            .filter(obj -> Objects.equals(obj.getRefTableId(), key))
                            .toList();

                    // product_version_id -> bin_location_detail_id -> bin_location_detail_locked_id
                    // 库位详情 -> 库位详情锁映射
                    Map<Long, Long> binLocationIdToLockIdMap = lockedInRefTableList
                            .stream()
                            .collect(Collectors.toMap(BinLocationDetailLocked::getBinLocationDetailId, IdModel::getId));
                    // 获取ReadyToGo库位锁变更数量
                    Map<Long, Integer> readyToGoChangeQty = readyToGoDetailList.stream()
                            .filter(obj -> productVersionMap.containsKey(obj.getProductVersionId())
                                    && binLocationIdToLockIdMap.containsKey(obj.getId()))
                            .collect(Collectors.toMap(
                                    obj -> binLocationIdToLockIdMap.get(obj.getId()),
                                    obj -> productVersionMap.get(obj.getProductVersionId()))
                            );
                    // 增加ReadyToGo库位锁的数量
                    // lockedInRefTableList.forEach(obj -> obj.setQty(obj.getQty() + readyToGoChangeQty.getOrDefault(obj.getId(), 0)));

                    // 将锁信息绑定到工单的拣货信息上
                    Map<Long, BinLocationDetailLocked> lockedMap = lockedInRefTableList.stream()
                            .collect(Collectors.toMap(BinLocationDetailLocked::getProductVersionId, Function.identity()));
                    value.stream()
                            .filter(obj -> lockedMap.containsKey(obj.getProductVersionId()))
                            // 绑定锁信息到拣货信息
                            .forEach(obj -> obj.setReadyToGoLocked(lockedMap.get(obj.getProductVersionId())));
                });
    }
}
