package cn.need.cloud.biz.model.param;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 货权转移详情 UpdateParam对象
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@Schema(description = "货权转移详情 UpdateParam对象")
public class TransferOwnerShipRequestDetailUpdateParam implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    @Size(max = 512, message = "deletedNote cannot exceed 512 characters")
    private String deletedNote;
    /**
     * 源产品Id
     */
    @Schema(description = "源产品Id")
    @NotNull(message = "fromProductId cannot be null")
    private Long fromProductId;
    /**
     * 序号
     */
    @Schema(description = "序号")
    @NotNull(message = "lineNum cannot be null")
    private Integer lineNum;
    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 200, message = "note cannot exceed 200 characters")
    private String note;
    /**
     * 目标产品Id
     */
    @Schema(description = "目标产品Id")
    @NotNull(message = "toProductId cannot be null")
    private Long toProductId;
    /**
     * 转移数量
     */
    @Schema(description = "转移数量")
    @NotNull(message = "transferQty cannot be null")
    private Integer transferQty;
    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    @NotNull(message = "warehouseId cannot be null")
    private Long warehouseId;

}