package cn.need.cloud.biz.model.query.otc.pickingslip.prep;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * OTC预提货单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC预提货单 query对象")
public class OtcPrepPickingSlipQuery extends SuperQuery {

    /**
     * 预拣货状态
     */
    @Schema(description = "预拣货状态， subPickingSlipStatus")
    private String prepPickingSlipStatus;

    /**
     * 预拣货状态
     */
    @Schema(description = "预拣货状态集合，subPickingSlipStatus")
    @Condition(value = Keyword.IN, fields = {"prepPickingSlipStatus"})
    private List<String> prepPickingSlipStatusList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 拣货id
     */
    @Schema(description = "拣货id")
    private Long otcPickingSlipId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 预拣货类型
     */
    @Schema(description = "预拣货类型，SubPickingSlipType")
    private String prepPickingSlipType;

    /**
     * 预拣货类型
     */
    @Schema(description = "预拣货类型集合")
    @Condition(value = Keyword.IN, fields = {"prepPickingSlipType"})
    private List<String> prepPickingSlipTypeList;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型")
    private String orderType;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型集合")
    @Condition(value = Keyword.IN, fields = {"orderType"})
    private List<String> orderTypeList;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里集合")
    @Condition(value = Keyword.IN, fields = {"pickToStation"})
    private List<String> pickToStationList;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态集合")
    @Condition(value = Keyword.IN, fields = {"printStatus"})
    private List<String> printStatusList;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private List<String> prepPickingSlipProductTypeList;

    /**
     * 流程类型
     */
    @Schema(description = "流程类型")
    private String processType;

}