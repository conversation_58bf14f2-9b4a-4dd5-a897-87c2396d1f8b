package cn.need.cloud.biz.service.otc.workorder.impl;

import cn.hutool.core.lang.Pair;
import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.*;
import cn.need.cloud.biz.client.constant.workorder.WorkorderLogConstant;
import cn.need.cloud.biz.converter.otc.OtcWorkorderConverter;
import cn.need.cloud.biz.mapper.otc.OtcWorkorderMapper;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.query.base.BaseBinLocationQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otc.workorder.OtcWorkOrderListQuery;
import cn.need.cloud.biz.model.query.otc.workorder.OtcWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.*;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipDetailPickVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipPickContextVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestVO;
import cn.need.cloud.biz.model.vo.otc.workorder.*;
import cn.need.cloud.biz.model.vo.page.OtcWorkorderPageVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcWorkOrderAuditLogHelper;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageService;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.ship.OtcShipStationConfigService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderBinLocationService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.biz.util.log.AuditLogUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC工单服务实现类
 * </p>
 * <p>
 * 该类负责处理OTC（Outbound To Customer）出库工单的全生命周期管理，
 * 包括工单的创建、查询、更新、拣货、发货等操作。
 * 作为核心业务服务，与其他多个业务实体（如请求单、拣货单、包裹）有紧密的交互。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@Lazy
public class OtcWorkorderServiceImpl extends SuperServiceImpl<OtcWorkorderMapper, OtcWorkorder> implements OtcWorkorderService {

    /**
     * 工单明细服务，用于操作工单明细信息
     */
    @Resource
    private OtcWorkorderDetailService otcWorkorderDetailService;

    /**
     * 工单库位服务，用于管理工单关联的库位信息
     */
    @Resource
    private OtcWorkorderBinLocationService otcWorkorderBinLocationService;

    /**
     * 拣货单服务，用于操作拣货单信息
     */
    @Resource
    private OtcPickingSlipService otcPickingSlipService;

    /**
     * 包裹服务，用于操作包裹信息
     */
    @Resource
    private OtcPackageService otcPackageService;

    /**
     * 拣货单基础服务
     */
    @Resource
    private PickingSlipService pickingSlipService;

    /**
     * OTC请求服务，用于获取请求信息
     */
    @Resource
    private OtcRequestService otcRequestService;

    /**
     * 库位明细服务，用于获取库位详细信息
     */
    @Resource
    private BinLocationDetailService binLocationDetailService;

    /**
     * 发货站配置服务，用于获取发货配置信息
     */
    @Resource
    private OtcShipStationConfigService shipStationConfigService;


    /**
     * 构建工单拣货信息
     * <p>
     * 根据拣货单拣货信息和工单明细，构建工单拣货详情列表。
     * 该方法使用产品分配工具，确保拣货数量按最早出货日期和工单编号进行合理分配。
     * </p>
     *
     * @param pickingSlipPickList       拣货单拣货信息列表
     * @param workOrderMap              工单映射（ID -> 工单）
     * @param detailGroupByProductIdMap 按产品ID分组的工单明细
     * @return 工单拣货明细视图对象列表
     * <p>
     * TODO: 考虑提取排序逻辑到单独的方法，提高代码可读性
     * TODO: 添加更丰富的拣货数量分配策略选项，支持不同业务场景
     */
    @NotNull
    private static List<OtcWorkorderDetailPickVO> buildPickList(
            List<OtcPickingSlipDetailPickVO> pickingSlipPickList,
            Map<Long, OtcWorkorder> workOrderMap,
            Map<Long, List<OtcWorkorderDetail>> detailGroupByProductIdMap) {
        // 先根据LastShip排序再根据工单RefNum排序 分配产品
        detailGroupByProductIdMap.forEach((key, detailList) -> detailList.sort(Comparator.<OtcWorkorderDetail, String>comparing(o -> {
            OtcWorkorder workOrder = workOrderMap.get(o.getOtcWorkorderId());
            return (workOrder != null) ? workOrder.getRequestSnapshotLastShipDate().toString() : null;
        }).thenComparing(o -> {
            OtcWorkorder workOrder = workOrderMap.get(o.getOtcWorkorderId());
            return (workOrder != null) ? workOrder.getRefNum() : null;
        })));

        return AllocationUtil.checkAndAllocationPickQty(
                pickingSlipPickList,
                detailGroupByProductIdMap,
                (detail, pick) -> buildWorkorderDetailPick(workOrderMap, pick, detail)
        );
    }

    /**
     * 构建工单明细拣货信息
     * <p>
     * 根据拣货单拣货信息和工单明细，构建单个工单明细拣货视图对象。
     * 设置产品信息、库位信息、拣货单关联信息以及日志相关信息。
     * </p>
     *
     * @param workOrderMap 工单映射（ID -> 工单）
     * @param pick         拣货单拣货明细视图对象
     * @param detail       工单明细
     * @return 工单明细拣货视图对象
     * <p>
     * TODO: 引用表信息设置可以提取为通用方法
     * TODO: 考虑添加数据验证，确保必要字段不为空
     */
    @NotNull
    private static OtcWorkorderDetailPickVO buildWorkorderDetailPick(Map<Long, OtcWorkorder> workOrderMap,
                                                                     OtcPickingSlipDetailPickVO pick,
                                                                     OtcWorkorderDetail detail) {

        Validate.notNull(workOrderMap, "workOrderMap can not null");
        Validate.notNull(pick, "pick can not null");
        Validate.notNull(detail, "detail can not null");

        OtcWorkorderDetailPickVO workorderPick = BeanUtil.copyNew(detail, OtcWorkorderDetailPickVO.class);
        workorderPick.setProductId(pick.getProductId());
        workorderPick.setProductVersionId(pick.getProductVersionId());

        // 设置库位相关信息
        workorderPick.setBinLocationId(pick.getBinLocationId());
        workorderPick.setBinLocationDetailId(pick.getBinLocationDetailId());
        workorderPick.setBinLocationDetailLockedId(pick.getBinLocationDetailLockedId());

        // 设置拣货单相关信息
        workorderPick.setOtcPickingSlipDetailId(pick.getId());
        workorderPick.setOtcPickingSlipId(pick.getOtcPickingSlipId());

        // 锁相关信息
        workorderPick.setRefTableId(workorderPick.getId());
        workorderPick.setRefTableRefNum(String.valueOf(workorderPick.getLineNum()));
        workorderPick.setRefTableName(OtcWorkorderDetail.class.getSimpleName());
        workorderPick.setRefTableShowName(OtcWorkorder.class.getSimpleName());
        workorderPick.setRefTableShowRefNum(Optional.ofNullable(workOrderMap.get(workorderPick.getOtcWorkorderId()))
                .map(OtcWorkorder::getRefNum)
                .orElse(String.valueOf(workorderPick.getOtcWorkorderId()))
        );

        // 库位日志信息
        RefTableBO logInfo = new RefTableBO();
        logInfo.setRefTableId(pick.getRefTableId());
        logInfo.setRefTableRefNum(pick.getRefTableRefNum());
        logInfo.setRefTableName(pick.getRefTableName());
        logInfo.setRefTableShowName(pick.getRefTableShowName());
        logInfo.setRefTableShowRefNum(pick.getRefTableShowRefNum());
        workorderPick.setPickLogInfo(logInfo);
        workorderPick.setPickedBeforeQty(detail.getPickedQty());
        return workorderPick;
    }

    /**
     * 修复并检查库位查询条件
     * <p>
     * 检查查询条件中的库位查询参数是否有效，如果无效则设置为null。
     * 库位查询仅在工单状态为"开始"状态且库位查询条件有效时启用。
     * </p>
     *
     * @param query 工单列表查询条件
     * @return 是否启用库位查询
     * <p>
     * TODO: 注释中提到的"enable设计好"需要完善实现
     * TODO: 考虑添加更详细的日志，说明为什么禁用库位查询
     */
    private static boolean checkAndFixBinLocationQuery(OtcWorkOrderListQuery query) {
        OtcWorkorderQuery otcWorkorderQuery = query.getOtcWorkorderQuery();
        BaseBinLocationQuery baseBinLocationQuery = query.getBinLocationQuery();
        boolean canBinLocationQuery = ObjectUtil.isNotNull(otcWorkorderQuery)
                // 仅New状态才开启
                && Objects.equals(otcWorkorderQuery.getOtcWorkorderStatus(), OtcWorkorderStatusEnum.BEGIN.getStatus())
                // 添加库位查询条件
                && ObjectUtil.isNotNull(baseBinLocationQuery)
                // 至少存在一个条件
                // todo: 则会个enable 设计好
                && baseBinLocationQuery.enable();
        if (!canBinLocationQuery) {
            // 未开启库位查询条件 库位查询条件设置null，空对象影响查询SQL
            query.setBinLocationQuery(null);
        }
        return canBinLocationQuery;
    }

    /**
     * 校验筛选构建查询条件
     * <p>
     * 验证工单筛选构建查询条件是否符合要求，特别是最晚发货日期的设置。
     * 如果没有选择发货日期范围且工单状态为"开始"状态，将自动设置默认的发货日期查询范围。
     * </p>
     *
     * @param workOrderListQuery 工单列表查询条件
     */
    private static void checkFilterBuildQuery(OtcWorkOrderListQuery workOrderListQuery) {
        //TODO: 当前默认设置7天期限的做法较为硬编码，考虑将其配置化
        //TODO: 添加更多查询条件的验证逻辑，确保查询效率
        OtcWorkorderQuery workorderQuery = workOrderListQuery.getOtcWorkorderQuery();
        // 非内部接口调用执行该逻辑
        LocalDateTime lastShipDateStart = workorderQuery.getRequestSnapshotLastShipDateStart();
        LocalDateTime lastShipDateEnd = workorderQuery.getRequestSnapshotLastShipDateEnd();
        boolean noSelectShipDate = ObjectUtil.isNull(lastShipDateStart)
                || ObjectUtil.isNull(lastShipDateEnd);
        // 未选择发货时间
        if (noSelectShipDate) {
            // throw new BusinessException("Please select LastShipDate");
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "lastShipDate"));
        }
        // 支持None/Processed
        List<String> prepWorkOderStatusSupportList = Arrays.asList(
                WorkOrderPrepStatusEnum.NONE.getStatus(),
                WorkOrderPrepStatusEnum.PROCESSED.getStatus()
        );
        List<String> prepTypeList = workorderQuery.getWorkorderPrepStatusList();
        if (ObjectUtil.isEmpty(prepTypeList)) {
            // throw new BusinessException("Please select WorkorderPrepStatus");
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "workorderPrepStatus"));
        }
        if (!prepWorkOderStatusSupportList.containsAll(prepTypeList)) {
            // throw new BusinessException("Only prepWorkOrderStatus such as None/Processed can be built");
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED, "prepWorkOrder", "NONE or PROCESSED", prepTypeList));
        }
        // 校验processType
        String processType = workorderQuery.getProcessType();
        Validate.isTrue(ProcessType.NORMAL.getType().equals(processType),
                String.format(ErrorMessages.STATUS_REQUIRED, "processType", ProcessType.NORMAL.getType(), processType)
        );
    }

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * 分页查询工单列表
     * <p>
     * 根据查询条件和分页参数获取工单列表，支持库位查询条件。
     * 查询结果会填充关联实体信息，如请求单、拣货单等。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的工单分页视图对象
     * <p>
     * TODO: 当存在库位条件且处理失败时直接返回空列表，可能需要提供更详细的错误信息
     * TODO: 考虑添加缓存机制，提高查询效率
     */
    @Override
    public PageData<OtcWorkorderPageVO> pageByQuery(PageSearch<OtcWorkOrderListQuery> search) {
        Page<OtcWorkorder> page = Conditions.page(search, entityClass);
        OtcWorkOrderListQuery condition = search.getCondition();
        // 存在库位条件的处理
        if (fixQueryByBinLocation(condition)) {
            return new PageData<>(Collections.emptyList(), page);
        }
        // 查询
        List<OtcWorkorderPageVO> dataList = mapper.listByQuery(condition.getOtcWorkorderQuery(), condition.getBinLocationQuery(), page);
        fillField(dataList);
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取工单详情
     * <p>
     * 该方法查询指定ID的工单信息，并转换为包含完整信息的视图对象。
     * 如果找不到对应ID的工单，则抛出业务异常。
     * </p>
     *
     * @param id 工单ID
     * @return 工单详情视图对象
     * @throws BusinessException 如果找不到指定ID的工单
     */
    @Override
    public OtcWorkorderVO detailById(Long id) {
        //TODO: 返回的工单详情可能缺少关联的明细信息，考虑添加明细信息的填充
        OtcWorkorder entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in OtcWorkorder");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtcWorkorder", id));
        }
        return buildOtcWorkorderVO(entity);
    }

    /**
     * 根据参考编号获取工单详情
     * <p>
     * 该方法查询指定参考编号的工单信息，并转换为包含完整信息的视图对象。
     * 如果找不到对应参考编号的工单，则抛出业务异常。
     * </p>
     *
     * @param refNum 工单参考编号
     * @return 工单详情视图对象
     * @throws BusinessException 如果找不到指定参考编号的工单
     */
    @Override
    public OtcWorkorderVO detailByRefNum(String refNum) {
        OtcWorkorder entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in OtcWorkorder");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtcWorkorder", "refNum", refNum));
        }

        return buildOtcWorkorderVO(entity);
    }

    /**
     * 统计筛选构建的拣货单数量
     * <p>
     * 该方法根据查询条件，统计可构建拣货单的工单数量。
     * 执行前会检查查询条件是否有效，如最晚发货日期、预处理状态等。
     * </p>
     *
     * @param query 工单列表查询条件
     * @return 筛选构建拣货单数量视图对象
     * @throws BusinessException 如果查询条件不符合要求
     */
    @Override
    public OtcWorkorderFilterBuildPickingSlipCountVO filterBuildPickingSlipCount(OtcWorkOrderListQuery query) {
        //TODO: 当存在库位条件且处理失败时直接返回0，可能需要提供更详细的错误信息
        // 检查查询条件
        checkFilterBuildQuery(query);
        OtcWorkorderFilterBuildPickingSlipCountVO countVO = new OtcWorkorderFilterBuildPickingSlipCountVO();
        // 存在库位条件的处理
        if (fixQueryByBinLocation(query)) {
            countVO.setFilterSum(0L);
            return countVO;
        }
        // 统计
        Long filterSum = mapper.filterBuildPickingSlipCount(query.getOtcWorkorderQuery(), query.getBinLocationQuery());
        // 构建返回结果
        countVO.setFilterSum(filterSum);
        return countVO;
    }

    /**
     * 根据ID获取工单基本信息（不包含明细）
     * <p>
     * 该方法查询指定ID的工单基本信息，不包含工单明细数据。
     * 主要用于只需要工单基本信息的场景，减少数据传输量。
     * </p>
     *
     * @param id 工单ID
     * @return 工单基本信息视图对象，如果找不到则返回null
     * <p>
     * TODO: 当工单不存在时返回null，可能导致调用方空指针异常，考虑抛出异常或返回空对象
     */
    @Override
    public OtcWorkorderNoDetailVO noDetailById(Long id) {
        Map<Long, OtcWorkorderNoDetailVO> noDetails = noDetailByIds(Collections.singletonList(id));
        return ObjectUtil.isEmpty(noDetails) ? null : noDetails.get(id);
    }

    /**
     * 批量获取工单基本信息（不包含明细）
     * <p>
     * 该方法批量查询指定ID列表的工单基本信息，不包含工单明细数据。
     * 同时会填充关联的请求单信息，以提供更完整的数据视图。
     * </p>
     *
     * @param workOrderIds 工单ID列表
     * @return 以工单ID为键，工单基本信息为值的Map
     * <p>
     * TODO: 请求单信息的填充可能导致大量数据查询，考虑添加懒加载机制
     */
    @Override
    public Map<Long, OtcWorkorderNoDetailVO> noDetailByIds(List<Long> workOrderIds) {
        if (ObjectUtil.isEmpty(workOrderIds)) {
            return Collections.emptyMap();
        }
        // 查询工单列表
        List<OtcWorkorder> workOrders = lambdaQuery()
                .in(IdModel::getId, workOrderIds)
                .list();
        List<OtcWorkorderNoDetailVO> noDetailList = BeanUtil.copyNew(workOrders, OtcWorkorderNoDetailVO.class);
        // 填充Request 对象
        List<Long> requestIds = StreamUtils.distinctMap(noDetailList, OtcWorkorderNoDetailVO::getOtcRequestId);
        Map<Long, OtcRequestVO> requestMap = otcRequestService.detailByIds(requestIds);
        noDetailList.forEach(obj -> obj.setOtcRequest(requestMap.get(obj.getOtcRequestId())));

        return StreamUtils.toMap(noDetailList, BaseSuperVO::getId);
    }

    /**
     * 根据查询条件筛选可构建拣货单的工单
     * <p>
     * 该方法根据拣货单筛选构建查询条件，获取符合条件的工单列表。
     * 支持设置最大可构建数量限制，确保拣货单构建的合理性。
     * </p>
     *
     * @param query 拣货单筛选构建查询条件
     * @return 符合条件的工单列表
     * @throws BusinessException 如果查询条件不符合要求
     */
    @Override
    public List<OtcWorkorder> filterBuildByQuery(OtcPickingSlipFilterBuildQuery query) {
        //  TODO: 当前实现没有校验库存，可能导致构建的拣货单无法执行，考虑添加库存校验
        OtcWorkOrderListQuery workOrderListQuery = query.getFilter();
        // 检查查询条件
        checkFilterBuildQuery(workOrderListQuery);

        // 限制最大工单数
        Integer maxWorkOrderCount = query.getStrategy().getMaxCanBuildSum();
        PageSearch<OtcWorkOrderListQuery> searchPage = new PageSearch<>(1, maxWorkOrderCount, workOrderListQuery);
        // 修复库位查询条件
        checkAndFixBinLocationQuery(workOrderListQuery);
        // 这里查询没有校验库存
        List<OtcWorkorderPageVO> workorderList = mapper.listByQuery(workOrderListQuery.getOtcWorkorderQuery(),
                workOrderListQuery.getBinLocationQuery(), Conditions.page(searchPage, entityClass));
        return BeanUtil.copyNew(workorderList, OtcWorkorder.class);
    }

    /**
     * 根据拣货单ID和产品ID获取关联工单及明细
     * <p>
     * 该方法查询指定拣货单下与特定产品相关的工单及其明细信息。
     * 用于拣货操作中快速定位待拣货的工单和产品明细。
     * </p>
     *
     * @param pickingSlipId 拣货单ID
     * @param productId     产品ID
     * @return 包含工单及相关产品明细的视图对象列表
     * <p>
     * TODO: 当不存在匹配条件的工单时直接返回空列表，可能需要添加日志记录
     * TODO: 考虑添加排序逻辑，优先显示紧急出货的工单
     */
    @Override
    public List<OtcWorkorderWithDetailVO> getByPickingSlipIdAndProductId(Long pickingSlipId, Long productId) {
        // 获取工单
        List<OtcWorkorder> workOrderList = lambdaQuery()
                .eq(OtcWorkorder::getOtcPickingSlipId, pickingSlipId)
                .list();
        // 不存在直接返回空对象
        if (ObjectUtil.isEmpty(workOrderList)) {
            return Collections.emptyList();
        }

        // 工单集合
        List<Long> workOrderIdList = StreamUtils.distinctMap(workOrderList, IdModel::getId);
        // 获取明细
        Map<Long, List<OtcWorkorderDetail>> detailList = otcWorkorderDetailService.groupByOtcWorkOrderIdList(workOrderIdList);
        // 根据产品筛选
        Map<Long, List<OtcWorkorderDetail>> hasProductWorkOrderDetailMap = detailList.values().stream()
                .flatMap(Collection::stream)
                // 存在产品则进行过滤
                .filter(obj -> productId == null || obj.getProductId().equals(productId))
                .collect(Collectors.groupingBy(OtcWorkorderDetail::getOtcWorkorderId));
        // 构建返回对象
        return workOrderList.stream()
                .map(obj -> BeanUtil.copyNew(obj, OtcWorkorderWithDetailVO.class))
                .filter(obj -> hasProductWorkOrderDetailMap.containsKey(obj.getId()))
                .peek(obj -> obj.setDetailList(BeanUtil.copyNew(hasProductWorkOrderDetailMap.get(obj.getId()), OtcWorkorderDetailVO.class)))
                .sorted(Comparator.comparing(OtcWorkorderWithDetailVO::getRefNum))
                .toList();
    }

    /**
     * 将工单状态更新为已发货
     * <p>
     * 该方法检查指定工单列表下的所有包裹是否已全部发货，
     * 如果是，则将工单状态更新为已发货，并触发相关请求单的状态更新。
     * </p>
     *
     * @param workOrderIdList 工单ID列表
     *                        <p>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shipped(List<Long> workOrderIdList) {
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return;
        }
        // 准备发货的工单
        List<OtcWorkorder> workorderList = this.findCanShippedList(workOrderIdList);
        // 获取所有包裹该工单下
        List<OtcPackage> packageList = otcPackageService.listByWorkOrderIdList(workOrderIdList).stream()
                .filter(obj -> !Objects.equals(obj.getPackageStatus(), OtcPackageStatusEnum.CANCELLED.getStatus()))
                .toList();
        // 每个工单下的包裹
        Map<Long, List<OtcPackage>> packageGroupByWorkMap = StreamUtils.groupBy(packageList, OtcPackage::getOtcWorkorderId);
        // 获取可发货工单
        List<OtcWorkorder> shippedList = workorderList.stream()
                .filter(obj -> packageGroupByWorkMap.containsKey(obj.getId()))
                // 检查是否所有包裹都已发货
                .filter(wk -> packageGroupByWorkMap.get(wk.getId()).stream()
                        .allMatch(pkg -> Objects.equals(pkg.getPackageStatus(), OtcPackageStatusEnum.SHIPPED.getStatus())))
                // 更新工单状态
                .peek(obj -> obj.setOtcWorkorderStatus(OtcWorkorderStatusEnum.SHIPPED.getStatus()))
                .toList();

        Validate.isTrue(updateBatch(shippedList) == shippedList.size(),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Failed to update all work order statuses"));

        // 工单: ReadyToShip -> Shipped 日志
        shippedList.forEach(obj -> OtcWorkOrderAuditLogHelper.recordLog(obj,
                Objects.equals(obj.getOtcWorkorderStatus(), OtcWorkorderStatusEnum.SHIPPED.getStatus()) ? WorkorderLogConstant.SHIPPED_DESCRIPTION : StringPool.EMPTY,
                null
        ));

        // 设置FinishQty
        var shippedPkgDetails = otcPackageService.findShippedPkgDetails(packageList);

        // 工单id + 产品id分组
        var pkgMap = StreamUtils.toMap(packageList, IdModel::getId);
        final var unionKey = "{}:{}";
        var finishProductQtyMap = shippedPkgDetails.stream()
                .collect(Collectors.groupingBy(obj -> StringUtil.format(unionKey,
                                pkgMap.get(obj.getOtcPackageId()).getOtcWorkorderId(), obj.getProductId()),
                        Collectors.summingInt(OtcPackageDetail::getQty)
                ));

        var workorderIds = StreamUtils.distinctMap(workorderList, IdModel::getId);
        var wkDetails = otcWorkorderDetailService.listByWorkOrderIds(workorderIds);
        // 设置FinishQty
        wkDetails.forEach(obj -> obj.setFinishQty(finishProductQtyMap.getOrDefault(
                StringUtil.format(unionKey, obj.getOtcWorkorderId(), obj.getProductId()), 0)
        ));

        Validate.isTrue(otcWorkorderDetailService.updateBatch(wkDetails) == wkDetails.size(),
                "Update WorkOrderDetail FinishQty Failed"
        );

        // 请求单标记Shipped
        List<Long> requestIdList = StreamUtils.distinctMap(shippedList, OtcWorkorder::getOtcRequestId);
        otcRequestService.shipped(requestIdList);
    }

    /**
     * 根据请求单ID获取工单列表
     * <p>
     * 该方法查询指定请求单下的所有工单。
     * </p>
     *
     * @param otcRequestId 请求单ID
     * @return 工单列表
     * <p>
     * TODO: 缺少结果排序，可能导致每次查询的结果顺序不一致
     * TODO: 考虑返回分页结果，避免大量数据返回影响性能
     */
    @Override
    public List<OtcWorkorder> listByRequestId(Long otcRequestId) {
        return lambdaQuery().eq(OtcWorkorder::getOtcRequestId, otcRequestId).list();
    }

    @Override
    public List<RefNumVO> listRefNumByRequestId(Long requestId) {
        return lambdaQuery().eq(OtcWorkorder::getOtcRequestId, requestId)
                .list()
                .stream()
                .map(this::convertToRefNumVO)
                .toList();
    }

    /**
     * 将工单实体转换为RefNumVO
     */
    private RefNumVO convertToRefNumVO(OtcWorkorder obj) {
        RefNumVO vo = new RefNumVO();
        vo.setId(obj.getId());
        vo.setRefNum(obj.getRefNum());
        return vo;
    }

    @Override
    public List<OtcWorkorder> listByRequestIds(List<Long> otcRequestId) {
        if (ObjectUtil.isEmpty(otcRequestId)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(OtcWorkorder::getOtcRequestId, otcRequestId).list();
    }

    /**
     * 处理包裹准备发货
     * <p>
     * 该方法处理包裹状态变更为准备发货时的工单状态更新。
     * 包括更新工单明细的准备发货数量，以及处理用户指定包裹的特殊情况。
     * </p>
     *
     * @param pkg 包裹实体
     * @throws IllegalArgumentException 如果包裹为空、状态不正确或找不到对应工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void readyToShip(OtcPackage pkg) {
        //TODO: 存在逻辑错误，检查工单状态的条件反了，应该检查工单状态是否不等于READY_TO_SHIP
        Validate.notNull(pkg, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "package"));
        Validate.isTrue(Objects.equals(pkg.getPackageStatus(),
                        OtcPackageStatusEnum.READY_TO_SHIP.getStatus()),
                String.format(ErrorMessages.STATUS_REQUIRED, "Package", "READY_TO_SHIP", pkg.getPackageStatus()));

        OtcWorkorder workOrder = getById(pkg.getOtcWorkorderId());
        Validate.notNull(workOrder, String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtcWorkorder", "package refNum", pkg.getRefNum()));

        Validate.isTrue(!Objects.equals(workOrder.getOtcWorkorderStatus(), OtcWorkorderStatusEnum.READY_TO_SHIP.getStatus()),
                String.format(ErrorMessages.STATUS_INVALID_OPERATION, "update", "OtcWorkorder", workOrder.getOtcWorkorderStatus())
        );

        // 获取工单详情
        List<OtcWorkorderDetail> workOrderDetailList = otcWorkorderDetailService.listByWorkOrderId(workOrder.getId());
        // 获取包裹明细
        List<OtcPackageDetail> packageDetailList = otcPackageService.afterProcessingMultiBox(pkg);

        // 用户指定包裹，更新工单详情打包数量
        if (workOrder.getRequestSnapshotProvideShippingLabelFlag()) {
            updatePackQty(workOrderDetailList, packageDetailList);
        }

        // 分配准备发货数量
        // 根据产品分组
        Map<Long, List<OtcWorkorderDetail>> detailGroupByProductMap = workOrderDetailList.stream()
                .collect(Collectors.groupingBy(OtcWorkorderDetail::getProductId));

        List<OtcWorkorderDetail> needUpdateDetailList = new ArrayList<>();
        packageDetailList
                .stream()
                .filter(obj -> ObjectUtil.nullToDefault(obj.getQty(), 0) > 0)
                .forEach(packageDetail -> {
                    // 单前包裹产品数量
                    int remainQty = ObjectUtil.nullToDefault(packageDetail.getQty(), 0);
                    // 获取工单明细
                    List<OtcWorkorderDetail> workorderDetailList = detailGroupByProductMap.getOrDefault(packageDetail.getProductId(), Collections.emptyList());
                    for (OtcWorkorderDetail workOrderDetail : workorderDetailList) {
                        // 可发货数量为空
                        int canShippedQty = workOrderDetail.getPickedQty() - workOrderDetail.getReadyToShipQty();
                        if (canShippedQty == 0) {
                            continue;
                        }
                        int currentQty = Math.min(canShippedQty, remainQty);
                        workOrderDetail.setReadyToShipQty(workOrderDetail.getReadyToShipQty() + currentQty);
                        needUpdateDetailList.add(workOrderDetail);
                        remainQty -= currentQty;
                    }
                    if (remainQty != 0) {
                        // throw new BusinessException("Package detail qty is not match");
                        throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Package detail quantity does not match work order detail quantity"));
                    }

                });
        // 没有ReadyToShipped 的数量
        if (ObjectUtil.isEmpty(needUpdateDetailList)) {
            return;
        }
        int updateCount = otcWorkorderDetailService.updateBatch(needUpdateDetailList);
        Validate.isTrue(updateCount == needUpdateDetailList.size(),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Failed to update all work order details"));
        // 全部准备发货
        boolean isAllReadyToShipped = ObjectUtil.isEmpty(workOrderDetailList)
                || workOrderDetailList.stream()
                .allMatch(obj -> Objects.equals(obj.getQty(), obj.getReadyToShipQty()));

        if (isAllReadyToShipped) {
            workOrder.setOtcWorkorderStatus(OtcWorkorderStatusEnum.READY_TO_SHIP.getStatus());
            Validate.isTrue(super.update(workOrder) == 1,
                    String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Failed to update work order to READY_TO_SHIP status"));
            // 请求单标记ReadyToShip
            otcRequestService.readyToShip(workOrder.getOtcRequestId());

            // 工单:  ReadyToShip 日志
            OtcWorkOrderAuditLogHelper.recordLog(workOrder, WorkorderLogConstant.READY_TO_SHIP_DESCRIPTION, null);
        }

    }

    /**
     * 获取工单字段的下拉选项列表
     * <p>
     * 该方法根据查询条件，获取工单中指定字段的唯一值列表，
     * 用于构建前端下拉选择框的选项数据。
     * </p>
     *
     * @param query 包含字段名列表和筛选条件的查询对象
     * @return 下拉选项视图对象列表
     * <p>
     * TODO: 使用通用的DropListUtil处理，可能难以支持特定业务场景的定制化需求
     * TODO: 考虑添加缓存机制，提高频繁查询下拉选项的性能
     */
    @Override
    public List<DropProVO> distinctValuePro(OtcWorkorderQuery query) {
        return DropListUtil.dropProList(
                query.getColumnNameList(),
                OtcWorkorder.class,
                columnInfos -> mapper.dropProList(columnInfos, query)
        );
    }

    /**
     * 执行工单拣货操作
     * <p>
     * 该方法根据拣货上下文对象，更新工单和工单明细的拣货状态。
     * 包括释放库位锁、移动库存到待出库区域、更新拣货数量等操作。
     * </p>
     *
     * @param context 拣货上下文对象，包含拣货单和拣货明细信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pick(OtcPickingSlipPickContextVO context) {
        //TODO: 库存移动和库位锁释放逻辑可能应该单独抽取为服务
        OtcPickingSlip slip = context.getPickingSlip();
        // 工单
        Map<Long, OtcWorkorder> workOrderMap = this.findCanPickListByPickingSlip(slip);

        // 可能取消了工单
        Validate.notEmpty(workOrderMap,
                "{}, Pick Workorder is not found, Please check workorder status",
                slip.refNumLog()
        );

        List<Long> workOrderIdList = workOrderMap.keySet().stream().toList();

        // 产品工单详情分组
        Map<Long, List<OtcWorkorderDetail>> detailGroupByProductIdMap = otcWorkorderDetailService.groupByOtcWorkOrderIdList(workOrderIdList)
                .values()
                .stream()
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(OtcWorkorderDetail::getProductId));

        List<OtcPickingSlipDetailPickVO> pickingSlipPickList = context.getPickAfterDetailList();
        // 构建工单拣货信息
        List<OtcWorkorderDetailPickVO> workOrderDetailPickUpdateList = buildPickList(pickingSlipPickList, workOrderMap, detailGroupByProductIdMap);

        // 释放库位锁、库存移到ReadyToGo
        pickingSlipService.otcMoveBinLocationInventoryToReadyToGo(workOrderDetailPickUpdateList);

        // 更新拣货数量
        this.updatePickedQtyAndPicked(workOrderMap, detailGroupByProductIdMap, workOrderDetailPickUpdateList);

        // 绑定工单拣货信息至上下文
        context.setWorkOrderPickAfterDetailList(workOrderDetailPickUpdateList);
    }

    /**
     * 构建基础工作订单对象
     * <p>
     * 该方法基于请求单信息构建基础工单对象，设置工单类型和生成工单编号。
     * </p>
     *
     * @param request   请求单视图对象
     * @param orderType 工单类型枚举
     * @return 构建的工单实体对象
     * <p>
     * TODO: 方法命名不够准确，实际是创建新的工单对象而非构建现有对象
     */
    @Override
    public OtcWorkorder buildBaseWorkOrder(final OtcRequestVO request, final OtcOrderTypeEnum orderType) {
        OtcWorkorder workOrder = buildWorkOrder(request);
        workOrder.setOrderType(orderType.getStatus());
        workOrder.setRefNum(getOtcWorkOrderRefNum());
        workOrder.setId(IdWorker.getId());
        return workOrder;
    }

    /**
     * 生成工单参考编号
     * <p>
     * 该方法生成工单的唯一参考编号，用于标识工单。
     * </p>
     *
     * @return 生成的工单参考编号
     * <p>
     * TODO: 编号生成依赖通用工具类，考虑添加业务特定的前缀或规则
     */
    @Override
    public String getOtcWorkOrderRefNum() {
        return FormatUtil.generateRefNum(RefNumTypeEnum.OTC_WORK_ORDER);
    }

    /**
     * 根据请求单构建工单
     * <p>
     * 该方法基于请求单信息构建完整的工单对象，包括设置请求单快照信息、
     * 工单状态、包裹构建类型等。
     * </p>
     *
     * @param request 请求单视图对象
     * @return 构建的工单实体对象
     * <p>
     * TODO: 大量的字段映射可以考虑使用映射工具如MapStruct简化
     * TODO: 工单状态和包裹构建类型的逻辑可以提取为单独的方法
     */
    @Override
    public OtcWorkorder buildWorkOrder(OtcRequestVO request) {
        // 将 OTCRequest 转换为 OTCWorkOrder（请根据实际情况实现）
        OtcWorkorder workOrder = new OtcWorkorder();
        workOrder.setOtcRequestId(request.getId());
        workOrder.setOtcPickingSlipId(null);
        workOrder.setRequestSnapshotShipExpressFlag(request.getShipExpressFlag());
        workOrder.setShipMethod(request.getShipMethod());
        workOrder.setShipCarrier(request.getShipCarrier());
        workOrder.setRequestSnapshotLastShipDate(request.getLastShipDate());
        workOrder.setRequestSnapshotInsuranceAmountAmount(request.getInsuranceAmountAmount());
        workOrder.setRequestSnapshotSignatureType(request.getSignatureType());
        workOrder.setRequestSnapshotOrderType(request.getOrderType());
        workOrder.setRequestSnapshotHasCusShipRequire(request.getHasCusShipRequire());
        workOrder.setRequestSnapshotShipMethod(request.getShipMethod());
        workOrder.setRequestSnapshotProvideShippingLabelFlag(request.getProvideShippingLabelFlag());
        workOrder.setOtcWorkorderStatus(OtcWorkorderStatusEnum.BEGIN.getStatus());

        // 这个是根据Package 来，有Package 的话就走Request，没有的话就走Warehouse
        if (request.getProvideShippingLabelFlag()) {
            workOrder.setBuildShipPackageType(OtcBuildShipPackageEnum.BY_REQUEST.getStatus());
        } else {
            workOrder.setBuildShipPackageType(OtcBuildShipPackageEnum.BY_WAREHOUSE.getStatus());
        }
        workOrder.setRequestSnapshotNote(request.getNote());
        workOrder.setRequestSnapshotRefNum(request.getRefNum());
        workOrder.setRequestSnapshotRequestRefNum(request.getRequestRefNum());
        workOrder.setRequestSnapshotShipCarrier(request.getShipCarrier());
        workOrder.setRequestSnapshotTransactionPartnerId(request.getTransactionPartnerId());
        workOrder.setRequestSnapshotChannel(request.getChannel());
        workOrder.setRequestSnapshotInsuranceAmountCurrency(request.getInsuranceAmountCurrency());
        workOrder.setRequestSnapshotShipApiProfileRefNum(request.getShipApiProfileRefNum());
        // 这个是根据Prep来设置
        workOrder.setWorkorderPrepStatus(OtcWorkOrderPrepWorkOrderTypeEnum.NONE.getStatus());
        // workOrder.setWorkorderPrepType();
        // workOrder.setPickToStation();
        // workOrder.setDeletedNote();
        // workOrder.setTenantId();
        // workOrder.setWarehouseId();
        // workOrder.setRefNum();
        // workOrder.setNote();
        // workOrder.setLockedBefore();
        // workOrder.setCreateBy();
        // workOrder.setUpdateBy();
        // workOrder.setCreateTime();
        // workOrder.setUpdateTime();
        // workOrder.setRemoveFlag();
        // workOrder.setVersion();
        // workOrder.setId();

        // 获取 ShipStation
        String pickToStation = shipStationConfigService.getShipStation(workOrder.getShipCarrier(), workOrder.getShipMethod());
        workOrder.setPickToStation(pickToStation);

        return workOrder;
    }

    /**
     * 按请求单ID分组获取工单
     * <p>
     * 该方法根据提供的请求单ID列表查询关联的工单，
     * 并按请求单ID分组返回，便于批量处理。
     * </p>
     *
     * @param requestIdList 请求单ID列表
     * @return 以请求单ID为键，工单列表为值的Map
     * <p>
     * TODO: 查询结果没有进行排序，可能影响业务处理的一致性
     */
    @Override
    public Map<Long, List<OtcWorkorder>> groupByRequestIdList(List<Long> requestIdList) {
        if (ObjectUtil.isEmpty(requestIdList)) {
            return Collections.emptyMap();
        }
        return lambdaQuery()
                .in(OtcWorkorder::getOtcRequestId, requestIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OtcWorkorder::getOtcRequestId));
    }

    /**
     * 检查指定库位是否存在未完成的工单
     * <p>
     * 该方法检查指定库位ID是否有关联的未完成工单（非已发货状态），
     * 用于判断库位是否可以进行其他操作。
     * </p>
     *
     * @param binLocationId 库位ID
     * @return 存在未完成工单时返回true，否则返回false
     * <p>
     * TODO: 硬编码已发货状态列表，考虑使用枚举或常量定义
     * TODO: 查询逻辑可能需要更复杂的条件，例如考虑工单的处理类型
     */
    @Override
    public Boolean existUnfinishedOrder(Long binLocationId) {
        List<String> list = Lists.arrayList(OtcWorkorderStatusEnum.SHIPPED.getStatus());
        Long count = mapper.existUnfinishedOrder(binLocationId, list);
        return count > 0;
    }

    /**
     * 检查工单列表是否都处于可构建状态
     * <p>
     * 该方法验证指定工单ID列表中的工单是否都处于"开始"状态，
     * 只有处于"开始"状态的工单才能参与构建拣货单操作。
     * </p>
     *
     * @param workorderIdList 工单ID列表
     * @throws IllegalArgumentException 如果列表为空或存在非"开始"状态的工单
     *                                  <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  TODO: 异常信息中直接拼接所有不符合条件的工单编号，可能导致消息过长
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  TODO: 考虑使用批处理方式，分批验证大量工单
     */
    @Override
    public void checkNoNormalFilterBuild(List<Long> workorderIdList) {
        Validate.notEmpty(workorderIdList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "workorderIdList"));

        // Normal FilterBuild过的工单
        List<OtcWorkorder> workorderFilterBuildList = lambdaQuery()
                .select(IdModel::getId, OtcWorkorder::getRefNum)
                .ne(OtcWorkorder::getOtcWorkorderStatus, OtcWorkorderStatusEnum.BEGIN.getStatus())
                .in(IdModel::getId, workorderIdList)
                .list()
                .stream()
                .toList();

        // 存在非New状态工单则抛出异常
        Validate.isTrue(ObjectUtil.isEmpty(workorderFilterBuildList),
                "WorkOrder: {} have already been FilterBuild", workorderFilterBuildList.stream()
                        .map(OtcWorkorder::getRefNum)
                        .collect(Collectors.joining(StringPool.COMMA))
        );
    }

    /**
     * 根据拣货单ID列表获取工单列表
     * <p>
     * 该方法查询与指定拣货单ID列表关联的所有工单。
     * 如果ID列表为空，则返回空列表。
     * </p>
     *
     * @param pickingSlipIdList 拣货单ID列表
     * @return 关联的工单列表
     * <p>
     * TODO: 查询结果没有进行排序，可能影响下游处理的一致性
     * TODO: 考虑添加工单状态等条件，过滤掉不需要的工单
     */
    @Override
    public List<OtcWorkorder> listByPickingSlipIds(List<Long> pickingSlipIdList) {
        return ObjectUtil.isEmpty(pickingSlipIdList)
                ? Collections.emptyList()
                : lambdaQuery().in(OtcWorkorder::getOtcPickingSlipId, pickingSlipIdList).list();
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 构建OTC工单视图对象
     * <p>
     * 该方法将OTC工单实体对象转换为包含完整信息的视图对象。
     * 包括填充关联的请求单信息和工单明细信息。
     * </p>
     *
     * @param entity OTC工单实体对象
     * @return 包含详细信息的OTC工单视图对象，如果实体为空则返回null
     * <p>
     * TODO: 注释中提到需要填充Payment信息，但尚未实现，需要加以实现
     * TODO: 构建过程中可能会执行多次数据库查询，考虑批量查询优化
     */
    private OtcWorkorderVO buildOtcWorkorderVO(OtcWorkorder entity) {
        // 返回包含详细信息的OTC工单VO对象
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        OtcWorkorderVO vo = Converters.get(OtcWorkorderConverter.class).toVO(entity);
        // 请求单
        OtcRequestVO otcRequest = otcRequestService.detailById(entity.getOtcRequestId());
        if (ObjectUtil.isNotNull(otcRequest)) {
            vo.setOtcRequest(BeanUtil.copyNew(otcRequest, OtcWorkOrderRequestVO.class));
        }
        // 详情
        vo.setDetailList(BeanUtil.copyNew(otcWorkorderDetailService.listByWorkOrderId(entity.getId()), OtcWorkorderDetailVO.class));
        // TODO Payment填充，待开发
        return vo;
    }

    /**
     * 填充工单分页视图对象的关联信息
     * <p>
     * 该方法为工单分页视图对象列表填充关联的拣货单信息。
     * 通过批量查询的方式提高性能。
     * </p>
     *
     * @param dataList 需要填充的工单分页视图对象列表
     *                 <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 TODO: 方法名过于简单，不能清晰表达方法的功能，考虑更改为更具描述性的名称
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 TODO: 只填充了拣货单信息，可能需要填充更多关联信息，如请求单、包裹等
     */
    private void fillField(List<OtcWorkorderPageVO> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }
        List<Long> pickingSlipIdList = StreamUtils.distinctMap(dataList, OtcWorkorderPageVO::getOtcPickingSlipId);
        Map<Long, RefNumVO> pickingSlipMap = otcPickingSlipService.refNumMapByIds(pickingSlipIdList);
        dataList.forEach(obj -> obj.setPickingSlip(pickingSlipMap.get(obj.getOtcPickingSlipId())));
    }

    /**
     * 根据库位查询条件修正工单查询条件
     * <p>
     * 该方法检查并修正工单查询条件中的库位查询参数。
     * 如果启用了库位查询，会检查库位库存，并将查询条件替换为有库存的工单ID列表。
     * </p>
     *
     * @param query 工单列表查询条件
     * @return 如果需要直接返回空结果则返回true，否则返回false
     * <p>
     * TODO: 方法命名不够准确，实际是根据库位查询条件验证库存并修正查询条件
     * TODO: 方法逻辑复杂，返回值含义不明确，考虑重构或拆分为多个方法
     */
    private boolean fixQueryByBinLocation(OtcWorkOrderListQuery query) {
        boolean canBinLocationQuery = checkAndFixBinLocationQuery(query);
        // 未开启直接返回
        if (!canBinLocationQuery) {
            return false;
        }
        // 开启工单库存校验
        List<OtcWorkorderPageVO> dataList = mapper.listByQuery(query.getOtcWorkorderQuery(), query.getBinLocationQuery());
        // The previous check ensures that only workOrderIds for which a matching BinLocation exists will be considered
        // Do not do additional inventory validation as the inventory in the bin may not be related to a workOrder.
        List<Long> hasStockWorkOrderIdList = listCheckWorkOrderInStock(dataList, query.getBinLocationQuery());
        if (ObjectUtil.isEmpty(hasStockWorkOrderIdList)) {
            return true;
        }

        // 清空查询条件 使用工单id查询
        query.setBinLocationQuery(null);
        query.setOtcWorkorderQuery(null);
        OtcWorkorderQuery fixWorkOrderQuery = new OtcWorkorderQuery();
        fixWorkOrderQuery.setIdList(hasStockWorkOrderIdList);
        query.setOtcWorkorderQuery(fixWorkOrderQuery);

        // 查询
        return false;
    }

    /**
     * 校验工单库存
     * <p>
     * 该方法检查工单列表中的产品是否在指定库位有足够的库存。
     * </p>
     *
     * @param dataList         工单分页视图对象列表
     * @param binLocationQuery 库位查询条件
     * @return 有足够库存的工单ID列表
     * <p>
     * TODO: 方法参数和返回值的注释不够准确，需要更明确地说明参数和返回值的含义
     * TODO: 方法逻辑过于复杂，考虑拆分为更小的方法，提高可维护性
     */
    private List<Long> listCheckWorkOrderInStock(List<OtcWorkorderPageVO> dataList, BaseBinLocationQuery binLocationQuery) {

        // 校验库存
        List<Long> workOrderIdList = StreamUtils.distinctMap(dataList, OtcWorkorderPageVO::getId);
        // 详情按工单分组
        Map<Long, List<OtcWorkorderDetail>> detailGroupByWorkOrderMap = otcWorkorderDetailService.groupByOtcWorkOrderIdList(workOrderIdList);
        // 产品id集合
        List<Long> productIdList = detailGroupByWorkOrderMap.values().stream()
                .flatMap(Collection::stream)
                .map(OtcWorkorderDetail::getProductId)
                .distinct()
                .toList();

        // 产品可用库存映射
        Map<Long, Integer> productAvailableStockMap = binLocationDetailService.realAvailableInStockGroupByProductId(binLocationQuery, productIdList);
        return StreamUtils.filterHasProductStock(StreamUtils.sortByVOList(detailGroupByWorkOrderMap, dataList), productAvailableStockMap);
    }

    /**
     * 更新包裹打包数量
     * <p>
     * 该方法验证并更新工单明细的打包数量，确保包裹明细中的产品数量不超过工单中已拣货但未打包的数量。
     * 同时分配打包数量到各个工单明细中。
     * </p>
     *
     * @param workOrderDetailList 工单明细列表
     * @param packageDetailList   包裹明细列表
     * @throws BusinessException 如果包裹需要的产品数量超过可打包数量
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           TODO: 方法逻辑复杂，考虑拆分为验证逻辑和更新逻辑两部分
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           TODO: 错误信息构建过程复杂，可以提取为单独的方法
     */
    private void updatePackQty(List<OtcWorkorderDetail> workOrderDetailList, List<OtcPackageDetail> packageDetailList) {
        Map<Long, Integer> productCanPackedQtyMap = workOrderDetailList.stream()
                .filter(obj -> obj.getPickedQty() > obj.getPackedQty())
                .collect(Collectors.groupingBy(OtcWorkorderDetail::getProductId,
                        Collectors.mapping(detail -> detail.getPickedQty() - detail.getPackedQty(), Collectors.summingInt(Integer::intValue))
                ));
        Map<Long, Integer> productNeedPackedQtyMap = packageDetailList.stream()
                .collect(Collectors.groupingBy(OtcPackageDetail::getProductId,
                        Collectors.mapping(OtcPackageDetail::getQty, Collectors.summingInt(Integer::intValue))
                ));

        List<Pair<Long, Integer>> notEnoughList = productNeedPackedQtyMap.entrySet()
                .stream()
                .map(entry -> {
                    Long productId = entry.getKey();
                    int needPackedQty = entry.getValue();
                    int canPackedQty = productCanPackedQtyMap.getOrDefault(productId, 0);
                    return Pair.of(productId, needPackedQty - canPackedQty);
                })
                .filter(pair -> pair.getValue() > 0)
                .toList();

        // 产品没有填充完包裹
        if (ObjectUtil.isNotEmpty(notEnoughList)) {

            // throw new BusinessException(notEnoughList.stream()
            //         .map(pair -> StringUtil.format("Product {} need packed qty is not enough, There are still {} no picked",
            //                 Optional.ofNullable(ProductCacheUtil.getById(pair.getKey()))
            //                         .map(ProductCache::getSupplierSku)
            //                         .orElse(StringPool.EMPTY), pair.getValue()))
            //         .collect(Collectors.joining(StringPool.COMMA))
            // );
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Insufficient product quantities: " +
                    notEnoughList.stream()
                            .map(pair -> StringUtil.format("Product {} needs {} more units",
                                    Optional.ofNullable(ProductCacheUtil.getById(pair.getKey()))
                                            .map(ProductCache::getSupplierSku)
                                            .orElse(StringPool.EMPTY), pair.getValue()))
                            .collect(Collectors.joining(", "))
            ));
        }

        // 产品分组
        Map<Long, List<OtcWorkorderDetail>> wkDetailGroupByProductMap = workOrderDetailList
                .stream()
                .collect(Collectors.groupingBy(OtcWorkorderDetail::getProductId));
        // 分配校验打包数量
        List<BasePackedVO> packedList = AuditLogUtil.allocationPackedList(packageDetailList, wkDetailGroupByProductMap);

        // 详情映射
        Map<Long, OtcWorkorderDetail> detailMap = StreamUtils.toMap(workOrderDetailList, IdModel::getId);

        // 需要更新Packed的工单
        List<OtcWorkorderDetail> wkDetailUpdateList = packedList.stream()
                // 存在分配
                .filter(obj -> obj.getChangePackedQty() > 0)
                .map(obj -> {
                    OtcWorkorderDetail workorderDetail = detailMap.get(obj.getId());
                    // 更新工单详情打包数量
                    workorderDetail.setPackedQty(obj.getPackedQty());
                    return workorderDetail;
                })
                .toList();

        // 更新打包数量
        Validate.isTrue(otcWorkorderDetailService.updateBatch(wkDetailUpdateList) == wkDetailUpdateList.size(),
                "Update WorkOrderDetail PackQty Failed"
        );
    }

    /**
     * 获取可拣货的工单列表
     * <p>
     * 该方法根据拣货单获取处于"拣货中"状态且处理类型正常的工单列表。
     * 结果以工单ID为键，工单对象为值的Map形式返回。
     * </p>
     *
     * @param slip 拣货单
     * @return 可拣货的工单Map，以工单ID为键，工单对象为值
     * <p>
     * TODO: 方法参数名需要保持一致性，使用驼峰命名
     * TODO: 方法返回值注释不够准确，应明确说明返回值的内容和结构
     */
    @NotNull
    private Map<Long, OtcWorkorder> findCanPickListByPickingSlip(OtcPickingSlip slip) {
        List<OtcWorkorder> workorderList = lambdaQuery()
                .eq(OtcWorkorder::getOtcWorkorderStatus, OtcWorkorderStatusEnum.IN_PICKING.getStatus())
                .eq(OtcWorkorder::getOtcPickingSlipId, slip.getId())
                .list();

        // workorderList.forEach(obj -> ProcessType.checkNormalAvailability(obj.getProcessType(), obj.refNumLog(), "findCanPickListByPickingSlip"));

        return workorderList
                .stream()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
    }

    /**
     * 获取可发货的工单列表
     * <p>
     * 该方法根据工单ID列表筛选状态为"准备发货"的工单。
     * </p>
     *
     * @param workOrderIdList 工单ID列表
     * @return 可发货的工单列表
     * <p>
     * TODO: 方法名应该更明确地表示其筛选功能，如filterShippableWorkorders
     * TODO: 方法没有检查工单的处理类型，可能导致非正常状态的工单被返回
     */
    private List<OtcWorkorder> findCanShippedList(List<Long> workOrderIdList) {
        return lambdaQuery()
                .in(IdModel::getId, workOrderIdList)
                .eq(OtcWorkorder::getOtcWorkorderStatus, OtcWorkorderStatusEnum.READY_TO_SHIP.getStatus())
                .list();
    }

    /**
     * 更新工单明细拣货数量和状态
     * <p>
     * 该方法处理工单拣货操作后的数据更新，包括：
     * 1. 创建工单库位关联记录
     * 2. 更新工单明细的拣货数量
     * 3. 根据拣货情况更新工单状态
     * </p>
     *
     * @param workOrderMap                  工单Map，以工单ID为键，工单对象为值
     * @param detailGroupByProductIdMap     按产品ID分组的工单明细Map
     * @param workOrderDetailPickUpdateList 工单明细拣货信息列表
     *                                      <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      TODO: 方法职责过多，建议拆分为创建库位关联、更新明细和更新工单状态三个方法
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      TODO: 实体对象的ID生成使用了硬编码方式，考虑使用更统一的ID生成策略
     */
    private void updatePickedQtyAndPicked(Map<Long, OtcWorkorder> workOrderMap,
                                          Map<Long, List<OtcWorkorderDetail>> detailGroupByProductIdMap,
                                          List<OtcWorkorderDetailPickVO> workOrderDetailPickUpdateList) {
        // 增加otc_workorder_bin_location、lock_id
        List<OtcWorkorderBinLocation> workOrderBinLocationList = workOrderDetailPickUpdateList.stream()
                .map(obj -> {
                    OtcWorkorderBinLocation wkBinLocation = BeanUtil.copyNew(obj, OtcWorkorderBinLocation.class);
                    wkBinLocation.setId(IdWorker.getId());
                    wkBinLocation.setOtcWorkorderDetailId(obj.getId());
                    obj.setOtcWorkorderBinLocationId(wkBinLocation.getId());
                    // 分配至库位的数量
                    wkBinLocation.setQty(obj.getChangePickQty());
                    // readyToGo lock_id
                    wkBinLocation.setBinLocationDetailLockedId(obj.getReadyToGoLocked().getId());
                    return wkBinLocation;
                })
                .toList();
        otcWorkorderBinLocationService.insertBatch(workOrderBinLocationList);

        // 工单详情映射
        Map<Long, Integer> detailPickChangeMap = workOrderDetailPickUpdateList.stream()
                .collect(Collectors.groupingBy(OtcWorkorderDetailPickVO::getId,
                        Collectors.mapping(BasePickVO::getChangePickQty, Collectors.summingInt(Integer::intValue)))
                );
        List<OtcWorkorderDetail> pickDetailUpdateList = detailGroupByProductIdMap.values()
                .stream()
                .flatMap(Collection::stream)
                .filter(obj -> detailPickChangeMap.containsKey(obj.getId()))
                .toList();

        // 更新工单详情
        int wkDetailUpdateCount = otcWorkorderDetailService.updateBatch(pickDetailUpdateList);
        Validate.isTrue(wkDetailUpdateCount == pickDetailUpdateList.size(), "Failed to update work order detail");

        // 全拣货逻辑处理
        pickingSlipService.dealWithAllPicked(workOrderDetailPickUpdateList,
                OtcWorkorderDetail::getOtcWorkorderId,
                detailGroupByProductIdMap,
                (workId, allPicked) -> {
                    OtcWorkorder workorder = workOrderMap.get(workId);
                    // 更新状态
                    workorder.setOtcWorkorderStatus(allPicked
                            ? OtcWorkorderStatusEnum.PICKED.getStatus()
                            : workorder.getOtcWorkorderStatus()
                    );
                }
        );

        // 更新工单状态
        List<OtcWorkorder> finishPickingWorkorderList = workOrderMap.values().stream()
                .filter(obj -> Objects.equals(obj.getOtcWorkorderStatus(), OtcWorkorderStatusEnum.PICKED.getStatus()))
                .toList();

        Validate.isTrue(super.updateBatch(finishPickingWorkorderList) == finishPickingWorkorderList.size(),
                "Failed to update Workorder FinishPicking status"
        );

        // 工单: InPicking -> FinishPicking 日志
        OtcWorkOrderAuditLogHelper.recordLog(finishPickingWorkorderList, WorkorderLogConstant.PICKED_DESCRIPTION, null);
    }

}
