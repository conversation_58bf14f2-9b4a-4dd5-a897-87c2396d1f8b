package cn.need.cloud.biz.service.otc.workorder.impl;

import cn.need.cloud.biz.mapper.otc.OtcPrepWorkorderBinLocationMapper;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorderBinLocation;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkorderBinLocationQuery;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.base.RelatedProductVO;
import cn.need.cloud.biz.model.vo.page.OtcPrepWorkorderBinLocationPageVO;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipService;
import cn.need.cloud.biz.service.otc.workorder.*;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC预提工单仓储位置 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcPrepWorkorderBinLocationServiceImpl extends SuperServiceImpl<OtcPrepWorkorderBinLocationMapper, OtcPrepWorkorderBinLocation> implements OtcPrepWorkorderBinLocationService {

    @Resource
    private OtcPrepPickingSlipService otcPrepPickingSlipService;
    @Resource
    @Lazy
    private OtcPrepWorkorderService otcPrepWorkorderService;
    @Resource
    private OtcPrepWorkorderDetailService otcPrepWorkorderDetailService;
    @Resource
    @Lazy
    private OtcWorkorderService otcWorkorderService;
    @Resource
    private OtcWorkorderDetailService otcWorkorderDetailService;

    /// ///////////////////////////////////////// 共有方法 ////////////////////////////////////////////

    @Override
    public PageData<OtcPrepWorkorderBinLocationPageVO> pageByQuery(PageSearch<OtcPrepWorkorderBinLocationQuery> search) {
        Page<OtcPrepWorkorderBinLocation> page = Conditions.page(search, entityClass);
        List<OtcPrepWorkorderBinLocationPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        fillRelationVO(dataList);
        return new PageData<>(dataList, page);
    }

    @Override
    public List<OtcPrepWorkorderBinLocation> listByOtcWorkorderDetailIdList(List<Long> workorderDetailIdList) {
        return ObjectUtil.isEmpty(workorderDetailIdList)
                ? Collections.emptyList()
                : lambdaQuery()
                .in(OtcPrepWorkorderBinLocation::getOtcPrepWorkorderDetailId, workorderDetailIdList)
                .list();
    }

    @Override
    public List<OtcPrepWorkorderBinLocation> listByPrepWorkorderId(Long workorderId) {
        return this.lambdaQuery().eq(OtcPrepWorkorderBinLocation::getOtcPrepWorkorderId, workorderId).list();
    }

    @Override
    public List<OtcPrepWorkorderBinLocation> listByPrepWorkorderIds(Collection<Long> workorderIds) {
        if (ObjectUtil.isEmpty(workorderIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(OtcPrepWorkorderBinLocation::getOtcPrepWorkorderId, workorderIds).list();
    }

    /// ///////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    private void fillRelationVO(List<OtcPrepWorkorderBinLocationPageVO> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }
        // 拣货单、工单、工单详情、Prep工单、Prep工单详情
        List<Long> pickingSlipIds = StreamUtils.distinctMap(dataList, OtcPrepWorkorderBinLocationPageVO::getOtcPrepPickingSlipId);
        Map<Long, RefNumVO> prepPickingSlipMap = otcPrepPickingSlipService.refNumMapByIds(pickingSlipIds);

        List<Long> workOrderIds = StreamUtils.distinctMap(dataList, OtcPrepWorkorderBinLocationPageVO::getOtcWorkorderId);
        Map<Long, RefNumVO> workOrderMap = otcWorkorderService.refNumMapByIds(workOrderIds);

        List<Long> workOrderDetailIds = StreamUtils.distinctMap(dataList, OtcPrepWorkorderBinLocationPageVO::getOtcWorkorderDetailId);
        Map<Long, RelatedProductVO> workOrderDetailMap = otcWorkorderDetailService.relatedProductByIds(workOrderDetailIds);

        List<Long> prepWorkOrderIds = StreamUtils.distinctMap(dataList, OtcPrepWorkorderBinLocationPageVO::getOtcPrepWorkorderId);
        Map<Long, RefNumVO> prepWorkOrderMap = otcPrepWorkorderService.refNumMapByIds(prepWorkOrderIds);

        List<Long> prepWorkOrderDetailIds = StreamUtils.distinctMap(dataList, OtcPrepWorkorderBinLocationPageVO::getOtcPrepWorkorderDetailId);
        Map<Long, RelatedProductVO> prepWorkOrderDetailMap = otcPrepWorkorderDetailService.relatedProductByIds(prepWorkOrderDetailIds);

        dataList.forEach(obj -> {
            obj.setOtcPrepPickingSlip(prepPickingSlipMap.get(obj.getOtcPrepPickingSlipId()));
            obj.setOtcWorkOrder(workOrderMap.get(obj.getOtcWorkorderId()));
            obj.setOtcWorkOrderDetail(workOrderDetailMap.get(obj.getOtcWorkorderDetailId()));
            obj.setOtcPrepWorkOrder(prepWorkOrderMap.get(obj.getOtcPrepWorkorderId()));
            obj.setOtcPrepWorkOrderDetail(prepWorkOrderDetailMap.get(obj.getOtcPrepWorkorderDetailId()));
        });
    }

}
