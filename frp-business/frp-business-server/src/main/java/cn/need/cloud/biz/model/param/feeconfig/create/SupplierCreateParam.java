package cn.need.cloud.biz.model.param.feeconfig.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;


/**
 * 供应商信息 CreateParam对象
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@Schema(description = "供应商信息 CreateParam对象")
public class SupplierCreateParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 1045995735477040973L;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)")
    @NotNull(message = "activeFlag cannot be empty")
    private Boolean activeFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 512, message = "note cannot exceed 512 characters")
    private String note;
    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id")
    @NotNull(message = "transactionPartnerId cannot be empty")
    private Long transactionPartnerId;


}