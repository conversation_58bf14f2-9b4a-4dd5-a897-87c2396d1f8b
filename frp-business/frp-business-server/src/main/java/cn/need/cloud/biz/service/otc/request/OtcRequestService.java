package cn.need.cloud.biz.service.otc.request;


import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.model.entity.otc.OtcRequest;
import cn.need.cloud.biz.model.param.otc.create.request.OtcRequestCreateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestUpdateParam;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPageVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestVO;
import cn.need.cloud.biz.service.base.RequestService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC请求 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcRequestService extends SuperService<OtcRequest>, RequestService<OtcRequest,OtcRequestService> {

    /**
     * 根据参数新增OTC请求
     *
     * @param param 请求创建参数，包含需要插入的OTC请求的相关信息
     * @return OTC请求ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    OtcRequest insertByParam(OtcRequestCreateParam param);


    /**
     * 根据参数更新OTC请求
     *
     * @param updateParam 请求创建参数，包含需要更新的OTC请求的相关信息
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    OtcRequest updateByParam(OtcRequestUpdateParam updateParam);

    /**
     * 根据查询条件获取OTC请求列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC请求对象的列表(分页)
     */
    List<OtcRequestPageVO> listByQuery(OtcRequestQuery query);

    /**
     * 根据查询条件获取OTC请求列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC请求对象的列表(分页)
     */
    PageData<OtcRequestPageVO> pageByQuery(PageSearch<OtcRequestQuery> search);

    /**
     * 根据ID获取OTC请求
     *
     * @param id OTC请求ID
     * @return 返回OTC请求VO对象
     */
    OtcRequestVO detailById(Long id);

    /**
     * 根据OTC请求唯一编码获取OTC请求
     *
     * @param refNum OTC请求唯一编码
     * @return 返回OTC请求VO对象
     */
    OtcRequestVO detailByRefNum(String refNum);


    /**
     * 根据请求单id集合获取VO
     *
     * @param ids 主键集合
     * @return vo
     */
    Map<Long, OtcRequestVO> detailByIds(List<Long> ids);

    /**
     * 根据流水号更新状态
     *
     * @param requestRefNum 根据流水号
     * @param status        状态
     */
    void updateRequestStatus(String requestRefNum, RequestStatusEnum status);

    /**
     * 批量审核
     *
     * @param type 审核类型
     * @param note 审核备注
     * @param ids  审核的请求单id集合
     */
    void batchAudit(String type, String note, List<Long> ids);

    /**
     * 请求审核失败更新
     */
    void updateByApproveFail(OtcRequest otcRequest, String reason, String note);

    /**
     * 请求单触发Shipped
     *
     * @param requestIdList 请求单id集合
     */
    void shipped(List<Long> requestIdList);

    /**
     * 请求单触发ReadyToShip
     *
     * @param otcRequestId 请求单id集合
     */
    void readyToShip(Long otcRequestId);

    /**
     * 判断仓库是否存在未完成的订单
     *
     * @param warehouseId 仓库id
     * @return true：存在未完成的订单，false：不存在未完成的订单
     */
    Boolean existUnfinishedOrder(Long warehouseId);
}