package cn.need.cloud.biz.model.vo.otc.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * OTC工单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTC工单 vo对象")
public class OtcWorkorderVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -199208975662696483L;
    private Long id;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * 构建运输打包类型
     */
    @Schema(description = "构建运输打包类型")
    private String buildShipPackageType;

    /**
     * 发货到c端工单状态
     */
    @Schema(description = "发货到c端工单状态, workorderStatus")
    private String otcWorkorderStatus;

    /**
     * 请求快照RequestRefnum
     */
    @Schema(description = "请求快照RequestRefnum")
    private String requestSnapshotRequestRefNum;

    /**
     * 请求快照订单类型
     */
    @Schema(description = "请求快照订单类型, orderType")
    private String requestSnapshotOrderType;

    /**
     * request快照渠道
     */
    @Schema(description = "request快照渠道, channel")
    private String requestSnapshotChannel;

    /**
     * 请求快照是否有特定运输要求
     */
    @Schema(description = "请求快照是否有特定运输要求, hasCusShipRequire")
    private Boolean requestSnapshotHasCusShipRequire;

    /**
     * 请求快照快递运输标志
     */
    @Schema(description = "请求快照快递运输标志, isShipExpressFlag")
    private Boolean requestSnapshotShipExpressFlag;

    /**
     * 请求快照保险金货币
     */
    @Schema(description = "请求快照保险金货币, currency")
    private String requestSnapshotInsuranceAmountCurrency;

    /**
     * 请求快照保险金额
     */
    @Schema(description = "请求快照保险金额, amount")
    private BigDecimal requestSnapshotInsuranceAmountAmount;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;

    /**
     * 请求快照签名类型
     */
    @Schema(description = "请求快照签名类型, SignatureType")
    private String requestSnapshotSignatureType;

    /**
     * 请求快照最后运输时间
     */
    @Schema(description = "请求快照最后运输时间, lastShipDate")
    private LocalDateTime requestSnapshotLastShipDate;

    /**
     * 请求快照提供运输标签标志
     */
    @Schema(description = "请求快照提供运输标签标志, isProvideShippingLabel")
    private Boolean requestSnapshotProvideShippingLabelFlag;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum")
    private String requestSnapshotShipApiProfileRefNum;

    /**
     * request快照备注
     */
    @Schema(description = "request快照备注")
    private String requestSnapshotNote;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * request对象
     */
    @Schema(description = "request对象")
    private OtcWorkOrderRequestVO otcRequest;

    /**
     * 工单详情集合
     */
    @Schema(description = "工单详情集合")
    private List<OtcWorkorderDetailVO> detailList;


    /**
     * 订单类型
     */
    @Schema(description = "订单类型")
    private String orderType;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String workorderProductType;

    /**
     * 流程类型
     */
    @Schema(description = "流程类型")
    private String processType;

}