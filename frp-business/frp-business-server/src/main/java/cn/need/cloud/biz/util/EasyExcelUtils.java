package cn.need.cloud.biz.util;

import cn.need.framework.common.core.exception.unchecked.BusinessException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2024-11-07
 */
public class EasyExcelUtils {

    public final static String XLSX = ".xlsx";
    public final static String XLS = ".xls";
    private final static Logger log = LoggerFactory.getLogger(EasyExcelUtils.class);

    private EasyExcelUtils() {
    }

    /**
     * 使用实体类注解的方式写入
     * 默认样式-单个sheet
     *
     * @param response       返回web的端应答请求
     * @param fileName       文件命名
     * @param writerConsumer writer构建
     */
    public static void write(HttpServletResponse response, String fileName, Consumer<ExcelWriterBuilder> writerConsumer) {
        if (writerConsumer == null) {
            return;
        }
        doGarbled(response, fileName);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            final ExcelWriterBuilder builder = EasyExcel.write(outputStream);
            writerConsumer.accept(builder);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new BusinessException("导出失败");
        }
    }


    /**
     * 防止乱码处理
     *
     * @param response 返回web端应答请求
     * @param fileName 文件名称
     */
    public static void doGarbled(HttpServletResponse response, String fileName) {
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        if (fileName == null || fileName.isEmpty()) {
            throw new BusinessException("filename is must not blank");
        }
        if (!fileName.endsWith(XLS) && !fileName.endsWith(XLSX)) {
            fileName = fileName + XLSX;
        }
        // 这里URLEncoder.encode可以防止中文乱码 当然和EasyExcel没有关系
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
    }
}
