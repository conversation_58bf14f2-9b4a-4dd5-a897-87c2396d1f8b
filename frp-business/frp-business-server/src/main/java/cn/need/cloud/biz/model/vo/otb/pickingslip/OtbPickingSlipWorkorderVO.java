package cn.need.cloud.biz.model.vo.otb.pickingslip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * OTB工单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTB拣货单 工单 vo对象")
public class OtbPickingSlipWorkorderVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 6185900065745614622L;
    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * request快照RefNum
     */
    @Schema(description = "request快照RefNum")
    private String requestSnapshotRefNum;

    /**
     * request快照request RefNum
     */
    @Schema(description = "request快照request RefNum")
    private String requestSnapshotRequestRefNum;

    /**
     * request快照渠道
     */
    @Schema(description = "request快照渠道")
    private String requestSnapshotChannel;

    /**
     * 请求快照 发货窗口开始时间
     */
    @Schema(description = "请求快照 发货窗口开始时间")
    private LocalDateTime requestSnapshotShipWindowStart;

    /**
     * 请求快照 发货窗口结束时间
     */
    @Schema(description = "请求快照 发货窗口结束时间")
    private LocalDateTime requestSnapshotShipWindowEnd;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * otb 工单状态
     */
    @Schema(description = "otb 工单状态")
    private String otbWorkorderStatus;

}