package cn.need.cloud.biz.model.param.base.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * OtcPickingSlipRollbackUpdateParam
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
@Schema(description = "Prep拣货单-Rollback PutAway Units参数")
public class RollbackPutawayUnitsUpdateParam implements Serializable {

    @NotNull(message = "prepWorkorderId is must not null")
    @Schema(description = "Prep工单id")
    private Long prepWorkorderId;

    @NotNull(message = "rollbackQty is must not null")
    @Min(value = 1, message = "rollbackQty is must not empty")
    private Integer rollbackQty;

    @NotNull(message = "note is must not null")
    @NotBlank(message = "note is must not empty")
    private String note;
}
