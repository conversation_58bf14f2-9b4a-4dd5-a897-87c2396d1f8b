package cn.need.cloud.biz.model.param.base;

import cn.need.cloud.biz.model.entity.otc.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * OtcWorkorderStartRollbackUpdateParam
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@Schema(description = "StartRollback 对象")
public class WorkorderStartUpdateParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 8965597173201482147L;
    @Schema(description = "请求id集合", hidden = true)
    private List<Long> requestIdList;

    @Schema(description = "工单id集合")
    @NotEmpty(message = "WorkOrder idList is not empty")
    @NotNull(message = "WorkOrder idList is not null")
    private List<Long> idList;

    @Schema(description = "Rollback Note")
    @NotNull(message = "rollback note is not null")
    @NotBlank(message = "rollback note is not blank")
    private String note;

    //todo: 这个是建立在 requestIdList 只有一个

    private OtcRequest request;

    private List<OtcPackage> packageList = new ArrayList<>();

    private List<OtcPrepPickingSlip> prepPickingSlipList = new ArrayList<>();

    private List<OtcWorkorder> workorderList = new ArrayList<>();

    private List<OtcPickingSlip> pickingSlipList = new ArrayList<>();

}
