package cn.need.cloud.biz.service.otc.pickingslip;

import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.vo.base.workorder.WorkOrderNoEnoughAvailQtyVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcFilterBuildContextVO;

import java.util.List;

/**
 * <p>
 * OTC拣货单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcPickingSlipBuildService {

    /**
     * 过滤构建拣货单
     *
     * @param query filterBuilder构建参数
     * @return /
     */
    List<WorkOrderNoEnoughAvailQtyVO> filterBuild(OtcPickingSlipFilterBuildQuery query);

    /**
     * 过滤构建拣货单
     *
     * @param query filterBuilder构建参数
     * @return 构建上下文
     */
    OtcFilterBuildContextVO filterBuildWithContext(OtcPickingSlipFilterBuildQuery query);
}