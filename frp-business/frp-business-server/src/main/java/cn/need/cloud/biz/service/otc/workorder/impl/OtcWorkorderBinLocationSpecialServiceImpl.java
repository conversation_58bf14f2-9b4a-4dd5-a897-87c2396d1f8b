package cn.need.cloud.biz.service.otc.workorder.impl;

import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.entity.otc.OtcPutawaySlipDetail;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorderBinLocation;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderBinLocationService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderBinLocationSpecialService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC工单仓储位置 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@AllArgsConstructor
@Service
public class OtcWorkorderBinLocationSpecialServiceImpl implements OtcWorkorderBinLocationSpecialService {

    private final OtcWorkorderBinLocationService otcWorkorderBinLocationService;

    @Override
    public void rollback(OtcPutawaySlipPutAwayBO param) {
        List<Long> wkBinLocationIds = param.getDetailList().stream()
                .map(OtcPutawaySlipPutAwayDetailBO::getPutawaySlipDetail)
                .map(OtcPutawaySlipDetail::getWorkorderBinLocationId)
                .distinct()
                .toList();

        // workorder_bin_location 需要扣减数量
        List<OtcWorkorderBinLocation> workorderBinLocations = otcWorkorderBinLocationService.listByIds(wkBinLocationIds);
        Map<Long, OtcWorkorderBinLocation> wkBinLocationMap = StreamUtils.toMap(workorderBinLocations, IdModel::getId);

        // Rollback
        List<OtcWorkorderBinLocation> rollbackList = param.getDetailList().stream()
                .map(obj -> {
                    OtcPutawaySlipDetail putawaySlipDetail = obj.getPutawaySlipDetail();
                    OtcWorkorderBinLocation workorderBinLocation = wkBinLocationMap.get(putawaySlipDetail.getWorkorderBinLocationId());
                    workorderBinLocation.setQty(workorderBinLocation.getQty() - obj.getPutawayQty());
                    // 扣减至0直接删除
                    boolean needRemove = workorderBinLocation.getQty() == 0;
                    workorderBinLocation.setDeletedNote(needRemove ? param.getNote() : null);
                    workorderBinLocation.setRemoveFlag(needRemove ? 1 : 0);

                    // 绑定
                    obj.setReadyToGoLockedId(workorderBinLocation.getBinLocationDetailLockedId());
                    return workorderBinLocation;
                })
                .toList();

        List<OtcWorkorderBinLocation> updateList = rollbackList.stream().filter(obj -> obj.getRemoveFlag() == 0).toList();
        Validate.isTrue(otcWorkorderBinLocationService.updateBatch(updateList) == updateList.size(),
                "Update WorkOrderBinLocation qty is fail"
        );

        List<Long> removeIds = rollbackList.stream().filter(obj -> obj.getRemoveFlag() == 1).map(IdModel::getId).toList();
        Validate.isTrue(otcWorkorderBinLocationService.removeByIds(removeIds) == removeIds.size(),
                "Delete WorkOrderBinLocation qty is fail"
        );
    }
}