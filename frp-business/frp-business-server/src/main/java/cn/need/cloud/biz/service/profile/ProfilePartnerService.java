package cn.need.cloud.biz.service.profile;

import cn.need.cloud.biz.model.entity.setting.ProfilePartner;
import cn.need.cloud.biz.model.param.profile.create.ProfilePartnerCreateParam;
import cn.need.cloud.biz.model.param.profile.update.ProfilePartnerUpdateParam;
import cn.need.cloud.biz.model.query.profile.ProfilePartnerQuery;
import cn.need.cloud.biz.model.vo.profile.ProfilePartnerPageVO;
import cn.need.cloud.biz.model.vo.profile.ProfilePartnerVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 企业伙伴档案 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
public interface ProfilePartnerService extends SuperService<ProfilePartner> {

    /**
     * 根据参数新增企业伙伴档案
     *
     * @param createParam 请求创建参数，包含需要插入的企业伙伴档案的相关信息
     * @return 企业伙伴档案ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(ProfilePartnerCreateParam createParam);


    /**
     * 根据参数更新企业伙伴档案
     *
     * @param updateParam 请求创建参数，包含需要更新的企业伙伴档案的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(ProfilePartnerUpdateParam updateParam);

    /**
     * 根据查询条件获取企业伙伴档案列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个企业伙伴档案对象的列表(分页)
     */
    List<ProfilePartnerPageVO> listByQuery(ProfilePartnerQuery query);

    /**
     * 根据查询条件获取企业伙伴档案列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个企业伙伴档案对象的列表(分页)
     */
    PageData<ProfilePartnerPageVO> pageByQuery(PageSearch<ProfilePartnerQuery> search);

    /**
     * 根据ID获取企业伙伴档案
     *
     * @param id 企业伙伴档案ID
     * @return 返回企业伙伴档案VO对象
     */
    ProfilePartnerVO detailById(Long id);


}