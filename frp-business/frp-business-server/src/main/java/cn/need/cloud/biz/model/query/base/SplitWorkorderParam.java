package cn.need.cloud.biz.model.query.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 拆单
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
@Schema(description = "整个工单拆单信息")
@NoArgsConstructor
@AllArgsConstructor
public class SplitWorkorderParam {

    @Schema(description = "工单")
    @NotNull(message = "workorderId must not null")
    private Long id;

    @Schema(description = "工单详情")
    @NotEmpty(message = "detailList must not empty")
    @Valid
    private List<SplitWorkorderDetailParam> detailList;
}
