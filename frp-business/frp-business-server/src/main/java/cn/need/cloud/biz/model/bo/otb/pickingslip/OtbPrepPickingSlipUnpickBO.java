package cn.need.cloud.biz.model.bo.otb.pickingslip;

import cn.need.cloud.biz.model.bo.base.pickingslip.PickingSlipUnpickBO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPutawaySlip;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPutawaySlipDetail;
import cn.need.cloud.biz.model.vo.base.pickingslip.PrepPickingSlipUnpickDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "UnPick 对象")
public class OtbPrepPickingSlipUnpickBO extends PickingSlipUnpickBO<OtbPrepPutawaySlip, OtbPrepPutawaySlipDetail, PrepPickingSlipUnpickDetailVO> {

}