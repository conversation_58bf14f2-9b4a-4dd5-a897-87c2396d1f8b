package cn.need.cloud.biz.model.entity.base;

import cn.need.cloud.biz.model.vo.base.feeconfig.RefNumWithNameAble;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/***
 * 带RefNum表的模型实体
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FeeConfigModel extends RefNumWithNameModel implements FeeConfigAble, RefNumWithNameAble {

    @Serial
    private static final long serialVersionUID = -7671654346840834359L;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;


    /**
     * 仓库报价id
     */
    @TableField("quote_id")
    private Long quoteId;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @TableField("active_flag")
    private Boolean activeFlag;

    /**
     * 名称
     */
    @TableField("name")
    private String name;
}
