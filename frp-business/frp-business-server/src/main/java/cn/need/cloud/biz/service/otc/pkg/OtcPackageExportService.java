package cn.need.cloud.biz.service.otc.pkg;

import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageListQuery;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * OTC包裹 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcPackageExportService {

    /**
     * 导出包裹数据
     *
     * @param query    查询条件
     * @param response 响应
     * @return /
     */
    Boolean export(OtcPackageListQuery query, HttpServletResponse response);


}