package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;


/**
 * OTB工单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTB工单 vo对象")
public class OtbWorkorderPageVO extends BaseSuperVO {


    @Serial
    private static final long serialVersionUID = 4317534938501584962L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * request快照RefNum
     */
    @Schema(description = "request快照RefNum")
    private String requestSnapshotRefNum;

    /**
     * otb 工单状态
     */
    @Schema(description = "otb 工单状态")
    private String otbWorkorderStatus;

    /**
     * otb 工单 预状态
     */
    @Schema(description = "otb 工单 预状态")
    private String workorderPrepStatus;

    /**
     * otb 请求装运状态
     */
    @Schema(description = "otb 请求装运状态")
    private String otbRequestShipmentStatus;

    /**
     * request快照渠道
     */
    @Schema(description = "request快照渠道")
    private String requestSnapshotChannel;

    /**
     * 工单产品类型
     */
    @Schema(description = "工单产品类型")
    private String workorderProductType;

    /**
     * 请求快照 发货窗口开始时间
     */
    @Schema(description = "请求快照 发货窗口开始时间")
    private LocalDateTime requestSnapshotShipWindowStart;


    /**
     * 请求快照 发货窗口结束时间
     */
    @Schema(description = "请求快照 发货窗口结束时间")
    private LocalDateTime requestSnapshotShipWindowEnd;

    /**
     * request快照备注
     */
    @Schema(description = "request快照备注")
    private String requestSnapshotNote;


    /**
     * 请求快照 订单号
     */
    @Schema(description = "请求快照 订单号")
    private String requestSnapshotOrderNum;

    /**
     * request快照transactionPartnerId
     */
    @Schema(description = "request快照transactionPartnerId")
    private Long requestSnapshotTransactionPartnerId;

    /**
     * 请求快照RequestRefnum
     */
    @Schema(description = "请求快照RequestRefnum")
    private String requestSnapshotRequestRefNum;

    /**
     * otb请求id
     */
    @Schema(description = "otb请求id")
    private Long otbRequestId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    @Schema(description = "流程类型")
    private String processType;


}