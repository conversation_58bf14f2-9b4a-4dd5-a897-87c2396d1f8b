package cn.need.cloud.biz.service.otc.ship.impl;

import cn.hutool.core.util.ReUtil;
import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.client.constant.enums.otc.OtcLabelTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPaperTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcShipCarrierTypeEnum;
import cn.need.cloud.biz.converter.otc.OtcShipPalletConverter;
import cn.need.cloud.biz.converter.otc.OtcShipPalletDetailConverter;
import cn.need.cloud.biz.mapper.otc.OtcShipPalletMapper;
import cn.need.cloud.biz.model.entity.otc.OtcPackage;
import cn.need.cloud.biz.model.entity.otc.OtcShipPallet;
import cn.need.cloud.biz.model.entity.otc.OtcShipPalletDetail;
import cn.need.cloud.biz.model.param.otc.create.shippallet.OtcShipPalletDetailCreateParam;
import cn.need.cloud.biz.model.param.otc.create.shippallet.OtcShipPalletWithReturnCreateParam;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipPalletCheckTrackingNumQuery;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipPalletQuery;
import cn.need.cloud.biz.model.vo.otc.OtcShipPalletLabelVO;
import cn.need.cloud.biz.model.vo.otc.OtcShipPalletVO;
import cn.need.cloud.biz.model.vo.page.OtcShipPalletPageVO;
import cn.need.cloud.biz.service.base.FileStringUploadService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageService;
import cn.need.cloud.biz.service.otc.ship.OtcShipPalletDetailService;
import cn.need.cloud.biz.service.otc.ship.OtcShipPalletService;
import cn.need.cloud.biz.service.ship.AmazonClientService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.cloud.ship.client.dto.amazon.AmazonShipPalletReqDTO;
import cn.need.cloud.ship.client.dto.amazon.AmazonShipPalletRespDTO;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC运输托盘 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcShipPalletServiceImpl extends SuperServiceImpl<OtcShipPalletMapper, OtcShipPallet> implements OtcShipPalletService {

    @Resource
    private OtcShipPalletDetailService otcShipPalletDetailService;
    @Resource
    private OtcPackageService otcPackageService;
    @Resource
    private AmazonClientService amazonClientService;
    @Resource
    private FileStringUploadService fileStringUploadService;


    /// ///////////////////////////////////////// 共有方法 ////////////////////////////////////////////


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeAndNote(Long id, String note) {
        // 执行删除
        List<OtcShipPalletDetail> palletDetailList = otcShipPalletDetailService.listByShipPalletId(id);
        otcShipPalletDetailService.removeByIds(StreamUtils.distinctMap(palletDetailList, IdModel::getId));
        return super.removeAndNote(id, note);

    }

    @Override
    public PageData<OtcShipPalletPageVO> pageByQuery(PageSearch<OtcShipPalletQuery> search) {
        Page<OtcShipPallet> page = Conditions.page(search, entityClass);
        List<OtcShipPalletPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtcShipPalletVO detailById(Long id) {
        OtcShipPallet entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtcShipPallet");
        }
        return buildOtcShipPalletVO(entity);
    }

    @Override
    public OtcShipPalletVO detailByRefNum(String refNum) {
        OtcShipPallet entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in OtcShipPallet");
        }
        return buildOtcShipPalletVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtcShipPalletVO withReturn(OtcShipPalletWithReturnCreateParam createParam) {
        // 校验
        this.withReturnCheck(createParam);
        // 构建托盘
        OtcShipPallet shipPallet = this.buildWithReturnParam(createParam);
        super.insert(shipPallet);
        // 详情集合入库
        OtcShipPalletDetailConverter converter = Converters.get(OtcShipPalletDetailConverter.class);
        // 构建Details列表
        List<OtcShipPalletDetail> detailList = createParam.getDetailList().stream()
                .map(converter::toEntity)
                .peek(obj -> obj.setOtcShipPalletId(shipPallet.getId()))
                .toList();
        otcShipPalletDetailService.insertBatch(detailList);
        return this.buildOtcShipPalletVO(shipPallet);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtcShipPalletLabelVO buildShipPalletLabelByRefNum(String refNum) {
        OtcShipPallet shipPallet = this.getByRefNum(refNum);
        Validate.notNull(shipPallet, "ShipPallet is not exist");
        //获取打托单下包裹
        List<String> trackingNumberList = otcShipPalletDetailService.listByShipPalletId(shipPallet.getId())
                .stream()
                .map(OtcShipPalletDetail::getTrackingNum)
                .toList();
        // TODO 获取token、请求AmazonShipPalletHttpService服务获取Label
        AmazonShipPalletRespDTO respDTO = amazonClientService.getShipPallet(buildAmazonShipPalletReqDTO(shipPallet, trackingNumberList));

        // 模拟数据
        shipPallet.setRawDataType(FileDataTypeEnum.BASE_64_STRING_PNG.getType());
        shipPallet.setLabelType(OtcLabelTypeEnum.SHIPPING_LABEL.getType());
        shipPallet.setLabelRefNum(respDTO.getTrackingNum());
        shipPallet.setFileIdRawDataType(FileDataTypeEnum.BASE_64_STRING_PNG.getType());
        shipPallet.setPaperType(OtcPaperTypeEnum.LABEL_4X6.getType());
        shipPallet.setLabelRawData(respDTO.getEncodedLabel());
        //上传文件服务
        fileStringUploadService.uploadLabelBatch(Collections.singletonList(shipPallet));
        this.update(shipPallet);

        // 构建返回结果
        return BeanUtil.copyNew(shipPallet, OtcShipPalletLabelVO.class);
    }


    /// ///////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    @Override
    public boolean checkTrackingNum(OtcShipPalletCheckTrackingNumQuery query) {
        OtcShipCarrierTypeEnum shipCarrierType = OtcShipCarrierTypeEnum.statusOf(query.getShipCarrier());
        if (ObjectUtil.isNull(shipCarrierType)) {
            return false;
        }
        String trackingNum = query.getTrackingNum();
        return switch (shipCarrierType) {
            case FEDEX -> ReUtil.isMatch("^(96\\d{20})$|^(98\\d{20})$|^(99\\d{20})$", trackingNum);
            case UPS -> ReUtil.isMatch("^1Z[0-9A-Z]{16}$", trackingNum);
            case USPS -> ReUtil.isMatch("^(\\d{30})$", trackingNum);
            case AMAZON -> ReUtil.isMatch("^(TBA\\d{12}|\\d{22})$", trackingNum);
        };
    }

    /**
     * 构建AmazonShipPalletReqDTO对象
     *
     * @param shipPallet 传入的OTC运输托盘对象
     * @return 返回构建后的AmazonShipPalletReqDTO对象
     */
    private AmazonShipPalletReqDTO buildAmazonShipPalletReqDTO(OtcShipPallet shipPallet, List<String> trackingNumberList) {
        //获取仓库缓存
        WarehouseCache cache = WarehouseCacheUtil.getById(WarehouseContextHolder.getWarehouseId());
        // 构建AmazonShipPalletReqDTO对象
        AmazonShipPalletReqDTO reqDTO = new AmazonShipPalletReqDTO();
        reqDTO.setAppId("0L");
        reqDTO.setPackageTrackingNumbers(trackingNumberList);
        reqDTO.setProfileRefNum(Objects.requireNonNull(cache).getAmazonShipPalletProfileShipApiRefNum());
        reqDTO.setShipFromPartyId(Objects.requireNonNull(cache).getAmazonWarehouseCode());
        reqDTO.setSellingPartyId(cache.getAmazonSellingPartyCode());
        reqDTO.setVendorContainerId(shipPallet.getRefNum());
        reqDTO.setRequestRefNum(shipPallet.getRefNum());
        reqDTO.setLabelFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getLabelFormat());
        return reqDTO;
    }

    /**
     * 初始化OTC运输托盘对象
     * 此方法用于设置OTC运输托盘对象的必要参数，确保其处于有效状态
     *
     * @param entity OTC运输托盘对象，不应为空
     * @return 返回初始化后的OTC运输托盘
     * @throws BusinessException 如果传入的OTC运输托盘为空，则抛出此异常
     */
    private OtcShipPallet initOtcShipPallet(OtcShipPallet entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("OtcShipPallet cannot be empty");
        }

        // 生成RefNum
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_SHIP_PALLET.getCode()));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建OTC运输托盘VO对象
     *
     * @param entity OTC运输托盘对象
     * @return 返回包含详细信息的OTC运输托盘VO对象
     */
    private OtcShipPalletVO buildOtcShipPalletVO(OtcShipPallet entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTC运输托盘VO对象
        OtcShipPalletVO vo = Converters.get(OtcShipPalletConverter.class).toVO(entity);
        vo.setDetailList(otcShipPalletDetailService.listDetailByShipPalletId(entity.getId()));
        return vo;
    }

    /**
     * 构建实体
     *
     * @param createParam WithReturn 构建参数createParam
     * @return 运输托盘实体
     */
    private OtcShipPallet buildWithReturnParam(OtcShipPalletWithReturnCreateParam createParam) {

        OtcShipPallet shipPallet = new OtcShipPallet();
        shipPallet.setShipCarrier(createParam.getShipCarrier());
        String refNum = FormatUtil.generateRefNum(RefNumTypeEnum.OTC_SHIP_PALLET.getCode());
        shipPallet.setRefNum(refNum);
        // 后端构建
        shipPallet.setCartonCount(createParam.getDetailList().size());
        shipPallet.setNote(createParam.getNote());
        return shipPallet;
    }

    /**
     * 校验是否能新建
     *
     * @param createParam 创建参数
     */
    private void withReturnCheck(OtcShipPalletWithReturnCreateParam createParam) {
        List<OtcShipPalletDetailCreateParam> createDetailList = createParam.getDetailList();
        // 获取包裹ID列表
        List<Long> packageIdList = StreamUtils.distinctMap(createDetailList, OtcShipPalletDetailCreateParam::getOtcPackageId);
        // 包裹已经存在运输托盘
        List<OtcShipPalletDetail> palletDetailList = otcShipPalletDetailService.listByPackageIdList(packageIdList);
        if (ObjectUtil.isNotEmpty(palletDetailList)) {
            throw new BusinessException(StringUtil.format(
                    "This trackingNumber({}) has already been used for building a pallet",
                    palletDetailList.stream()
                            .map(OtcShipPalletDetail::getTrackingNum)
                            .distinct()
                            .collect(Collectors.joining(StringPool.COMMA)))
            );
        }
        List<OtcPackage> packageList = otcPackageService.listByIds(packageIdList);

        if (ObjectUtil.isEmpty(packageList)) {
            return;
        }

        //todo: 临时去掉 ReadyToShip Check

        // // 检查包裹是否ReadyToShip
        // List<OtcPackage> notReadyToShip = packageList.stream()
        //         .filter(obj -> !Objects.equals(obj.getPackageStatus(), OtcPackageStatusEnum.READY_TO_SHIP.getStatus()))
        //         .toList();
        //
        // // 检查包裹是否ReadyToShip
        // if (ObjectUtil.isNotEmpty(notReadyToShip)) {
        //     throw new BusinessException(String.format(
        //             "Package %s is not ReadyToShip",
        //             notReadyToShip.stream()
        //                     .map(OtcPackage::getRefNum)
        //                     .collect(Collectors.joining(StringPool.COMMA)))
        //     );
        // }
    }

}
