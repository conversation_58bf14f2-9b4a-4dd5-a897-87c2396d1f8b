package cn.need.cloud.biz.service.otc.pickingslip.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.otc.OtcOrderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPackageStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.pickingslip.PickingSlipLogConstant;
import cn.need.cloud.biz.converter.otc.OtcPickingSlipConverter;
import cn.need.cloud.biz.mapper.otc.OtcPickingSlipMapper;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipPickQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipQuery;
import cn.need.cloud.biz.model.vo.base.BasePickVO;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.base.ProductBinLocationPickVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.*;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageLabelVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderWithDetailVO;
import cn.need.cloud.biz.model.vo.page.OtcPickingSlipPageVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipDetailService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageDetailService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageLabelService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.util.DropListUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.biz.util.log.AuditLogUtil;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC拣货单服务实现类
 * </p>
 * <p>
 * 该类负责处理与OTC（Outbound To Customer）拣货单相关的所有业务逻辑，包括：
 * 1. 拣货单的创建、查询和状态管理
 * 2. 拣货单明细处理
 * 3. 拣货流程的执行与管理
 * 4. 拣货单与包裹、工单的关联处理
 * 5. 拣货单打印和汇总统计
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcPickingSlipServiceImpl extends SuperServiceImpl<OtcPickingSlipMapper, OtcPickingSlip> implements OtcPickingSlipService {

    /**
     * 拣货单明细服务，用于处理拣货单中的产品明细信息
     */
    @Resource
    private OtcPickingSlipDetailService otcPickingSlipDetailService;

    /**
     * 工单服务，处理与拣货单关联的工单业务
     * 使用@Lazy注解避免循环依赖
     */
    @Resource
    @Lazy
    private OtcWorkorderService otcWorkorderService;

    /**
     * 包裹服务，处理与拣货单关联的包裹业务
     */
    @Resource
    private OtcPackageService otcPackageService;

    /**
     * 包裹明细服务，处理包裹中的产品明细信息
     */
    @Resource
    private OtcPackageDetailService otcPackageDetailService;

    /**
     * 包裹标签服务，处理包裹的标签打印和管理
     */
    @Resource
    private OtcPackageLabelService otcPackageLabelService;

    /**
     * 租户缓存服务，获取租户配置信息
     */
    @Resource
    private TenantCacheService tenantCacheService;

    /**
     * 通用拣货单服务，提供基础的拣货单功能
     */
    @Resource
    private PickingSlipService pickingSlipService;

    /**
     * 构建合并单产品拣货单汇总对象
     * <p>
     * 该方法将多个拣货单中相同产品的信息汇总，生成汇总视图对象，包含产品在各拣货单和库位中的分布情况。
     * </p>
     *
     * @param pickingSlipList           拣货单列表
     * @param pickingSlipDetailGrouping 拣货单明细按拣货单ID分组的映射
     * @param entry                     产品ID与对应明细列表的键值对
     * @return 拣货单汇总视图对象
     * <p>
     * TODO: 方法参数名称应更加明确，如将entry改为productDetailEntry
     * TODO: 考虑将汇总逻辑拆分为多个子方法，提高代码可读性
     */
    private static OtcPickingSlipSummaryVO buildPickingSlipSummary(List<OtcPickingSlipPageVO> pickingSlipList,
                                                                   Map<Long, List<OtcPickingSlipDetailVO>> pickingSlipDetailGrouping,
                                                                   Map.Entry<Long, List<OtcPickingSlipDetailVO>> entry) {
        List<OtcPickingSlipDetailVO> details = entry.getValue();
        Long productId = entry.getKey();

        // 构建汇总对象
        OtcPickingSlipSummaryVO summary = new OtcPickingSlipSummaryVO();
        summary.setProductId(productId);
        // 获取当前产品对应的拣货单id列表
        List<Long> currentDetailPsIdList = StreamUtils.distinctMap(details, OtcPickingSlipDetailVO::getOtcPickingSlipId);
        // 设置拣货单信息
        List<OtcPickingSlipSummaryVO.SummaryPickingSlipDetail> summaryPsDetailList = pickingSlipList.stream()
                .filter(obj -> currentDetailPsIdList.contains(obj.getId()))
                .map(obj -> {
                    OtcPickingSlipSummaryVO.SummaryPickingSlipDetail psDetail = new OtcPickingSlipSummaryVO.SummaryPickingSlipDetail();
                    psDetail.setId(obj.getId());
                    psDetail.setRefNum(obj.getRefNum());
                    // 汇总数量
                    psDetail.setQty(pickingSlipDetailGrouping.get(obj.getId()).stream()
                            .mapToInt(OtcPickingSlipDetailVO::getQty)
                            .sum());
                    // 订单类型
                    psDetail.setOrderType(obj.getOrderType());
                    // Prep单类型
                    psDetail.setBuildFromType(obj.getBuildFromType());
                    return psDetail;
                })
                .collect(Collectors.toList());
        summary.setOtcPickingSlipList(summaryPsDetailList);

        // 设置库位信息
        List<OtcPickingSlipSummaryVO.SummaryBinLocation> summaryBlList = details.stream()
                .collect(Collectors.groupingBy(
                        OtcPickingSlipDetailVO::getBinLocationId,
                        Collectors.summingInt(OtcPickingSlipDetailVO::getQty)
                ))
                .entrySet()
                .stream()
                .map(location -> {
                    OtcPickingSlipSummaryVO.SummaryBinLocation binLocation = new OtcPickingSlipSummaryVO.SummaryBinLocation();
                    // 库位id
                    binLocation.setBinLocationId(location.getKey());
                    // 数量
                    binLocation.setQty(location.getValue());
                    return binLocation;
                })
                .collect(Collectors.toList());
        summary.setBinLocationList(summaryBlList);
        return summary;
    }

    /**
     * 记录拣货日志
     * <p>
     * 该方法记录拣货单状态变更的审计日志，包括拣货操作和完成拣货的日志。
     * </p>
     *
     * @param context 拣货上下文对象，包含拣货单和拣货后的明细信息
     *                <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        TODO: 考虑将日志记录分为两个独立方法，一个记录拣货操作，一个记录完成拣货
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        TODO: 增加更详细的日志信息，如操作人员、拣货时间等
     */
    private static void recordPickLog(OtcPickingSlipPickContextVO context) {
        // 拣货单: New -> InPicking -> Picked 日志
        String desc = AuditLogUtil.pickLogDesc(context.getPickAfterDetailList());
        OtcPickingSlip pickingSlip = context.getPickingSlip();

        // 拣货单 Pick 日志
        OtcPickingSlipAuditLogHelper.recordLog(pickingSlip, PickingSlipLogConstant.PICK_STATUS, desc, null, BaseTypeLogEnum.OPERATION.getType());

        // 记录Picked日志
        if (Objects.equals(pickingSlip.getPickingSlipStatus(), OtcPickingSlipStatusEnum.PICKED.getStatus())) {
            OtcPickingSlipAuditLogHelper.recordLog(context.getPickingSlip(), PickingSlipLogConstant.PICKED_DESCRIPTION, null);
        }
    }

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * 根据查询条件获取拣货单列表
     * <p>
     * 该方法通过指定的查询条件检索拣货单列表，不分页。
     * </p>
     *
     * @param query 拣货单查询条件
     * @return 拣货单页面视图对象列表
     * <p>
     * TODO: 考虑添加结果排序功能，确保返回结果的顺序一致性
     * TODO: 大数据量场景下可能导致性能问题，应考虑限制最大返回记录数
     */
    @Override
    public List<OtcPickingSlipPageVO> listByQuery(OtcPickingSlipQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 分页查询拣货单列表
     * <p>
     * 该方法通过指定的查询条件和分页参数检索拣货单列表。
     * </p>
     *
     * @param search 包含查询条件和分页参数的对象
     * @return 分页数据，包含拣货单页面视图对象列表和分页信息
     * <p>
     * TODO: 优化查询性能，考虑添加查询结果缓存机制
     */
    @Override
    public PageData<OtcPickingSlipPageVO> pageByQuery(PageSearch<OtcPickingSlipQuery> search) {
        Page<OtcPickingSlip> page = Conditions.page(search, entityClass);
        List<OtcPickingSlipPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取拣货单详情
     * <p>
     * 该方法通过拣货单ID检索拣货单详细信息，包括关联的明细数据。
     * </p>
     *
     * @param id 拣货单ID
     * @return 拣货单详情视图对象
     * @throws BusinessException 如果找不到指定ID的拣货单
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           TODO: 考虑使用Optional返回值代替抛出异常，提高代码健壮性
     */
    @Override
    public OtcPickingSlipVO detailById(Long id) {
        OtcPickingSlip entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in OtcPickingSlip");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtcPickingSlip", id));
        }
        return buildOtcPickingSlipVO(entity);
    }

    /**
     * 根据参考编号获取拣货单详情
     * <p>
     * 该方法通过拣货单参考编号检索拣货单详细信息，包括关联的明细数据。
     * </p>
     *
     * @param refNum 拣货单参考编号
     * @return 拣货单详情视图对象
     * @throws BusinessException 如果找不到指定参考编号的拣货单
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           TODO: 异常信息应包含更详细的上下文，帮助快速定位问题
     */
    @Override
    public OtcPickingSlipVO detailByRefNum(String refNum) {
        OtcPickingSlip entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in OtcPickingSlip");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtcPickingSlip", "refNum", refNum));
        }
        return buildOtcPickingSlipVO(entity);
    }

    /**
     * 统计打印汇总信息
     * <p>
     * 该方法根据查询条件统计拣货单的打印汇总数据，只支持处理新建、拣货中和已拣货状态的拣货单。
     * </p>
     *
     * @param query 拣货单查询条件
     * @return 拣货单打印汇总计数视图对象
     * @throws BusinessException 如果查询条件包含不支持的拣货单状态
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           TODO: 状态列表应使用枚举常量而非硬编码字符串，提高代码可维护性
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           TODO: 考虑添加缓存机制，提高频繁查询场景下的性能
     */
    @Override
    public OtcPickingSlipPrintSummaryCountVO summaryPrint(OtcPickingSlipQuery query) {
        List<String> canProcessStatuses = Arrays.asList(
                OtcPickingSlipStatusEnum.NEW.getStatus(),
                OtcPickingSlipStatusEnum.IN_PICKING.getStatus(),
                OtcPickingSlipStatusEnum.PICKED.getStatus()
        );

        boolean notProcess = !canProcessStatuses.containsAll(query.getPickingSlipStatusList());
        if (notProcess) {
            // throw new BusinessException("Only support New,InPicking,Picked Status");
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED, "OtcPickingSlip", "NEW, IN_PICKING, or PICKED", query.getPickingSlipStatusList()));
        }

        // 获取所有拣货单
        List<OtcPickingSlipPageVO> pickingSlipList = listByQuery(query);
        // 获取拣货单详情
        List<Long> pickingSlipIdList = StreamUtils.distinctMap(pickingSlipList, OtcPickingSlipPageVO::getId);
        Map<Long, List<OtcPickingSlipDetailVO>> pickingSlipDetailGrouping = otcPickingSlipDetailService.groupByOtcPickingSlip(pickingSlipIdList);

        // 获取只有一个产品的拣货单明细
        Map<Long, List<OtcPickingSlipDetailVO>> oneProductPsDetailMap = pickingSlipDetailGrouping.values().stream()
                .filter(values -> values.size() == 1)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(OtcPickingSlipDetailVO::getProductId));
        // Summary
        List<OtcPickingSlipSummaryVO> oneProducts = oneProductPsDetailMap.entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(entry -> buildPickingSlipSummary(pickingSlipList, pickingSlipDetailGrouping, entry))
                .toList();

        //  获取单个拣货单ID列表
        List<Long> onePsIds = oneProductPsDetailMap.values().stream()
                .filter(values -> values.size() == 1)
                .flatMap(Collection::stream)
                .map(OtcPickingSlipDetailVO::getOtcPickingSlipId)
                .toList();

        //  获取多产品拣货单ID列表
        List<Long> manyProductsPsIds = new ArrayList<>(pickingSlipDetailGrouping.values().stream()
                .filter(values -> values.size() > 1)
                .flatMap(Collection::stream)
                .map(OtcPickingSlipDetailVO::getOtcPickingSlipId)
                .toList());

        // no Summary 多种+一种产品的拣货单
        manyProductsPsIds.addAll(onePsIds);
        List<RefNumVO> noSummaryList = pickingSlipList.stream()
                .map(obj -> BeanUtil.copyNew(obj, RefNumVO.class))
                .filter(obj -> manyProductsPsIds.contains(obj.getId()))
                .toList();

        // 构建返回对象
        OtcPickingSlipPrintSummaryCountVO vo = new OtcPickingSlipPrintSummaryCountVO();
        vo.setSummaryList(oneProducts);
        vo.setSummaryCount(oneProducts.size());
        vo.setCount(pickingSlipIdList.size());
        vo.setNoSummaryCount(noSummaryList.size());
        vo.setNoSummaryList(noSummaryList);
        return vo;
    }

    /**
     * 执行拣货单的拣货操作
     * <p>
     * 该方法实现了拣货单的拣货操作，是拣货单从“新建”到“拣货中”再到“已拣货”状态的关键流程。
     * 主要处理步骤包括：
     * 1. 验证拣货单是否存在且状态允许拣货
     * 2. 检查处理类型是否允许拣货
     * 3. 创建拣货上下文对象
     * 4. 执行拣货单拣货操作
     * 5. 更新拣货单状态
     * 6. 执行工单拣货操作
     * 7. 执行包裹拣货操作
     * 8. 记录拣货日志
     * </p>
     * <p>
     * 该方法在事务中执行，确保所有拣货相关的操作要么全部成功，要么全部失败。
     * 它协调了拣货单、工单和包裹三个实体的状态变更，确保数据一致性。
     * </p>
     *
     * @param pickQuery 拣货查询对象，包含拣货单ID和拣货详情列表
     * @return 拣货操作是否成功
     * @throws BusinessException 如果拣货单不存在或状态不允许拣货，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           TODO: 将拣货逻辑分解为更小的方法，提高代码可维护性
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           优化建议：将验证、拣货单拣货、工单拣货、包裹拣货等逻辑分离为独立的方法
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pick(OtcPickingSlipPickQuery pickQuery) {
        Long pickingSlipId = pickQuery.getOtcPickingSlipId();
        OtcPickingSlip slip = getById(pickingSlipId);
        // 不存在拣货单
        Validate.notNull(slip, String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtcPickingSlip", pickingSlipId));

        // 仅支持New、InPicking状态
        Validate.isTrue(OtcPickingSlipStatusEnum.canPickProcessStatus(slip.getPickingSlipStatus()),
                String.format(ErrorMessages.STATUS_REQUIRED, "OtcPickingSlip", "NEW or IN_PICKING", slip.getPickingSlipStatus())
        );

        ProcessType.checkNormalAvailability(slip.getProcessType(), slip.refNumLog(), "pick");

        OtcPickingSlipPickContextVO context = new OtcPickingSlipPickContextVO();
        context.setPickingSlip(slip);
        context.setPickList(pickQuery.getPickDetailList());

        // 拣货单拣货
        pickPickingSlip(context);
        // 更新拣货单
        update(slip);
        // 工单拣货
        otcWorkorderService.pick(context);

        // 包裹拣货
        otcPackageService.pick(context.getWorkOrderPickAfterDetailList());

        recordPickLog(context);
        return true;
    }

    @Override
    public OtcPickingSlipPieceVO singleOrderMultiplePiecePackage(Long pickSlipId) {
        List<OtcPickingSlipPieceVO> psPackageList = buildPickingSlipPackage(pickSlipId, null, OtcOrderTypeEnum.SOMP);
        return ObjectUtil.isEmpty(psPackageList) ? null : psPackageList.get(0);
    }

    @Override
    public List<OtcPickingSlipPieceVO> singleOrderSinglePiecePackage(Long pickingSlipId, Long productId) {
        return buildPickingSlipPackage(pickingSlipId, productId, OtcOrderTypeEnum.SOSP);
    }

    @Override
    public List<OtcPickingSlipPieceVO> slapAndGoPackage(Long pickingSlipId, Long productId) {
        return buildPickingSlipPackage(pickingSlipId, productId, OtcOrderTypeEnum.SLAP_AND_GO);
    }

    @Override
    public List<OtcPickingSlipPieceVO> multiBoxPackage(Long pickingSlipId, Long productId) {
        return buildPickingSlipPackage(pickingSlipId, productId, OtcOrderTypeEnum.MULTI_BOX);
    }

    /**
     * 将拣货单标记为准备发货状态
     * <p>
     * 该方法在包裹准备发货时被调用，用于更新拣货单的状态和拣货单详情的准备发货数量。
     * 主要处理步骤包括：
     * 1. 获取包裹关联的拣货单
     * 2. 获取拣货单详情和包裹详情
     * 3. 根据包裹详情更新拣货单详情的准备发货数量
     * 4. 检查拣货单是否所有产品都已准备发货
     * 5. 如果所有产品都已准备发货，则将拣货单状态更新为“准备发货”
     * 6. 记录准备发货日志
     * </p>
     * <p>
     * 该方法在事务中执行，确保数据一致性。它是连接拣货和发货环节的关键步骤，
     * 确保只有当所有产品都已准备好发货时，拣货单才会进入“准备发货”状态。
     * </p>
     *
     * @param pkg 要准备发货的包裹对象
     * @throws BusinessException 如果包裹详情数量与拣货单详情不匹配，或更新拣货单详情失败，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           TODO: 当前方法中的循环嵌套度过高，应该拆分为多个小方法
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           优化建议：将包裹详情匹配、拣货单详情更新等逻辑分离为独立的方法
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void readyToShip(OtcPackage pkg) {
        OtcPickingSlip pickingSlip = getById(pkg.getOtcPickingSlipId());
        if (ObjectUtil.isNull(pickingSlip)) {
            return;
        }

        // 拣货单详情集合
        List<OtcPickingSlipDetail> pickingSlipDetailList = otcPickingSlipDetailService.groupByPickingSlipIdList(Collections.singletonList(pickingSlip.getId()))
                .values()
                .stream()
                .flatMap(Collection::stream)
                .toList();

        // 包裹详情集合
        List<OtcPackageDetail> packageDetailList = otcPackageService.afterProcessingMultiBox(pkg);

        // 需要更新的拣货单详情集合
        List<OtcPickingSlipDetail> needUpdateDetailList = new ArrayList<>();
        packageDetailList.forEach(packageDetail -> {
            int remainQty = ObjectUtil.nullToDefault(packageDetail.getQty(), 0);
            for (OtcPickingSlipDetail detail : pickingSlipDetailList) {
                if (remainQty == 0) {
                    break;
                }
                if (!Objects.equals(detail.getProductId(), packageDetail.getProductId())) {
                    continue;
                }
                // 可发货数量为空
                int canShippedQty = detail.getPickedQty() - detail.getReadyToShipQty();
                if (canShippedQty == 0) {
                    continue;
                }
                int currentQty = Math.min(canShippedQty, remainQty);
                detail.setReadyToShipQty(detail.getReadyToShipQty() + currentQty);
                needUpdateDetailList.add(detail);
                remainQty -= currentQty;
            }
            if (remainQty != 0) {
                // throw new BusinessException("Package detail qty is not match");
                throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Package detail quantity does not match picking slip detail quantity"));
            }

        });

        // 没有ReadyToShipped 的数量
        if (ObjectUtil.isEmpty(needUpdateDetailList)) {
            return;
        }

        int updateCount = otcPickingSlipDetailService.updateBatch(needUpdateDetailList);
        Validate.isTrue(updateCount == needUpdateDetailList.size(),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Failed to update all picking slip details"));

        // 全部准备发货
        boolean isAllShipped = ObjectUtil.isEmpty(pickingSlipDetailList)
                || pickingSlipDetailList.stream()
                .allMatch(obj -> Objects.equals(obj.getQty(), obj.getReadyToShipQty()));

        if (isAllShipped) {
            pickingSlip.setPickingSlipStatus(OtcPickingSlipStatusEnum.READY_TO_SHIP.getStatus());
            Validate.isTrue(update(pickingSlip) == 1,
                    String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Failed to update picking slip to READY_TO_SHIP status"));

            // 拣货单: ReadyToShip 日志
            OtcPickingSlipAuditLogHelper.recordLog(pickingSlip, PickingSlipLogConstant.READY_TO_SHIP_DESCRIPTION, null);
        }
    }

    /// ///////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    @Override
    public List<DropProVO> distinctValue(OtcPickingSlipQuery query) {
        return DropListUtil.dropProList(
                query.getColumnNameList(),
                OtcPickingSlip.class,
                columnNameList -> mapper.dropProList(columnNameList, query)
        );
    }

    /**
     * 拣货单拣货处理
     * <p>
     * 该方法实现了拣货单的具体拣货逻辑，包括拣货单详情的拣货、状态更新和拣货类型判断。
     * 主要处理步骤包括：
     * 1. 调用拣货单详情服务执行拣货操作
     * 2. 获取拣货单详情ID列表
     * 3. 判断是否所有产品都已拣货完成
     * 4. 更新拣货单状态（新建 -> 拣货中 -> 已拣货）
     * 5. 判断拣货库位类型（虚拟库位或正常库位）
     * 6. 设置拣货类型
     * 7. 将拣货信息绑定到上下文中，供后续处理使用
     * </p>
     * <p>
     * 该方法是拣货单拣货流程的核心部分，负责更新拣货单的状态和拣货信息。
     * 它会根据拣货结果决定是将拣货单状态更新为“拣货中”还是“已拣货”。
     * </p>
     *
     * @param context 拣货上下文对象，包含拣货单和拣货信息
     *                <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        TODO: 将状态判断和更新逻辑分离为独立的方法，提高代码可维护性
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        优化建议：创建单独的方法判断拣货完成状态和更新拣货单状态
     */
    private void pickPickingSlip(OtcPickingSlipPickContextVO context) {
        OtcPickingSlip pickingSlip = context.getPickingSlip();
        // 拣货单拣货信息
        List<OtcPickingSlipDetailPickVO> pickingSlipDetailPickList = otcPickingSlipDetailService.pick(context.getPickList());

        // 拣货单详情id列表
        List<Long> pickingSlipDetilIdList = StreamUtils.distinctMap(pickingSlipDetailPickList, BasePickVO::getId);
        // 全部分配完就拣货完成
        boolean currentAllPicked = pickingSlipDetailPickList.stream()
                .allMatch(detail -> Objects.equals(detail.getQty(), detail.getPickedQty()));
        boolean picked = currentAllPicked
                // 数据里的也全部拣货完
                && otcPickingSlipDetailService.allPickedIgnoreDetailIdList(pickingSlip.getId(), pickingSlipDetilIdList);

        if (Objects.equals(pickingSlip.getPickingSlipStatus(), OtcPickingSlipStatusEnum.NEW.getStatus())) {
            // 拣货单 InPicking 日志
            OtcPickingSlipAuditLogHelper.recordLog(pickingSlip, OtcPickingSlipStatusEnum.IN_PICKING.getStatus(), null, null);
        }

        // 设置状态
        pickingSlip.setPickingSlipStatus(picked
                ? OtcPickingSlipStatusEnum.PICKED.getStatus()
                : OtcPickingSlipStatusEnum.IN_PICKING.getStatus());

        // 判断拣货库位是虚拟还是正常库位
        List<Long> binLocationIdList = StreamUtils.distinctMap(pickingSlipDetailPickList, ProductBinLocationPickVO::getBinLocationId);
        // 设置谁拣货类型
        pickingSlip.setPickFromType(pickingSlipService.getPickFromType(binLocationIdList, pickingSlip.getPickFromType()));

        // 绑定拣货单锁信息
        pickingSlipDetailPickList.forEach(obj -> {
            obj.setRefTableShowRefNum(pickingSlip.getRefNum());
            obj.setRefTableShowName(OtcPickingSlip.class.getSimpleName());
        });
        // 绑定到上下文中
        context.setPickAfterDetailList(pickingSlipDetailPickList);
    }

    /**
     * 构建拣货单与包裹的关联信息
     * <p>
     * 该方法用于根据拣货单ID、产品ID和订单类型构建拣货单与包裹的关联信息。
     * 主要处理步骤包括：
     * 1. 获取拣货单信息并验证其存在性
     * 2. 验证拣货单类型是否与指定类型匹配
     * 3. 获取拣货单详情并验证产品是否存在于拣货单中
     * 4. 获取与拣货单关联的工单信息
     * 5. 获取与拣货单关联的包裹信息
     * 6. 获取包裹标签和包裹详情
     * 7. 构建工单和包裹的关联信息并返回
     * </p>
     * <p>
     * 该方法支持不同类型的订单（单个订单单件、单个订单多件、多箱、即贴即发），
     * 根据订单类型的不同构建相应的关联信息。
     * </p>
     *
     * @param pickingSlipId 拣货单ID，用于获取拣货单信息
     * @param productId     产品ID，可以为null，如果指定则只获取该产品的包裹信息
     * @param orderType     订单类型，用于验证拣货单类型是否匹配
     * @return 拣货单与包裹的关联信息列表
     * @throws BusinessException 如果拣货单不存在、类型不匹配、产品不存在于拣货单中或无法获取工单信息，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           TODO: 当前方法过长且复杂，应该拆分为多个小方法
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           优化建议：将验证、获取工单、获取包裹、构建关联信息等逻辑分离为独立的方法
     */
    private List<OtcPickingSlipPieceVO> buildPickingSlipPackage(Long pickingSlipId, Long productId, OtcOrderTypeEnum orderType) {
        // 获取拣货单
        OtcPickingSlip pickingSlip = getById(pickingSlipId);
        Validate.notNull(pickingSlip, String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtcPickingSlip", pickingSlipId));

        // 检验拣货单类型
        Validate.isTrue(Objects.equals(orderType.getStatus(), pickingSlip.getOrderType()),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Order type mismatch for picking slip " + pickingSlip.getRefNum() + ". Expected: " + orderType.getStatus() + ", Actual: " + pickingSlip.getOrderType())
        );
        // 获取拣货单详情
        List<OtcPickingSlipDetailVO> psDetailList = otcPickingSlipDetailService.listByOtcPickingSlipId(pickingSlipId);
        // 验证产品是否存在于拣货单中
        boolean checkProductIn = productId != null && psDetailList.stream()
                .noneMatch(x -> x.getProductId().equals(productId));
        if (checkProductIn) {
            // throw new BusinessException("The product is not present in this picking list");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "Product", "in picking list", pickingSlipId));
        }

        // 获取工单
        List<OtcWorkorderWithDetailVO> workOrderList = otcWorkorderService.getByPickingSlipIdAndProductId(pickingSlipId, productId);

        // 验证工单是否存在
        Validate.notEmpty(workOrderList, String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtcWorkorder", "for picking slip " + pickingSlip.getRefNum() + " and product", productId));

        // 获取包裹
        List<OtcPackage> pacakgeList = otcPackageService.listByPickingSlipId(pickingSlipId)
                .stream()
                // 过滤已经取消的包裹
                .filter(obj -> !Objects.equals(obj.getPackageStatus(), OtcPackageStatusEnum.CANCELLED.getStatus()))
                // 只处理正常流程的包裹
                .filter(obj -> Objects.equals(obj.getProcessType(), ProcessType.NORMAL.getType()))
                // 排序
                .sorted(Comparator.comparing(OtcPackage::getRefNum))
                .toList();

        // 工单包裹的映射
        Map<Long, List<OtcPackage>> packageGroupByWorkOrderIdMap = StreamUtils.groupBy(pacakgeList, OtcPackage::getOtcWorkorderId);
        // 封装包裹标签
        List<Long> packageIdList = StreamUtils.distinctMap(pacakgeList, IdModel::getId);
        Map<Long, List<OtcPackageLabel>> packageLabelGroupPackageIdMap = otcPackageLabelService.listByPackageIdList(packageIdList);
        Map<Long, List<OtcPackageDetail>> packageDetailGroupPackageIdMap = otcPackageDetailService.groupByPackageId(packageIdList);

        // 构建返回值
        return workOrderList.stream()
                .map(obj -> {
                    OtcPickingSlipPieceVO vo = new OtcPickingSlipPieceVO();
                    vo.setOtcWorkOrder(BeanUtil.copyNew(obj, OtcPickingSlipPieceWorkOrderVO.class));
                    List<OtcPackage> packageWithWorkList = packageGroupByWorkOrderIdMap.getOrDefault(obj.getId(), Collections.emptyList());
                    // 组装返回包裹信息
                    List<OtcPickingSlipPiecePackageVO> piecePkgList = packageWithWorkList.stream()
                            .map(pkg -> {
                                OtcPickingSlipPiecePackageVO piecePkg = new OtcPickingSlipPiecePackageVO();
                                BeanUtil.copy(pkg, piecePkg);
                                // 包裹标签
                                List<OtcPackageLabel> labelList = packageLabelGroupPackageIdMap.getOrDefault(pkg.getId(), Collections.emptyList());
                                piecePkg.setLabelList(BeanUtil.copyNew(labelList, OtcPackageLabelVO.class));
                                // 包裹详情
                                List<OtcPackageDetail> details = packageDetailGroupPackageIdMap.getOrDefault(pkg.getId(), Collections.emptyList());
                                piecePkg.setDetailList(BeanUtil.copyNew(details, OtcPickingSlipPiecePackageDetailVO.class));
                                // 设置需要拣货数量
                                piecePkg.getDetailList().forEach(detail -> detail.setNeedPickQty(detail.getQty() - detail.getPickedQty()));
                                return piecePkg;
                            })
                            .toList();
                    vo.setOtcPackageList(piecePkgList);
                    return vo;
                })
                .toList();
    }

    /**
     * 构建OTC拣货单VO对象
     * <p>
     * 该方法将OTC拣货单实体对象转换为包含详细信息的VO对象，便于前端展示。
     * 主要处理步骤包括：
     * 1. 检查拣货单实体是否为空
     * 2. 使用转换器将实体转换为VO对象
     * 3. 获取拣货单详情并设置到VO对象中
     * 4. 获取合作伙伴信息并设置到VO对象中
     * </p>
     * <p>
     * 该方法确保返回的VO对象包含拣货单的完整信息，包括拣货单基本信息、详情信息和合作伙伴信息。
     * </p>
     *
     * @param entity OTC拣货单实体对象
     * @return 返回包含详细信息的OTC拣货单VO对象，如果实体为空则返回null
     * <p>
     * TODO: 考虑使用Optional包装返回值，提高代码健壮性
     * 优化建议：使用Optional.ofNullable(entity).map(this::convertToVO).orElse(null)的方式处理空值情况
     */
    private OtcPickingSlipVO buildOtcPickingSlipVO(OtcPickingSlip entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTC拣货单VO对象
        OtcPickingSlipVO vo = Converters.get(OtcPickingSlipConverter.class).toVO(entity);

        // vo.setDetailList(otcPickingSlipDetailService.mergeListByOtcPickingSlipId(entity.getId()));
        vo.setDetailList(otcPickingSlipDetailService.listByOtcPickingSlipId(entity.getId()));
        // // 供应商
        // TenantCache tenantCache = tenantCacheService.getById(entity.getTransactionPartnerId());
        // if (ObjectUtil.isNotNull(tenantCache)) {
        //     vo.setBasePartnerVO(BeanUtil.copyNew(tenantCache, BasePartnerVO.class));
        // }
        return vo;
    }
}
