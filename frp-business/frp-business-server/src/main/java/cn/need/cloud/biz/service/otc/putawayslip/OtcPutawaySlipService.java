package cn.need.cloud.biz.service.otc.putawayslip;

import cn.need.cloud.biz.model.bo.otc.pickingslip.OtcPickingSlipUnpickBO;
import cn.need.cloud.biz.model.entity.otc.OtcPutawaySlip;
import cn.need.cloud.biz.model.param.base.update.PutawaySlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.putawayslip.OtcPutawaySlipPutAwayUpdateParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.query.otc.putawayslip.OtcPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.base.putawayslip.PutawaySlipConfirmDetailVO;
import cn.need.cloud.biz.model.vo.otc.page.OtcPutawaySlipPageVO;
import cn.need.cloud.biz.model.vo.otc.putawayslip.OtcPutawaySlipVO;
import cn.need.cloud.biz.service.base.HeaderPrintedService;
import cn.need.cloud.biz.service.base.putawayslip.NormalPutawaySlipService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * OTC上架单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
public interface OtcPutawaySlipService extends NormalPutawaySlipService<OtcPutawaySlip>, HeaderPrintedService<OtcPutawaySlip, OtcPutawaySlipService, PrintQuery> {

    /**
     * 根据查询条件获取OTC上架单列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC上架单对象的列表(分页)
     */
    PageData<OtcPutawaySlipPageVO> pageByQuery(PageSearch<OtcPutawaySlipQuery> search);

    /**
     * 根据ID获取OTC上架单
     *
     * @param id OTC上架单ID
     * @return 返回OTC上架单VO对象
     */
    OtcPutawaySlipVO detailById(Long id);

    /**
     * 根据OTC上架单唯一编码获取OTC上架单
     *
     * @param refNum OTC上架单唯一编码
     * @return 返回OTC上架单VO对象
     */
    OtcPutawaySlipVO detailByRefNum(String refNum);

    /**
     * 取消上架
     *
     * @param param 取消参数
     * @return /
     */
    boolean cancel(PutawaySlipCancelUpdateParam param);

    /**
     * 上架
     *
     * @param param 上架参数
     * @return /
     */
    boolean putAway(OtcPutawaySlipPutAwayUpdateParam param);

    /**
     * 创建上架单
     *
     * @param param unpick
     */
    void unpick(OtcPickingSlipUnpickBO param);

    /**
     * 详细Detail信息列表
     *
     * @param query 工单条件
     * @return /
     */
    List<PutawaySlipConfirmDetailVO> confirmDetailList(WorkorderRollbackListQuery query);
}