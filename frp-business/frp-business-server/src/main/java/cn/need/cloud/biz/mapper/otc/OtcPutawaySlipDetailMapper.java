package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcPutawaySlipDetail;
import cn.need.framework.common.mybatis.base.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 上架详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Mapper
public interface OtcPutawaySlipDetailMapper extends SuperMapper<OtcPutawaySlipDetail> {

    /**
     * 获取可用的上架单详情
     *
     * @param workorderIds 工单
     * @return /
     */
    List<OtcPutawaySlipDetail> listAvailableByWorkorderIds(@Param("workorderIds") Collection<Long> workorderIds);

}