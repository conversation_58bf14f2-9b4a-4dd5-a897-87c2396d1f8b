package cn.need.cloud.biz.service.fee;


import cn.need.cloud.biz.model.entity.fee.FeeStorageDetail;
import cn.need.cloud.biz.model.param.fee.create.FeeStorageDetailCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeStorageDetailUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeStorageDetailQuery;
import cn.need.cloud.biz.model.vo.fee.FeeStorageDetailVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeStorageDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 费用详情storage service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface FeeStorageDetailService extends SuperService<FeeStorageDetail> {

    /**
     * 根据参数新增费用详情storage
     *
     * @param createParam 请求创建参数，包含需要插入的费用详情storage的相关信息
     * @return 费用详情storageID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeStorageDetailCreateParam createParam);


    /**
     * 根据参数更新费用详情storage
     *
     * @param updateParam 请求创建参数，包含需要更新的费用详情storage的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeStorageDetailUpdateParam updateParam);

    /**
     * 根据查询条件获取费用详情storage列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用详情storage对象的列表(分页)
     */
    List<FeeStorageDetailPageVO> listByQuery(FeeStorageDetailQuery query);

    /**
     * 根据查询条件获取费用详情storage列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用详情storage对象的列表(分页)
     */
    PageData<FeeStorageDetailPageVO> pageByQuery(PageSearch<FeeStorageDetailQuery> search);

    /**
     * 根据ID获取费用详情storage
     *
     * @param id 费用详情storageID
     * @return 返回费用详情storageVO对象
     */
    FeeStorageDetailVO detailById(Long id);

    /**
     * 根据费用详情storage唯一编码获取费用详情storage
     *
     * @param refNum 费用详情storage唯一编码
     * @return 返回费用详情storageVO对象
     */
    FeeStorageDetailVO detailByRefNum(String refNum);


    /**
     * 根据费用storageid获取费用详情storage集合
     *
     * @param feeStorageId 费用storageid
     * @return 费用详情storage集合
     */
    List<FeeStorageDetailVO> listByFeeStorageId(Long feeStorageId);

}