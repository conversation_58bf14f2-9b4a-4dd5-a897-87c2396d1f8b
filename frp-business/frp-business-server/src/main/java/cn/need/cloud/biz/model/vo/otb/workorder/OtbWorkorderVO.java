package cn.need.cloud.biz.model.vo.otb.workorder;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;


/**
 * OTB工单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB工单 vo对象")
public class OtbWorkorderVO extends BaseSuperVO {

    @Serial
    private static final long serialVersionUID = 4890326496705250327L;
    private Long id;
    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * otb 工单状态
     */
    @Schema(description = "otb 工单状态")
    private String otbWorkorderStatus;

    /**
     * otb 请求装运状态
     */
    @Schema(description = "otb 请求装运状态")
    private String otbRequestShipmentStatus;

    /**
     * request快照RefNum
     */
    @Schema(description = "request快照RefNum")
    private String requestSnapshotRefNum;

    /**
     * request快照渠道
     */
    @Schema(description = "request快照渠道")
    private String requestSnapshotChannel;

    /**
     * 请求快照 发货窗口开始时间
     */
    @Schema(description = "请求快照 发货窗口开始时间")
    private LocalDateTime requestSnapshotShipWindowStart;

    /**
     * 请求快照 发货窗口结束时间
     */
    @Schema(description = "请求快照 发货窗口结束时间")
    private LocalDateTime requestSnapshotShipWindowEnd;

    /**
     * request快照备注
     */
    @Schema(description = "request快照备注")
    private String requestSnapshotNote;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * otb 工单详情
     */
    @Schema(description = "otb 工单详情")
    private List<OtbWorkorderDetailVO> detailList;

    @Schema(description = "流程类型")
    private String processType;

    @Schema(description = "拣货单id")
    private Long otbPickingSlipId;
}