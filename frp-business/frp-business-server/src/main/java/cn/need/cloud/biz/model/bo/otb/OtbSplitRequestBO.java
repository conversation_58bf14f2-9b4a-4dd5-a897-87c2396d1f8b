package cn.need.cloud.biz.model.bo.otb;

import cn.need.cloud.biz.model.vo.otb.request.OtbRequestVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * OtbSplitRequestBO
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@Schema(description = "OtbSplitRequestBO")
public class OtbSplitRequestBO implements Serializable {

    /**
     * request
     */
    @Schema(description = "request")
    private OtbRequestVO request;

    /**
     * detailList
     */
    @Schema(description = "detailList")
    private List<OtbSplitRequestDetailBO> detailList;


    public OtbSplitRequestBO(OtbRequestVO request) {
        this.request = request;
    }

}

