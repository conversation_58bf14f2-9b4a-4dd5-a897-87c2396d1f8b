package cn.need.cloud.biz.controller.binlocation;

import cn.need.cloud.biz.converter.binlocation.BinLocationReserveConverter;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationReserve;
import cn.need.cloud.biz.model.param.binlocation.create.BinLocationReserveCreateParam;
import cn.need.cloud.biz.model.param.binlocation.update.BinLocationReserveUpdateParam;
import cn.need.cloud.biz.model.query.binlocation.BinLocationReserveQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationReserveVO;
import cn.need.cloud.biz.model.vo.page.BinLocationReservePageVO;
import cn.need.cloud.biz.service.binlocation.BinLocationReserveService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 预留库位 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/bin-location-reserve")
@Tag(name = "预留库位")
public class BinLocationReserveController extends AbstractRestController<BinLocationReserveService, BinLocationReserve, BinLocationReserveConverter, BinLocationReserveVO> {

    @Operation(summary = "新增预留库位", description = "接收预留库位的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) BinLocationReserveCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改预留库位", description = "接收预留库位的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) BinLocationReserveUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除预留库位", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取预留库位详情", description = "根据数据主键id，从数据库中获取其对应的预留库位详情")
    @GetMapping(value = "/detail/{id}")
    public Result<BinLocationReserveVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取预留库位详情
        BinLocationReserveVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取预留库位分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的预留库位列表")
    @PostMapping(value = "/list")
    public Result<PageData<BinLocationReservePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<BinLocationReserveQuery> search) {

        // 获取预留库位分页
        PageData<BinLocationReservePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
