package cn.need.cloud.biz.service.inbound;

import cn.need.cloud.biz.model.entity.inbound.InboundPallet;
import cn.need.cloud.biz.model.entity.inbound.InboundPalletDetail;
import cn.need.cloud.biz.model.entity.inbound.InboundUnload;
import cn.need.cloud.biz.model.query.inbound.InboundPalletDetailQuery;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletDetailVO;
import cn.need.cloud.biz.model.vo.page.InboundPalletDetailPageVO;
import cn.need.cloud.biz.model.vo.page.InboundPalletPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 入库单打托详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface InboundPalletDetailService extends SuperService<InboundPalletDetail> {

    /**
     * 根据查询条件获取入库单打托详情列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个入库单打托详情对象的列表(分页)
     */
    List<InboundPalletDetailPageVO> listByQuery(InboundPalletDetailQuery query);

    /**
     * 根据查询条件获取入库单打托详情列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个入库单打托详情对象的列表(分页)
     */
    PageData<InboundPalletDetailPageVO> pageByQuery(PageSearch<InboundPalletDetailQuery> search);

    /**
     * 根据ID获取入库单打托详情
     *
     * @param id 入库单打托详情ID
     * @return 返回入库单打托详情VO对象
     */
    InboundPalletDetailVO detailById(Long id);


    /**
     * 根据打托id获取卸货单id
     *
     * @param palletId 打托单id
     */
    Set<Long> getByPalletId(Long palletId);

    List<InboundPalletDetail> listByPalletId(Long palletId);

    /**
     * 生成打托单详情并持久化数据库
     *
     * @param inboundUnloadList 卸货单集合
     * @param putAwaySlipId     上架单id
     * @param inboundPallet     打托单信息
     */
    List<InboundPalletDetailVO> generatePalletDetail(List<InboundUnload> inboundUnloadList, Long putAwaySlipId, InboundPallet inboundPallet);

    /**
     * 根据打托单id集合获取打托单详情集合
     *
     * @param dataList 打托单集合
     */
    void fillPalletDetail(List<InboundPalletPageVO> dataList);

    /**
     * 根据打托单id集合获取打托单详情集合
     *
     * @param list 打托单集合
     * @return 打托单详情集合
     */
    List<InboundPalletDetailVO> listByInboundPalletIds(List<Long> list);
}