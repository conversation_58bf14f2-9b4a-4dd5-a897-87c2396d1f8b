package cn.need.cloud.biz.model.param.inbound.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 入库工单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "入库工单 vo对象")
public class InboundWorkorderCreateParam implements Serializable {


    /**
     * 入库请求运输方式类型
     */
    @Schema(description = "入库请求运输方式类型")
    private String requestSnapshotTransportMethodType;

    /**
     * request快照transactionPartnerId
     */
    @Schema(description = "request快照transactionPartnerId")
    private Long requestSnapshotTransactionPartnerId;

    /**
     * 入库请求id
     */
    @Schema(description = "入库请求id")
    private Long inboundRequestId;

    /**
     * 请求快照RequestRefnum
     */
    @Schema(description = "请求快照RequestRefnum")
    private String requestSnapshotRequestRefNum;

    /**
     * 请求单预计到达时间
     */
    @Schema(description = "请求单预计到达时间")
    private LocalDateTime requestSnapshotEstimateArrivalDate;

    /**
     * 请求单实际到达时间
     */
    @Schema(description = "请求单实际到达时间")
    private LocalDateTime requestSnapshotActualArrivalDate;

    /**
     * 请求单物流跟踪编码
     */
    @Schema(description = "请求单物流跟踪编码")
    private String requestSnapshotTrackingNum;

    /**
     * 发件人姓名
     */
    @Schema(description = "发件人姓名")
    private String requestSnapshotFromAddressName;

    /**
     * 发件人公司
     */
    @Schema(description = "发件人公司")
    private String requestSnapshotFromAddressCompany;

    /**
     * 发件人国家
     */
    @Schema(description = "发件人国家")
    private String requestSnapshotFromAddressCountry;

    /**
     * 发件人州
     */
    @Schema(description = "发件人州")
    private String requestSnapshotFromAddressState;

    /**
     * 发件人城市
     */
    @Schema(description = "发件人城市")
    private String requestSnapshotFromAddressCity;

    /**
     * 发件人邮编
     */
    @Schema(description = "发件人邮编")
    private String requestSnapshotFromAddressZipCode;

    /**
     * 发件人地址1
     */
    @Schema(description = "发件人地址1")
    private String requestSnapshotFromAddressAddr1;

    /**
     * 发件人地址2
     */
    @Schema(description = "发件人地址2")
    private String requestSnapshotFromAddressAddr2;

    /**
     * 发件人地址3
     */
    @Schema(description = "发件人地址3")
    private String requestSnapshotFromAddressAddr3;

    /**
     * 发件人邮箱
     */
    @Schema(description = "发件人邮箱")
    private String requestSnapshotFromAddressEmail;

    /**
     * 发件人电话
     */
    @Schema(description = "发件人电话")
    private String requestSnapshotFromAddressPhone;

    /**
     * request快照备注
     */
    @Schema(description = "request快照备注")
    private String requestSnapshotNote;

    /**
     * 入库工单 卸货状态
     */
    @Schema(description = "入库工单 卸货状态")
    private String inboundWorkorderUnloadStatus;

    /**
     * 入库工单 上架状态
     */
    @Schema(description = "入库工单 上架状态")
    private String inboundWorkorderPutawayStatus;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;


    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 发件人备注
     */
    @Schema(description = "发件人备注")
    private String requestSnapshotFromAddressNote;

    /**
     * request快照RefNum
     */
    @Schema(description = "request快照RefNum")
    private String requestSnapshotRefNum;

    /**
     * 入库工单状态
     */
    @Schema(description = "入库工单状态")
    private String inboundWorkorderStatus;

    /**
     * 容器类型
     */
    @Schema(description = "容器类型")
    private String requestSnapshotContainerType;

    /**
     * 发件人地址是否为住宅
     */
    @Schema(description = "发件人地址是否为住宅")
    private Boolean requestSnapshotFromAddressIsResidential;

}