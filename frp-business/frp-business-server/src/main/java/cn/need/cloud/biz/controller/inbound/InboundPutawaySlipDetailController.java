package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.converter.inbound.InboundPutawaySlipDetailConverter;
import cn.need.cloud.biz.model.entity.inbound.InboundPutawaySlipDetail;
import cn.need.cloud.biz.model.query.inbound.InboundPutawaySlipDetailQuery;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipDetailVO;
import cn.need.cloud.biz.model.vo.page.InboundPutawaySlipDetailPageVO;
import cn.need.cloud.biz.service.inbound.InboundPutawaySlipDetailService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 上架详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/inbound-putaway-slip-detail")
@Tag(name = "上架详情")
public class InboundPutawaySlipDetailController extends AbstractRestController<InboundPutawaySlipDetailService, InboundPutawaySlipDetail, InboundPutawaySlipDetailConverter, InboundPutawaySlipDetailVO> {

    @Operation(summary = "根据id获取上架详情详情", description = "根据数据主键id，从数据库中获取其对应的上架详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<InboundPutawaySlipDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取上架详情详情
        InboundPutawaySlipDetailVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取上架详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的上架详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<InboundPutawaySlipDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InboundPutawaySlipDetailQuery> search) {

        // 获取上架详情分页
        PageData<InboundPutawaySlipDetailPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
