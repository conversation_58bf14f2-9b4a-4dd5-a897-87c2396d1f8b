package cn.need.cloud.biz.service.transfer.impl;

import cn.need.cloud.biz.mapper.TransferOwnerShipRequestDetailMapper;
import cn.need.cloud.biz.model.entity.TransferOwnerShipRequestDetail;
import cn.need.cloud.biz.service.transfer.TransferOwnerShipRequestDetailService;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 货权转移详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class TransferOwnerShipRequestDetailServiceImpl extends SuperServiceImpl<TransferOwnerShipRequestDetailMapper, TransferOwnerShipRequestDetail> implements TransferOwnerShipRequestDetailService {

    @Override
    public List<TransferOwnerShipRequestDetail> listByHeaderId(Long id) {
        return lambdaQuery().eq(TransferOwnerShipRequestDetail::getHeaderId, id).list();
    }
}
