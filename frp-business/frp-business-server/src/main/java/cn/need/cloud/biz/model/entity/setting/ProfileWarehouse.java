package cn.need.cloud.biz.model.entity.setting;

import cn.need.framework.common.annotation.validation.Unique;
import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 仓库档案
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("profile_warehouse")
public class ProfileWarehouse extends SuperModel {


    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 服务类型
     */
    @TableField("service_type")
    private String serviceType;

    /**
     * 分类代码
     */
    @TableField("category_code")
    private String categoryCode;

    /**
     * 分类描述
     */
    @TableField("category_desc")
    private String categoryDesc;

    /**
     * 值类型
     */
    @TableField("value_type")
    private String valueType;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 值
     */
    @TableField("value")
    private String value;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 激活标志
     */
    @TableField("active_flag")
    private Boolean activeFlag;

    /**
     * 代码
     */
    @TableField("code")
    @Unique(message = "Unique code under warehouse", combined = {"warehouseId"})
    private String code;

}
