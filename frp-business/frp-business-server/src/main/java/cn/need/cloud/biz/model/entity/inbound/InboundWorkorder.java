package cn.need.cloud.biz.model.entity.inbound;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * <p>
 * 入库工单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inbound_workorder")
public class InboundWorkorder extends RefNumModel {


    /**
     * 入库请求运输方式类型
     */
    @TableField("request_snapshot_transport_method_type")
    private String transportMethodType;

    /**
     * request快照transactionPartnerId
     */
    @TableField("request_snapshot_transaction_partner_id")
    private Long transactionPartnerId;

    /**
     * 入库请求id
     */
    @TableField("inbound_request_id")
    private Long inboundRequestId;

    /**
     * 请求快照RequestRefnum
     */
    @TableField("request_snapshot_request_ref_num")
    private String requestSnapshotRequestRefNum;

    /**
     * 请求单预计到达时间
     */
    @TableField("request_snapshot_estimate_arrival_date")
    private LocalDateTime estimateArrivalDate;

    /**
     * 请求单实际到达时间
     */
    @TableField("request_snapshot_actual_arrival_date")
    private LocalDateTime actualArrivalDate;

    /**
     * 请求单物流跟踪编码
     */
    @TableField("request_snapshot_tracking_num")
    private String trackingNum;

    /**
     * 发件人姓名
     */
    @TableField("request_snapshot_from_address_name")
    private String fromAddressName;

    /**
     * 发件人公司
     */
    @TableField("request_snapshot_from_address_company")
    private String fromAddressCompany;

    /**
     * 发件人国家
     */
    @TableField("request_snapshot_from_address_country")
    private String fromAddressCountry;

    /**
     * 发件人州
     */
    @TableField("request_snapshot_from_address_state")
    private String fromAddressState;

    /**
     * 发件人城市
     */
    @TableField("request_snapshot_from_address_city")
    private String fromAddressCity;

    /**
     * 发件人邮编
     */
    @TableField("request_snapshot_from_address_zip_code")
    private String fromAddressZipCode;

    /**
     * 发件人地址1
     */
    @TableField("request_snapshot_from_address_addr1")
    private String fromAddressAddr1;

    /**
     * 发件人地址2
     */
    @TableField("request_snapshot_from_address_addr2")
    private String fromAddressAddr2;

    /**
     * 发件人地址3
     */
    @TableField("request_snapshot_from_address_addr3")
    private String fromAddressAddr3;

    /**
     * 发件人邮箱
     */
    @TableField("request_snapshot_from_address_email")
    private String fromAddressEmail;

    /**
     * 发件人电话
     */
    @TableField("request_snapshot_from_address_phone")
    private String fromAddressPhone;

    /**
     * request快照备注
     */
    @TableField("request_snapshot_note")
    private String requestSnapshotNote;

    /**
     * 入库工单 卸货状态
     */
    @TableField("inbound_workorder_unload_status")
    private String inboundWorkorderUnloadStatus;

    /**
     * 入库工单 上架状态
     */
    @TableField("inbound_workorder_putaway_status")
    private String inboundWorkorderPutawayStatus;

    /**
     * 打印状态
     */
    @TableField("print_status")
    private String printStatus;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 发件人备注
     */
    @TableField("request_snapshot_from_address_note")
    private String fromAddressNote;

    /**
     * request快照RefNum
     */
    @TableField("request_snapshot_ref_num")
    private String requestSnapshotRefNum;

    /**
     * 入库工单状态
     */
    @TableField("inbound_workorder_status")
    private String inboundWorkorderStatus;

    /**
     * 容器类型
     */
    @TableField("request_snapshot_container_type")
    private String containerType;

    /**
     * 发件人地址是否为住宅
     */
    @TableField("request_snapshot_from_address_is_residential")
    private Boolean fromAddressIsResidential;

    @Override
    public String toLog() {
        return this.getRefNum() + "/" + this.getRequestSnapshotRefNum() + "/" + this.getRequestSnapshotRequestRefNum();
    }

}
