package cn.need.cloud.biz.model.query.otb.workorder.prep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * OTC工单仓储位置 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTB Prep工单仓储位置 query对象")
public class OtbPrepWorkOrderBinLocationQuery implements Serializable {

    /**
     * 发货到c端工单id
     */
    @Schema(description = "发货到B端工单id")
    private Long otbPrepWorkorderId;

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long otbWorkorderId;
}

