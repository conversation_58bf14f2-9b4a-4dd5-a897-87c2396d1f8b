package cn.need.cloud.biz.model.param.otb.update.pallet;

import cn.need.cloud.biz.model.param.base.update.BatchRollbackUpdateParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * OTB包裹批量Rollback对象
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTB托盘Rollback对象")
public class OtbPalletBatchRollbackUpdateParam extends BatchRollbackUpdateParam {


}
