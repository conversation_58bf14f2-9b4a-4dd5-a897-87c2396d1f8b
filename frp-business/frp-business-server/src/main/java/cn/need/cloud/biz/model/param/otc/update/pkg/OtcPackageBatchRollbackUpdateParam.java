package cn.need.cloud.biz.model.param.otc.update.pkg;

import cn.need.cloud.biz.model.param.base.update.BatchRollbackUpdateParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * OTC包裹批量Rollback对象
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC包裹批量Rollback对象")
public class OtcPackageBatchRollbackUpdateParam extends BatchRollbackUpdateParam {

}
