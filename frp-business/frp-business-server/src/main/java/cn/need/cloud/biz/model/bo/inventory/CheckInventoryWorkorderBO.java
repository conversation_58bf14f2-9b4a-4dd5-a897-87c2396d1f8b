package cn.need.cloud.biz.model.bo.inventory;

import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * CheckInventoryWorkorderVO
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@Schema(description = "CheckInventoryWorkorderBO")
public class CheckInventoryWorkorderBO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 详情列表
     */
    @Schema(description = "详情列表")
    private List<CheckInventoryWorkorderDetailBO> details;

    /**
     * 子工单类型
     * <p>
     * None 表示从非虚拟库位取值
     * 其他表示从虚拟库位取值
     * </p>
     */
    @Schema(description = "子工单类型，None 表示从非虚拟库位取值，其他表示从虚拟库位取值")
    private WorkOrderPrepStatusEnum workOrderPrepStatus;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 参考编号，供外部使用，唯一
     * <p>
     * 格式：xx_123456
     * </p>
     */
    @Schema(description = "参考编号，供外部使用，唯一")
    private String refNum;
}
