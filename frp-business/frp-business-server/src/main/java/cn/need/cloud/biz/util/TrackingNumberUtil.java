package cn.need.cloud.biz.util;

import java.util.Random;
import java.util.UUID;

public class TrackingNumberUtil {

    private static final Random random = new Random();

    /**
     * 根据物流公司生成模拟的跟踪号码
     *
     * @param prefix 物流公司名称（如"Amazon"、"FedEx"、"UPS"、"USPS"等）
     * @return 模拟的跟踪号码
     */
    public static String generate(String prefix, int length) {
        // 生成10位随机数字
        String randomDigits = generateRandomDigits(length);
        // 生成校验位
        String checkDigit = generateCheckDigit(prefix + randomDigits);

        return prefix + randomDigits + checkDigit;
    }

    /**
     * 获取物流公司的跟踪号码前缀
     *
     * @param carrier 物流公司名称
     * @return 跟踪号码前缀
     */
    public static String mock(String carrier) {
        return switch (carrier.toLowerCase()) {
            case "amazon" -> generate("TBA", 11);
            case "fedex" -> generate("96", 19);
            case "ups" -> generate("1Z", 15);
            case "usps" -> generate("94", 27);
            default -> UUID.randomUUID().toString();
        };
    }

    /**
     * 生成指定长度的随机数字字符串
     *
     * @param length 随机数字字符串的长度
     * @return 随机数字字符串
     */
    private static String generateRandomDigits(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    /**
     * 生成校验位
     *
     * @param number 跟踪号码的前缀和随机数字部分
     * @return 校验位
     */
    private static String generateCheckDigit(String number) {
        int sum = 0;
        for (int i = 0; i < number.length(); i++) {
            int digit = Character.getNumericValue(number.charAt(i));
            sum += (i % 2 == 0) ? digit : digit * 3;
        }
        int checkDigit = (10 - (sum % 10)) % 10;
        return String.valueOf(checkDigit);
    }
}
