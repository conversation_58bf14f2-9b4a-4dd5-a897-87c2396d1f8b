package cn.need.cloud.biz.controller;

import cn.need.cloud.ship.client.api.SSCCClient;
import cn.need.cloud.upms.cache.util.TenantCacheUtil;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <p>
 * OTB包裹 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/bop")
@Tag(name = "bop接口")
public class TestController extends AbstractController {
    @Resource
    private SSCCClient ssccClient;

    @Operation(summary = "更新库存", description = "更新库存")
    @PostMapping(value = "/update")
    public Result<Set<Long>> update() {
        Set<Long> list = TenantCacheUtil.list();
        return Result.ok(list);
    }
}
