package cn.need.cloud.biz.service.feeconfig.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.feeconfig.FeeConfigInboundConverter;
import cn.need.cloud.biz.mapper.feeconfig.FeeConfigInboundMapper;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigInbound;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigInboundDetail;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigInboundCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigInboundUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigInboundFrpQuery;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigInboundQuery;
import cn.need.cloud.biz.model.vo.base.feeconfig.RefNumWithNameVO;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigInboundDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigInboundVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigInboundPageVO;
import cn.need.cloud.biz.service.feeconfig.FeeConfigInboundDetailService;
import cn.need.cloud.biz.service.feeconfig.FeeConfigInboundService;
import cn.need.cloud.biz.service.feeconfig.QuoteService;
import cn.need.cloud.biz.service.helper.SectionHelper;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 仓库报价费用配置inbound service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service("feeConfigInbound")
public class FeeConfigInboundServiceImpl extends SuperServiceImpl<FeeConfigInboundMapper, FeeConfigInbound> implements FeeConfigInboundService {

    @Resource
    private FeeConfigInboundDetailService feeConfigInboundDetailservice;
    @Resource
    private QuoteService quoteService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(FeeConfigInboundCreateParam createParam) {
        // 检查传入仓库报价费用配置inbound参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        SectionHelper.checkDetailList(createParam.getDetailList());

        // 获取仓库报价费用配置inbound转换器实例，用于将仓库报价费用配置inbound参数对象转换为实体对象
        FeeConfigInboundConverter converter = Converters.get(FeeConfigInboundConverter.class);

        // 将仓库报价费用配置inbound参数对象转换为实体对象并初始化
        FeeConfigInbound entity = initFeeConfigInbound(converter.toEntity(createParam));

        // 插入仓库报价费用配置inbound实体对象到数据库
        super.insert(entity);

        final List<FeeConfigInboundDetail> feeConfigInboundDetails = feeConfigInboundDetailservice.initFeeConfigInboundDetail(createParam.getDetailList(), item -> item.setHeaderId(entity.getId()));

        feeConfigInboundDetailservice.insertBatch(feeConfigInboundDetails);

        // 返回仓库报价费用配置inboundID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(FeeConfigInboundUpdateParam updateParam) {
        // 检查传入仓库报价费用配置inbound参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        SectionHelper.checkDetailList(updateParam.getDetailList());

        checkHasQuote(updateParam.getId());

        // 获取仓库报价费用配置inbound转换器实例，用于将仓库报价费用配置inbound参数对象转换为实体对象
        FeeConfigInboundConverter converter = Converters.get(FeeConfigInboundConverter.class);
        // 将仓库报价费用配置inbound参数对象转换为实体对象
        FeeConfigInbound entity = converter.toEntity(updateParam);

        feeConfigInboundDetailservice.updateByFeeConfigInboundId(updateParam.getId(), updateParam.getDetailList());

        // 执行更新仓库报价费用配置inbound操作
        return super.update(entity);

    }

    @Override
    public List<FeeConfigInboundPageVO> listByQuery(FeeConfigInboundQuery query) {

        return mapper.listByQuery(query);
    }

    @Override
    public PageData<FeeConfigInboundPageVO> pageByQuery(PageSearch<FeeConfigInboundQuery> search) {
        Page<FeeConfigInbound> page = Conditions.page(search, entityClass);
        List<FeeConfigInboundPageVO> dataList = mapper.listByQuery(search.getCondition(), page);

        //fillData
        fillData(dataList);

        return new PageData<>(dataList, page);
    }

    @Override
    public FeeConfigInboundVO detailById(Long id) {
        FeeConfigInbound entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "FeeConfigInbound", id));
        }

        return buildFeeConfigInboundVO(entity);
    }

    @Override
    public FeeConfigInboundVO detailByRefNum(String refNum) {
        FeeConfigInbound entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "FeeConfigInbound", "refNum", refNum));
        }
        return buildFeeConfigInboundVO(entity);
    }

    @Override
    public PageData<FeeConfigInboundPageVO> pageByQueryPro(PageSearch<FeeConfigInboundFrpQuery> search) {

        Page<FeeConfigInbound> page = Conditions.page(search, entityClass);
        List<FeeConfigInboundPageVO> dataList = mapper.listByQueryPro(search.getCondition(), page);

        return new PageData<>(dataList, page);
    }

    @Override
    public List<FeeConfigInboundPageVO> listByQueryPro(FeeConfigInboundFrpQuery query) {

        return mapper.listByQueryPro(query);
    }

    @Override
    public List<FeeConfigInboundVO> listDetailByQuoteId(Long quoteId) {
        if (ObjectUtil.isEmpty(quoteId)) {
            return Collections.emptyList();
        }
        List<FeeConfigInbound> feeConfigInboundList = lambdaQuery().eq(FeeConfigInbound::getQuoteId, quoteId).list();
        return buildFeeConfigInboundVOList(feeConfigInboundList);
    }

    @Override
    public void beforeSwitchActive(FeeConfigInbound entity) {
        checkHasQuote(entity);
    }

    /**
     * 初始化仓库报价费用配置inbound对象
     * 此方法用于设置仓库报价费用配置inbound对象的必要参数，确保其处于有效状态
     *
     * @param entity 仓库报价费用配置inbound对象，不应为空
     * @return 返回初始化后的仓库报价费用配置inbound
     * @throws BusinessException 如果传入的仓库报价费用配置inbound为空，则抛出此异常
     */
    private FeeConfigInbound initFeeConfigInbound(FeeConfigInbound entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("FeeConfigInbound cannot be empty");
        }

        entity.setId(IdWorker.getId());
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_CONFIG_INBOUND));

        // 返回初始化后的配置对象
        return entity;
    }

    private void fillData(List<FeeConfigInboundPageVO> dataList) {
        if (!ObjectUtil.isNotEmpty(dataList)) {
            return;
        }

        final Map<Long, RefNumWithNameVO> quoteMap = quoteService.refNumWithNameMapByIds(FeeConfigUtil.getQuoteIds(dataList));

        if (ObjectUtil.isNotEmpty(quoteMap)) {
            for (FeeConfigInboundPageVO item : dataList) {
                if (!ObjectUtil.isNotEmpty(item.getQuoteId())) {
                    continue;
                }
                item.setQuote(quoteMap.get(item.getQuoteId()));
            }
        }
    }

    @Override
    public int removeAndNote(Long id, String note) {

        checkHasQuote(id);

        return super.removeAndNote(id, note);
    }


    /**
     * 构建仓库报价费用配置inboundVO对象
     *
     * @param entity 仓库报价费用配置inbound对象
     * @return 返回包含详细信息的仓库报价费用配置inboundVO对象
     */
    private FeeConfigInboundVO buildFeeConfigInboundVO(FeeConfigInbound entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }

        FeeConfigInboundVO result = Converters.get(FeeConfigInboundConverter.class).toVO(entity);

        //详情
        List<FeeConfigInboundDetailVO> detailList = feeConfigInboundDetailservice.listByFeeConfigInboundId(result.getId());

        result.setDetailList(detailList);

        result.setQuote(quoteService.refNumWithNameById(entity.getQuoteId()));

        return result;
    }

    /**
     * 构建仓库报价费用配置inboundVO对象列表
     *
     * @param entityList 仓库报价费用配置inbound对象列表
     * @return 返回包含详细信息的仓库报价费用配置inboundVO对象列表
     */
    private List<FeeConfigInboundVO> buildFeeConfigInboundVOList(List<FeeConfigInbound> entityList) {
        if (ObjectUtil.isEmpty(entityList)) {
            return Collections.emptyList();
        }

        List<FeeConfigInboundVO> resultList = Converters.get(FeeConfigInboundConverter.class).toVO(entityList);

        //详情
        List<Long> feeConfigIds = resultList.stream().map(FeeConfigInboundVO::getId).toList();
        List<Long> quoteIds = resultList.stream().map(FeeConfigInboundVO::getQuoteId).toList();

        Map<Long, List<FeeConfigInboundDetailVO>> detailListMap = feeConfigInboundDetailservice.mapByFeeConfigInboundIdList(feeConfigIds);

        final Map<Long, RefNumWithNameVO> refNumVOMap = quoteService.refNumWithNameMapByIds(quoteIds);

        for (FeeConfigInboundVO feeConfigInboundVO : resultList) {

            List<FeeConfigInboundDetailVO> detailList = detailListMap.get(feeConfigInboundVO.getId());
            feeConfigInboundVO.setDetailList(ObjectUtil.isEmpty(detailList) ? Collections.emptyList() : detailList);

            RefNumWithNameVO quote = refNumVOMap.get(feeConfigInboundVO.getId());
            feeConfigInboundVO.setQuote(quote);
        }

        return resultList;
    }


}
