package cn.need.cloud.biz.model.entity.product;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 产品组装
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_component")
public class ProductComponent extends SuperModel {


    /**
     * 组装产品ID
     */
    @TableField("assembly_product_id")
    private Long assemblyProductId;

    /**
     * 组件产品ID
     */
    @TableField("component_product_id")
    private Long componentProductId;

    /**
     * 组装说明备注
     */
    @TableField("assembly_instruction_note")
    private String assemblyInstructionNote;


    @TableField("component_qty")
    private Integer componentQty;

    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 组装产品版本号
     */
    @TableField("component_version_int")
    private Integer componentVersionInt;

}
