package cn.need.cloud.biz.service.otb.shipment;

import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbSignedBolFileVO;

/**
 * <p>
 * OTB大件装运 service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface OtbShipmentLtlService {
    /**
     * 打印Pol文件
     *
     * @param printQuery 打印信息
     */
    void printedBolFile(PrintQuery printQuery);

    /**
     * 打印托盘文件
     *
     * @param printQuery 打印信息
     */
    void printedPalletFile(PrintQuery printQuery);

    /**
     * 拆托
     *
     * @param otbShipmentId 发货单ID
     */
    void splitPallet(Long otbShipmentId);

    /**
     * 打托单打托完成
     *
     * @param otbPalletId   打托单ID
     * @param otbShipmentId 发货单ID
     */
    void markPalletReadyToShip(Long otbPalletId, Long otbShipmentId);

    /**
     * 大件发货
     *
     * @param otbSignedBolFileVO 签字文件信息
     */
    void processed(OtbSignedBolFileVO otbSignedBolFileVO);
}
