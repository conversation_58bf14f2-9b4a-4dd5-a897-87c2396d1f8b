package cn.need.cloud.biz.model.query.base.putawayslip;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * OTC上架单 Query对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PutawaySlipQuery extends SuperQuery {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    // region deletedNote

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 集合")
    @Condition(value = Keyword.IN, fields = {"deletedNote"})
    private List<String> deletedNoteList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"deletedNote"})
    private List<String> deletedNoteNiList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因值类型集合")
    private List<String> deletedNoteValueTypeList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于")
    @Condition(value = Keyword.GT, fields = {"deletedNote"})
    private String deletedNoteGt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于等于")
    @Condition(value = Keyword.GE, fields = {"deletedNote"})
    private String deletedNoteGe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于")
    @Condition(value = Keyword.LT, fields = {"deletedNote"})
    private String deletedNoteLt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于等于")
    @Condition(value = Keyword.LE, fields = {"deletedNote"})
    private String deletedNoteLe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"deletedNote"})
    private String deletedNoteLike;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"deletedNote"})
    private String deletedNoteLikeLeft;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"deletedNote"})
    private String deletedNoteLikeRight;

    // endregion deletedNote

    // region note

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 备注
     */
    @Schema(description = "备注 集合")
    @Condition(value = Keyword.IN, fields = {"note"})
    private List<String> noteList;

    /**
     * 备注
     */
    @Schema(description = "备注 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"note"})
    private List<String> noteNiList;

    /**
     * 备注
     */
    @Schema(description = "备注值类型集合")
    private List<String> noteValueTypeList;

    /**
     * 备注
     */
    @Schema(description = "备注 大于")
    @Condition(value = Keyword.GT, fields = {"note"})
    private String noteGt;

    /**
     * 备注
     */
    @Schema(description = "备注 大于等于")
    @Condition(value = Keyword.GE, fields = {"note"})
    private String noteGe;

    /**
     * 备注
     */
    @Schema(description = "备注 小于")
    @Condition(value = Keyword.LT, fields = {"note"})
    private String noteLt;

    /**
     * 备注
     */
    @Schema(description = "备注 小于等于")
    @Condition(value = Keyword.LE, fields = {"note"})
    private String noteLe;

    /**
     * 备注
     */
    @Schema(description = "备注 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"note"})
    private String noteLike;

    /**
     * 备注
     */
    @Schema(description = "备注 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"note"})
    private String noteLikeLeft;

    /**
     * 备注
     */
    @Schema(description = "备注 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"note"})
    private String noteLikeRight;

    // endregion note

    // region putawaySlipStatus

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String putawaySlipStatus;

    /**
     * 状态
     */
    @Schema(description = "状态 集合")
    @Condition(value = Keyword.IN, fields = {"putawaySlipStatus"})
    private List<String> putawaySlipStatusList;

    /**
     * 状态
     */
    @Schema(description = "状态 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"putawaySlipStatus"})
    private List<String> putawaySlipStatusNiList;

    /**
     * 状态
     */
    @Schema(description = "状态值类型集合")
    private List<String> putawaySlipStatusValueTypeList;

    /**
     * 状态
     */
    @Schema(description = "状态 大于")
    @Condition(value = Keyword.GT, fields = {"putawaySlipStatus"})
    private String putawaySlipStatusGt;

    /**
     * 状态
     */
    @Schema(description = "状态 大于等于")
    @Condition(value = Keyword.GE, fields = {"putawaySlipStatus"})
    private String putawaySlipStatusGe;

    /**
     * 状态
     */
    @Schema(description = "状态 小于")
    @Condition(value = Keyword.LT, fields = {"putawaySlipStatus"})
    private String putawaySlipStatusLt;

    /**
     * 状态
     */
    @Schema(description = "状态 小于等于")
    @Condition(value = Keyword.LE, fields = {"putawaySlipStatus"})
    private String putawaySlipStatusLe;

    /**
     * 状态
     */
    @Schema(description = "状态 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"putawaySlipStatus"})
    private String putawaySlipStatusLike;

    /**
     * 状态
     */
    @Schema(description = "状态 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"putawaySlipStatus"})
    private String putawaySlipStatusLikeLeft;

    /**
     * 状态
     */
    @Schema(description = "状态 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"putawaySlipStatus"})
    private String putawaySlipStatusLikeRight;

    // endregion putawaySlipStatus

    // region printStatus

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String printStatus;

    /**
     * 状态
     */
    @Schema(description = "状态 集合")
    @Condition(value = Keyword.IN, fields = {"printStatus"})
    private List<String> printStatusList;

    /**
     * 状态
     */
    @Schema(description = "状态 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"printStatus"})
    private List<String> printStatusNiList;

    /**
     * 状态
     */
    @Schema(description = "状态值类型集合")
    private List<String> printStatusValueTypeList;

    /**
     * 状态
     */
    @Schema(description = "状态 大于")
    @Condition(value = Keyword.GT, fields = {"printStatus"})
    private String printStatusGt;

    /**
     * 状态
     */
    @Schema(description = "状态 大于等于")
    @Condition(value = Keyword.GE, fields = {"printStatus"})
    private String printStatusGe;

    /**
     * 状态
     */
    @Schema(description = "状态 小于")
    @Condition(value = Keyword.LT, fields = {"printStatus"})
    private String printStatusLt;

    /**
     * 状态
     */
    @Schema(description = "状态 小于等于")
    @Condition(value = Keyword.LE, fields = {"printStatus"})
    private String printStatusLe;

    /**
     * 状态
     */
    @Schema(description = "状态 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"printStatus"})
    private String printStatusLike;

    /**
     * 状态
     */
    @Schema(description = "状态 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"printStatus"})
    private String printStatusLikeLeft;

    /**
     * 状态
     */
    @Schema(description = "状态 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"printStatus"})
    private String printStatusLikeRight;

    // endregion printStatus

    // region putawaySlipType

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String putawaySlipType;

    /**
     * 状态
     */
    @Schema(description = "状态 集合")
    @Condition(value = Keyword.IN, fields = {"putawaySlipType"})
    private List<String> putawaySlipTypeList;

    /**
     * 状态
     */
    @Schema(description = "状态 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"putawaySlipType"})
    private List<String> putawaySlipTypeNiList;

    /**
     * 状态
     */
    @Schema(description = "状态值类型集合")
    private List<String> putawaySlipTypeValueTypeList;

    /**
     * 状态
     */
    @Schema(description = "状态 大于")
    @Condition(value = Keyword.GT, fields = {"putawaySlipType"})
    private String putawaySlipTypeGt;

    /**
     * 状态
     */
    @Schema(description = "状态 大于等于")
    @Condition(value = Keyword.GE, fields = {"putawaySlipType"})
    private String putawaySlipTypeGe;

    /**
     * 状态
     */
    @Schema(description = "状态 小于")
    @Condition(value = Keyword.LT, fields = {"putawaySlipType"})
    private String putawaySlipTypeLt;

    /**
     * 状态
     */
    @Schema(description = "状态 小于等于")
    @Condition(value = Keyword.LE, fields = {"putawaySlipType"})
    private String putawaySlipTypeLe;

    /**
     * 状态
     */
    @Schema(description = "状态 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"putawaySlipType"})
    private String putawaySlipTypeLike;

    /**
     * 状态
     */
    @Schema(description = "状态 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"putawaySlipType"})
    private String putawaySlipTypeLikeLeft;

    /**
     * 状态
     */
    @Schema(description = "状态 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"putawaySlipType"})
    private String putawaySlipTypeLikeRight;

    // endregion putawaySlipType

    // region refNum

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String refNum;

    /**
     * 编码
     */
    @Schema(description = "编码 集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 编码
     */
    @Schema(description = "编码 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"refNum"})
    private List<String> refNumNiList;

    /**
     * 编码
     */
    @Schema(description = "编码值类型集合")
    private List<String> refNumValueTypeList;

    /**
     * 编码
     */
    @Schema(description = "编码 大于")
    @Condition(value = Keyword.GT, fields = {"refNum"})
    private String refNumGt;

    /**
     * 编码
     */
    @Schema(description = "编码 大于等于")
    @Condition(value = Keyword.GE, fields = {"refNum"})
    private String refNumGe;

    /**
     * 编码
     */
    @Schema(description = "编码 小于")
    @Condition(value = Keyword.LT, fields = {"refNum"})
    private String refNumLt;

    /**
     * 编码
     */
    @Schema(description = "编码 小于等于")
    @Condition(value = Keyword.LE, fields = {"refNum"})
    private String refNumLe;

    /**
     * 编码
     */
    @Schema(description = "编码 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"refNum"})
    private String refNumLike;

    /**
     * 编码
     */
    @Schema(description = "编码 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"refNum"})
    private String refNumLikeLeft;

    /**
     * 编码
     */
    @Schema(description = "编码 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"refNum"})
    private String refNumLikeRight;

    // endregion refNum

    // region warehouseId

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 集合")
    @Condition(value = Keyword.IN, fields = {"warehouseId"})
    private List<Long> warehouseIdList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"warehouseId"})
    private List<Long> warehouseIdNiList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id值类型集合")
    private List<String> warehouseIdValueTypeList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于")
    @Condition(value = Keyword.GT, fields = {"warehouseId"})
    private Long warehouseIdGt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于等于")
    @Condition(value = Keyword.GE, fields = {"warehouseId"})
    private Long warehouseIdGe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于")
    @Condition(value = Keyword.LT, fields = {"warehouseId"})
    private Long warehouseIdLt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于等于")
    @Condition(value = Keyword.LE, fields = {"warehouseId"})
    private Long warehouseIdLe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"warehouseId"})
    private Long warehouseIdLike;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"warehouseId"})
    private Long warehouseIdLikeLeft;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"warehouseId"})
    private Long warehouseIdLikeRight;

    // endregion warehouseId

}