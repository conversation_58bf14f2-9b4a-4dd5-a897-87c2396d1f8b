package cn.need.cloud.biz.service.otc.pickingslip.impl;

import cn.hutool.core.lang.Pair;
import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPackageStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.PutAwaySlipType;
import cn.need.cloud.biz.client.constant.special.RollbackConstant;
import cn.need.cloud.biz.model.bo.base.ChangeQtyLogBO;
import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.base.pickingslip.UnpickDetailBO;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.bo.otc.OtcPackageRollbackSingleWorkorderBO;
import cn.need.cloud.biz.model.bo.otc.pickingslip.OtcPickingSlipUnpickBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitDetailBO;
import cn.need.cloud.biz.model.entity.base.pickingslip.BasePickingSlipDetailModel;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.param.base.update.PickingSlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.pickingslip.OtcPickingSlipUnpickCreateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelContext;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.UnpickVO;
import cn.need.cloud.biz.model.vo.base.pickingslip.PickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmDetailVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.helper.LockedHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.helper.pickingslip.OtcPickingSlipHelper;
import cn.need.cloud.biz.service.helper.pickingslip.PickingSlipHelper;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipDetailService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipSpecialService;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPutawaySlipService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderSpecialService;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * OTC拣货单特殊操作服务实现类
 * </p>
 * <p>
 * 该类实现了OTC拣货单的特殊操作功能，包括拣货单的挂起、回滚、取消、撤销拣货等非常规流程操作。
 * 主要功能包括：
 * 1. 拣货单挂起（On Hold）操作
 * 2. 拣货单流程类型变更（如标记为回滚中、取消中等）
 * 3. 撤销拣货（Unpick）操作，将已拣货的商品退回库位
 * 4. 回滚准备发货状态，取消已准备发货的包裹
 * 5. 批量取消拣货单
 * </p>
 * <p>
 * 这些特殊操作通常用于处理异常情况，如订单取消、拣货错误、库存调整等场景，
 * 确保系统能够灵活应对各种业务变更需求。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor(onConstructor = @__(@Lazy))
public class OtcPickingSlipSpecialServiceImpl implements OtcPickingSlipSpecialService {

    /**
     * OTC拣货单服务，用于操作拣货单基本信息
     */
    private final OtcPickingSlipService otcPickingSlipService;

    /**
     * OTC拣货单详情服务，用于操作拣货单详情信息
     */
    private final OtcPickingSlipDetailService otcPickingSlipDetailService;

    /**
     * OTC工单服务，用于操作工单基本信息
     */
    private final OtcWorkorderService otcWorkorderService;

    /**
     * OTC工单特殊操作服务，用于处理工单的特殊操作，如取消、回滚等
     */
    private final OtcWorkorderSpecialService otcWorkorderSpecialService;

    /**
     * OTC上架单服务，用于操作上架单基本信息
     */
    private final OtcPutawaySlipService otcPutawaySlipService;

    private final BinLocationDetailLockedService binLocationDetailLockedService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * Rollback 更新状态
     *
     * @param rollback rollback参数
     */
    private static void rollbackUpdateStatus(OtcPackageRollbackSingleWorkorderBO rollback) {
        // 拣货单
        OtcPickingSlip pickingSlip = rollback.getPickingSlip();

        if (ObjectUtil.isEmpty(pickingSlip)) {
            return;
        }
        // 设置状态
        boolean picked = rollback.getPickingSlipDetailList().stream().allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()));
        boolean isNew = rollback.getPickingSlipDetailList().stream().allMatch(obj -> obj.getPickedQty() == 0);
        String oldStatus = pickingSlip.getPickingSlipStatus();
        pickingSlip.setPickingSlipStatus(isNew ?
                OtcPickingSlipStatusEnum.NEW.getStatus() : picked
                ? OtcPickingSlipStatusEnum.PICKED.getStatus()
                : OtcPickingSlipStatusEnum.IN_PICKING.getStatus()
        );
        // 状态变更记录日志
        if (!Objects.equals(oldStatus, pickingSlip.getPickingSlipStatus())) {
            OtcPickingSlipAuditLogHelper.recordLog(pickingSlip);
        }
    }

    /**
     * 触发拣货单的流程类型变更
     * <p>
     * 该方法用于更新拣货单的流程类型，如将拣货单标记为“回滚中”、“取消中”等状态。
     * 流程类型的变更影响拣货单的后续处理方式，如异常流程类型的拣货单可以进行撤销拣货操作。
     * </p>
     * <p>
     * 操作流程：
     * 1. 检查拣货单ID列表是否为空，如果为空则直接返回
     * 2. 获取流程类型值
     * 3. 获取拣货单列表并更新流程类型
     * 4. 批量更新拣货单
     * 5. 记录操作日志
     * </p>
     *
     * @param process 工单流程对象，包含要更新的拣货单ID列表、流程类型和备注
     *                <p>
     */
    @Override
    public void processTriggering(WorkorderProcessBO process) {
        List<Long> pickingSlipIds = process.getPickingSlipIds();
        if (ObjectUtil.isEmpty(pickingSlipIds)) {
            return;
        }
        String type = process.getProcessType().getType();
        List<OtcPickingSlip> pickingSlipList = otcPickingSlipService.listByIds(pickingSlipIds);

        List<OtcPickingSlipDetail> pickingSlipDetailList = otcPickingSlipDetailService.listByPickingSlipIds(pickingSlipIds);

        process.setPickingSlipDetailList(pickingSlipDetailList);

        process.setPickingSlipList(pickingSlipList);

        //先临时去掉 PickingSlip 要 Pick 的Qty

        for (OtcPickingSlip otcPickingSlip : pickingSlipList) {
            List<OtcPickingSlipDetail> currentPickingSlipDetailList = pickingSlipDetailList.stream()
                    .filter(x -> x.getOtcPickingSlipId().equals(otcPickingSlip.getId()))
                    .toList();

            List<OtcPackage> currentPackageList = process.getPackageList().stream()
                    .filter(x -> x.getOtcPickingSlipId().equals(otcPickingSlip.getId()))
                    .filter(x -> List.of(OtcPackageStatusEnum.NEW.getStatus(),
                            OtcPackageStatusEnum.IN_PICKING.getStatus(), OtcPackageStatusEnum.PICKED.getStatus()).contains(x.getPackageStatus()))
                    .toList();

            List<OtcPackageDetail> currentPackageDetailList = currentPackageList.stream()
                    .map(x -> process.getPackageDetailList().stream().filter(y -> y.getOtcPackageId().equals(x.getId())).toList())
                    .flatMap(Collection::stream)
                    .toList();


            for (OtcPackageDetail packageDetail : currentPackageDetailList) {
                Integer needCancelQty = packageDetail.getQty();

                for (OtcPickingSlipDetail pickingSlipDetail : currentPickingSlipDetailList) {
                    if (needCancelQty == 0) {
                        break;
                    }
                    if (!Objects.equals(pickingSlipDetail.getProductId(), packageDetail.getProductId())) {
                        continue;
                    }
                    int needPickQty = pickingSlipDetail.getQty() - pickingSlipDetail.getReadyToShipQty();
                    if (needPickQty > 0) {
                        if (needPickQty >= needCancelQty) {
                            pickingSlipDetail.setQty(pickingSlipDetail.getQty() - needCancelQty);

                            OtcPickingSlipAuditLogHelper.recordLog(pickingSlipList, "Reduce Qty", StringUtil.format("Need adjust lock packageDetail {} PickSlipDetail {} before qty: {} , after qty: {}", packageDetail.getId(), pickingSlipDetail.getId(), pickingSlipDetail.getQty() + needCancelQty, pickingSlipDetail.getQty()), process.getNote(), "Developer Process After");

                            break;
                        } else {
                            pickingSlipDetail.setQty(pickingSlipDetail.getQty() - needPickQty);

                            OtcPickingSlipAuditLogHelper.recordLog(pickingSlipList, "Reduce Qty", StringUtil.format("Need adjust lock packageDetail {}  PickSlipDetail {} before qty: {} , after qty: {}", packageDetail.getId(), pickingSlipDetail.getId(), pickingSlipDetail.getQty() + needPickQty, pickingSlipDetail.getQty()), process.getNote(), "Developer Process After");

                            needCancelQty -= needPickQty;
                        }
                    }
                }
            }

        }

        otcPickingSlipDetailService.updateBatch(pickingSlipDetailList);


        //todo: 暂时不设置 拣货单为 Cancelled,而是体现在 Note 里面

        // pickingSlipList.forEach(obj -> {
        //     // 不允许 PickingSlip 进行不同的 异常Process
        //     if (!Objects.equals(type, ProcessType.NORMAL.getType())) {
        //         if (ProcessType.abnormal().contains(obj.getProcessType())) {
        //             if (!obj.getProcessType().equals(type)) {
        //                 throw new BusinessException(StringUtil.format("{} current ProcessType is {}, Can Not be{}", obj.refNumLog(), type, obj.getProcessType()));
        //             }
        //         }
        //     }
        //     obj.setProcessType(type);
        // });

        pickingSlipList.forEach(pickingSlip -> {
            // todo: 暂时不设置 PickingSlip 为 Cancelled,而是体现在 Note 里面
            pickingSlip.setNote(pickingSlip.getNote() + StringUtil.format("{} Cancel, Cause Package {} Cancelled", process.getRequest().getRequestRefNum(), process.getPackageList().stream().map(OtcPackage::getTrackingNum).collect(Collectors.joining(","))));
        });

        // 更新
        Validate.isTrue(otcPickingSlipService.updateBatch(pickingSlipList) == pickingSlipList.size(),
                "Update PickingSlip status [{}] failed", type
        );

        // 记录日志
        OtcPickingSlipAuditLogHelper.recordLog(pickingSlipList, type, null, process.getNote(), BaseTypeLogEnum.PROCESS_TYPE.getType());
    }

    /**
     * 执行撤销拣货操作
     * <p>
     * 该方法在事务中执行，用于将已拣货的商品退回库位。
     * 撤销拣货通常用于处理拣货错误、订单取消或其他异常情况。
     * 该操作会创建上架单，将商品退回库位。
     * </p>
     * <p>
     * 操作流程：
     * 1. 获取并验证工单信息
     * 2. 获取并验证拣货单信息，确保其处于异常流程类型
     * 3. 验证工单流程类型，确保其处于异常流程类型
     * 4. 获取当前可上架详情列表
     * 5. 验证每个要撤销的详情是否有足够的可回滚数量
     * 6. 设置上架单类型（回滚或取消）
     * 7. 创建上架单
     * </p>
     *
     * @param createParam 撤销拣货创建参数，包含工单ID和要撤销的详情列表
     * @throws BusinessException 如果工单不存在、流程类型不允许撤销或可回滚数量不足，则抛出业务异常
     *                           <p>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unpick(OtcPickingSlipUnpickCreateParam createParam) {

        OtcPickingSlipUnpickBO query = BeanUtil.copyNew(createParam, OtcPickingSlipUnpickBO.class);
        query.setDetailList(BeanUtil.copyNew(createParam.getDetailList(), UnpickDetailBO.class));

        OtcWorkorder workorder = otcWorkorderService.getById(query.getWorkorderId());

        Validate.notNull(workorder, "WorkorderId {} is null", query.getWorkorderId());

        OtcPickingSlip pickingSlip = otcPickingSlipService.getById(workorder.getOtcPickingSlipId());
        Validate.isTrue(ProcessType.abnormal().contains(pickingSlip.getProcessType()),
                "{} is not {} type, can not unpick", pickingSlip.refNumLog(), ProcessType.abnormal()
        );

        Validate.isTrue(ProcessType.abnormal().contains(workorder.getProcessType()),
                "{} is not {} type, can not unpick", workorder.refNumLog(), ProcessType.abnormal()
        );

        // 当前可上架详情列表
        List<PickingSlipUnpickDetailVO> currentUnpickList = listByUnpickWithWorkorderId(workorder.getId());

        query.setCurrentUnpickList(currentUnpickList);
        query.setWorkorder(workorder);
        query.setPickingSlip(pickingSlip);
        query.setPutawaySlipClass(OtcPutawaySlip.class);
        query.setPutawaySlipDetailClass(OtcPutawaySlipDetail.class);
        // 上架类型
        query.setPutawaySlipType(ProcessType.ROLLBACKING.getType().equals(pickingSlip.getProcessType())
                ? PutAwaySlipType.ROLLBACK.getType()
                : PutAwaySlipType.CANCEL.getType()
        );

        // 校验
        PickingSlipHelper.checkUnpickCanRollback(query, currentUnpickList);

        // 创建上架单
        otcPutawaySlipService.unpick(query);
    }

    /**
     * 根据工单ID获取可撤销拣货的信息
     * <p>
     * 该方法用于获取指定工单的可撤销拣货信息，包括可回滚的拣货详情和工单信息。
     * 撤销拣货是指将已拣货的商品退回库位的操作，通常用于处理拣货错误或订单取消等情况。
     * </p>
     * <p>
     * 操作流程：
     * 1. 获取工单的可撤销拣货详情列表
     * 2. 过滤出可回滚数量大于0的记录
     * 3. 获取工单的回滚列表信息
     * 4. 构建并返回撤销拣货信息对象
     * </p>
     *
     * @param workorderId 工单ID
     * @return 撤销拣货信息对象，包含可撤销的拣货详情和工单信息
     * <p>
     * TODO: 考虑添加对工单状态的验证，确保只有特定状态的工单才能撤销拣货
     */
    @Override
    public UnpickVO unpickByWorkorderId(Long workorderId) {
        List<PickingSlipUnpickDetailVO> unpickList = this.listByUnpickWithWorkorderId(workorderId)
                .stream()
                .filter(obj -> obj.getCanRollbackQty() > 0)
                .toList();

        // 工单信息
        WorkorderRollbackListQuery query = new WorkorderRollbackListQuery();
        query.setIdList(Collections.singletonList(workorderId));
        List<WorkorderConfirmDetailVO> workorderDetailList = otcWorkorderSpecialService.confirmDetailList(query);
        return new UnpickVO(unpickList, workorderDetailList);
    }

    /**
     * 执行拣货单回滚操作
     * <p>
     * 该方法用于处理上架单完成后的拣货单回滚操作。
     * 当上架单完成时，需要相应地减少拣货单详情的已拣货数量，并更新拣货单状态。
     * 根据回滚后的情况，拣货单状态可能变为“已取消”、“新建”或“拣货中”。
     * </p>
     * <p>
     * 操作流程：
     * 1. 获取拣货单详情和拣货单信息
     * 2. 将上架单详情与拣货单详情关联
     * 3. 计算每个拣货单详情的新拣货数量（原数量减去上架数量）
     * 4. 记录变更日志
     * 5. 判断拣货单的新状态（已取消、新建或拣货中）
     * 6. 更新拣货单状态并记录日志
     * 7. 更新拣货单详情的拣货数量
     * 8. 记录回滚操作日志
     * </p>
     *
     * @param putawayParam 上架单上架更新参数，包含上架单、上架详情和上架数量信息
     * @throws BusinessException 如果更新拣货单状态或详情失败，则抛出业务异常
     *                           <p>
     */
    @Override
    public void rollback(OtcPutawaySlipPutAwayBO putawayParam) {
        OtcPutawaySlip putawaySlip = putawayParam.getPutawaySlip();
        List<OtcPickingSlipDetail> psDetails = otcPickingSlipDetailService.listByPickingSlipId(putawaySlip.getPickingSlipId());
        OtcPickingSlip pickingSlip = otcPickingSlipService.getById(putawaySlip.getPickingSlipId());

        putawayParam.setPickingSlip(pickingSlip);
        putawayParam.setPickingSlipDetails(psDetails);

        // 拣货单详情
        Map<Long, OtcPickingSlipDetail> psDetailMap = StreamUtils.toMap(psDetails, IdModel::getId);

        // Rollback
        List<ChangeQtyLogBO> changeList = putawayParam.getDetailList().stream()
                .map(param -> {
                    OtcPutawaySlipDetail putawaySlipDetail = param.getPutawaySlipDetail();
                    OtcPickingSlipDetail psDetail = psDetailMap.get(putawaySlipDetail.getPickingSlipDetailId());
                    psDetail.setPickedQty(psDetail.getPickedQty() - param.getPutawayQty());

                    // 变更数量
                    ChangeQtyLogBO change = new ChangeQtyLogBO();
                    change.setBeforeQty(psDetail.getPickedQty() + param.getPutawayQty());
                    change.setAfterQty(psDetail.getPickedQty());
                    change.setProductId(psDetail.getProductId());

                    // 库位变更
                    RefTableBO refTable = new RefTableBO();
                    refTable.setRefTableId(psDetail.getId());
                    refTable.setRefTableName(psDetail.getClass().getSimpleName());
                    refTable.setRefTableRefNum(String.valueOf(psDetail.getLineNum()));
                    refTable.setRefTableShowName(pickingSlip.getClass().getSimpleName());
                    refTable.setRefTableShowRefNum(pickingSlip.getRefNum());
                    param.setChangeLogRefTable(refTable);

                    // 设置拣货单库位锁
                    param.setPickBinLocationDetailLockedId(psDetail.getBinLocationDetailLockedId());
                    param.setPickBinLocationDetailId(psDetail.getBinLocationDetailId());
                    param.setPickBinLocationId(psDetail.getBinLocationId());
                    return change;
                })
                .toList();

        boolean isCancelled = psDetails.stream().allMatch(obj -> obj.getQty() == 0);
        boolean allRollback = psDetails.stream().allMatch(obj -> obj.getPickedQty() == 0);
        String oldStatus = pickingSlip.getPickingSlipStatus();

        pickingSlip.setPickingSlipStatus(isCancelled
                ? OtcPickingSlipStatusEnum.CANCELLED.getStatus()
                : allRollback
                ? OtcPickingSlipStatusEnum.NEW.getStatus()
                : OtcPickingSlipStatusEnum.IN_PICKING.getStatus()
        );


        // 回滚详情
        List<OtcPickingSlipDetail> rollbackDetails = putawayParam.getPickingSlipDetails();

        Validate.isTrue(otcPickingSlipDetailService.updateBatch(rollbackDetails) == rollbackDetails.size(),
                "Update PickingSlipDetail pickedQty is fail"
        );

        // 记录日志
        OtcPickingSlipAuditLogHelper.recordLog(pickingSlip, "Rollback PickedQty", JsonUtil.toJson(changeList),
                putawayParam.getNote(), BaseTypeLogEnum.OPERATION.getType()
        );

        if (!Objects.equals(oldStatus, pickingSlip.getPickingSlipStatus())) {
            Validate.isTrue(otcPickingSlipService.update(pickingSlip) == 1, "Update PickingSlip status is fail");
            OtcPickingSlipAuditLogHelper.recordLog(pickingSlip, null, putawayParam.getNote());
        }
    }

    /**
     * 回滚拣货单的准备发货状态
     * <p>
     * 该方法用于回滚拣货单的准备发货状态，通常在取消包裹的准备发货状态时调用。
     * 回滚操作会减少拣货单详情的准备发货数量，并根据情况更新拣货单状态。
     * </p>
     * <p>
     * 操作流程：
     * 1. 遍历回滚列表，对每个工单级别的回滚执行回滚准备发货数量的操作
     * 2. 更新拣货单详情的准备发货数量
     * 3. 更新拣货单状态
     * </p>
     * <p>
     * 该方法在包裹取消准备发货状态时非常重要，确保拣货单状态与包裹状态保持一致。
     * </p>
     *
     * @param rollbackList 工单级别的包裹回滚对象列表，包含要回滚的包裹、包裹详情和拣货单信息
     * @throws BusinessException 如果更新拣货单详情或拣货单失败，则抛出业务异常
     *                           <p>
     */
    @Override
    public void rollbackByPackage(List<OtcPackageRollbackSingleWorkorderBO> rollbackList) {
        if (ObjectUtil.isEmpty(rollbackList)) {
            return;
        }
        // 执行Rollback
        rollbackList.forEach(rollback -> {
            this.rollbackReadyToShipQty(rollback);
            rollbackUpdateStatus(rollback);
        });

        // 更新
        List<OtcPickingSlipDetail> pickingSlipDetailList = rollbackList.stream()
                .flatMap(rollback -> rollback.getPickingSlipDetailList().stream())
                .toList();
        Validate.isTrue(otcPickingSlipDetailService.updateBatch(pickingSlipDetailList) == pickingSlipDetailList.size(),
                "Update PickingSlipDetail rollback is fail"
        );
        List<OtcPickingSlip> pickingSlipList = StreamUtils.distinctMap(rollbackList, OtcPackageRollbackSingleWorkorderBO::getPickingSlip);
        Validate.isTrue(otcPickingSlipService.updateBatch(pickingSlipList) == pickingSlipList.size(),
                "Update PickingSlip rollback is fail"
        );
    }

    /**
     * 批量取消拣货单
     * <p>
     * 该方法用于批量取消拣货单，将指定的拣货单状态更新为“已取消”。
     * 取消操作只能对处于异常流程类型且状态为“新建”的拣货单进行。
     * 取消拣货单的同时也会取消相关的工单。
     * </p>
     * <p>
     * 操作流程：
     * 1. 获取要取消的拣货单列表
     * 2. 验证拣货单的流程类型是否为异常类型
     * 3. 验证拣货单的状态是否为“新建”
     * 4. 将拣货单状态更新为“已取消”
     * 5. 记录操作日志
     * 6. 取消相关的工单
     * </p>
     *
     * @param param 拣货单取消更新参数，包含要取消的拣货单ID列表和备注
     * @return 取消操作是否成功
     * @throws BusinessException 如果拣货单不存在、流程类型不是异常类型、状态不是“新建”或更新失败，则抛出业务异常
     *                           <p>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCancel(PickingSlipCancelUpdateParam param) {
        List<OtcPickingSlip> cancelList = otcPickingSlipService.listByIds(param.getIdList());
        Validate.notEmpty(cancelList, "PickingSlipId: {} is not exist", param.getIdList());

        // 校验流程类型
        cancelList.forEach(obj -> {
            ProcessType.checkAbnormal(obj.getProcessType(), obj.refNumLog(), "batchCancel");

            // New才可以回滚
            Validate.isTrue(Objects.equals(OtcPickingSlipStatusEnum.NEW.getStatus(), obj.getPickingSlipStatus()),
                    ErrorConstant.STATUS_ERROR_FORMAT,
                    obj.refNumLog(), "batchCancel", OtcPickingSlipStatusEnum.NEW.getStatus(), obj.getPickingSlipStatus()
            );

            // Cancelled
            obj.setPickingSlipStatus(OtcPickingSlipStatusEnum.CANCELLED.getStatus());
        });

        Validate.isTrue(otcPickingSlipService.updateBatch(cancelList) == cancelList.size(),
                "Update PickingSlip status is fail"
        );

        // 记录日志
        OtcPickingSlipAuditLogHelper.recordLog(cancelList, null, param.getNote());

        List<Long> pickingSlipIdList = StreamUtils.distinctMap(cancelList, IdModel::getId);

        // 释放锁
        List<OtcPickingSlipDetail> details = otcPickingSlipDetailService.listByPickingSlipIds(pickingSlipIdList);
        List<Long> lockedIds = StreamUtils.distinctMap(details, OtcPickingSlipDetail::getBinLocationDetailLockedId);
        binLocationDetailLockedService.releaseAll(lockedIds);

        // 工单Cancel
        otcWorkorderSpecialService.cancelWithPickingSlip(pickingSlipIdList);
        return true;
    }

    @Override
    public void split(List<OtcWorkorderSplitBO> splitHolders) {
        // 拣货单
        var pickingSlipIds = splitHolders.stream()
                .map(OtcWorkorderSplitBO::getWorkorder)
                .map(OtcWorkorder::getOtcPickingSlipId)
                .filter(Objects::nonNull)
                .toList();

        var pickingSlips = otcPickingSlipService.listByIds(pickingSlipIds);
        // 没有生成拣货单退出
        if (ObjectUtil.isEmpty(pickingSlips)) {
            return;
        }

        var psDetails = otcPickingSlipDetailService.listByPickingSlipIds(pickingSlipIds);
        var psDetailsGroupMap = StreamUtils.groupBy(psDetails, OtcPickingSlipDetail::getOtcPickingSlipId);

        // 工单详情拆单信息
        var workorderMap = splitHolders.stream()
                .map(OtcWorkorderSplitBO::getWorkorder)
                .filter(obj -> ObjectUtil.isNotNull(obj.getOtcPickingSlipId()))
                .distinct()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));

        var detailHoldersGroupMap = splitHolders.stream()
                .map(OtcWorkorderSplitBO::getDetailHolders)
                .flatMap(Collection::stream)
                // 拣货单id分组
                .collect(Collectors.groupingBy(obj -> workorderMap.get(obj.getDetail().getOtcWorkorderId()).getOtcPickingSlipId()));

        // Detail 拆单
        var psDetailPairs = psDetailsGroupMap.entrySet().stream()
                .flatMap(entry -> {
                    var currentDetailHolders = detailHoldersGroupMap.get(entry.getKey());
                    // 拆单产品数量
                    var productSplitQtyMap = currentDetailHolders.stream()
                            .collect(Collectors.groupingBy(obj -> obj.getDetail().getProductId(),
                                    Collectors.summingInt(OtcWorkorderSplitDetailBO::getSplitQty)
                            ));
                    return entry.getValue().stream()
                            // 存在产品，并可拆数量大于0
                            .filter(obj -> productSplitQtyMap.containsKey(obj.getProductId())
                                    && productSplitQtyMap.get(obj.getProductId()) > 0)
                            .sorted(Comparator.comparing(BasePickingSlipDetailModel::getLineNum))
                            .map(obj -> {
                                int needSplitQty = productSplitQtyMap.getOrDefault(obj.getProductId(), 0);
                                var canSplitQty = obj.getQty() - obj.getPickedQty();
                                var currentSplitQty = Math.min(needSplitQty, canSplitQty);
                                // 设置回去，后续计算
                                productSplitQtyMap.put(obj.getProductId(), needSplitQty - currentSplitQty);

                                // 减去拆单的数量
                                obj.setQty(obj.getQty() - currentSplitQty);

                                return Pair.of(obj, currentSplitQty);
                            });
                })
                .toList();

        // 拣货单：Update 状态
        OtcPickingSlipHelper.refreshStatus(pickingSlips, psDetailsGroupMap);

        // Step: 拣货单锁释放
        this.lockSplit(psDetailPairs);

        Validate.isTrue(otcPickingSlipService.updateBatch(pickingSlips) == pickingSlips.size(),
                "Update PickingSlip qty fail"
        );

        Validate.isTrue(otcPickingSlipDetailService.updateBatch(psDetails) == psDetails.size(),
                "Update PickingSlipDetail qty fail"
        );

    }

    /**
     * @param context
     */
    @Override
    public void cancelByRequest(OtcRequestCancelContext context) {

        List<Long> pickingSlipIds = context.getPickingSlipIds();
        if (ObjectUtil.isEmpty(pickingSlipIds)) {
            return;
        }

        String type = ProcessType.CANCELLING.getType();

        List<OtcPickingSlip> pickingSlipList = otcPickingSlipService.listByIds(pickingSlipIds);

        pickingSlipList.forEach(obj -> {
            // 不允许 PickingSlip 进行不同的 异常Process
            if (!Objects.equals(type, ProcessType.NORMAL.getType())) {
                if (ProcessType.abnormal().contains(obj.getProcessType())) {
                    if (!obj.getProcessType().equals(type)) {
                        throw new BusinessException(StringUtil.format("{} current ProcessType is {}, Can Not be{}", obj.refNumLog(), type, obj.getProcessType()));
                    }
                }
            }
            obj.setProcessType(type);
        });

        // 记录日志
        OtcPickingSlipAuditLogHelper.recordLog(pickingSlipList, "Start Cancel", null, context.getNote(), BaseTypeLogEnum.PROCESS_TYPE.getType());

        // 更新
        Validate.isTrue(otcPickingSlipService.updateBatch(pickingSlipList) == pickingSlipList.size(),
                "Update PickingSlip status [{}] failed", type
        );

    }

    /**
     * 锁拆单
     *
     * @param psDetailPairs 拣货单详情
     */
    private void lockSplit(List<Pair<OtcPickingSlipDetail, Integer>> psDetailPairs) {
        var lockedIds = psDetailPairs.stream().map(Pair::getKey)
                .map(BasePickingSlipDetailModel::getBinLocationDetailLockedId).toList();

        var lockedList = binLocationDetailLockedService.listByIds(lockedIds);
        var lockedMap = StreamUtils.toMap(lockedList, IdModel::getId);

        // 释放锁
        psDetailPairs.forEach(psDetailPair -> {
            var psDetail = psDetailPair.getKey();
            var splitQty = psDetailPair.getValue();
            var currentLocked = lockedMap.get(psDetail.getBinLocationDetailLockedId());

            currentLocked.setQty(currentLocked.getQty() - splitQty);
            var splitFinishQty = currentLocked.getFinishQty() > currentLocked.getQty()
                    ? currentLocked.getFinishQty() - currentLocked.getQty() : 0;
            // Locked: FinishQty
            currentLocked.setFinishQty(currentLocked.getFinishQty() - splitFinishQty);
            LockedHelper.statusRefresh(currentLocked);
        });
        Validate.isTrue(binLocationDetailLockedService.updateBatch(lockedList) == lockedList.size(),
                "Update BinLocationLocked qty fail"
        );
    }

    /**
     * 回滚ReadyToShipQty
     *
     * @param rollback 工单纬度回滚
     */
    private void rollbackReadyToShipQty(OtcPackageRollbackSingleWorkorderBO rollback) {
        List<OtcPackage> readyToShipPkgList = rollback.getPkgList().stream()
                .filter(obj -> Objects.equals(obj.getPackageStatus(), OtcPackageStatusEnum.READY_TO_SHIP.getStatus()))
                .toList();
        if (ObjectUtil.isEmpty(readyToShipPkgList)) {
            return;
        }

        Map<Long, OtcPackage> readyToShipMap = StreamUtils.toMap(readyToShipPkgList, OtcPackage::getId);
        // 获取ReadyToShipQty产品数量
        Map<Long, Integer> readToShipQtyWithProductMap = rollback.getPkgDetailList().stream()
                .filter(obj -> readyToShipMap.containsKey(obj.getOtcPackageId()))
                .collect(Collectors.groupingBy(OtcPackageDetail::getProductId, Collectors.summingInt(OtcPackageDetail::getPickedQty)));

        // 拣货单
        OtcPickingSlip pickingSlip = rollback.getPickingSlip();

        // 校验是否启用异常流程
        ProcessType.checkAbnormal(pickingSlip.getProcessType(), pickingSlip.refNumLog(), "rollbackReadyToShipQty");

        List<ChangeQtyLogBO> psReadyToShipChangeList = rollback.getPickingSlipDetailList().stream()
                .filter(obj -> readToShipQtyWithProductMap.containsKey(obj.getProductId()))
                .collect(Collectors.groupingBy(OtcPickingSlipDetail::getProductId))
                .entrySet()
                .stream()
                .flatMap(entry -> {
                    int remainingQty = readToShipQtyWithProductMap.get(entry.getKey());

                    List<ChangeQtyLogBO> changeList = new ArrayList<>();
                    for (OtcPickingSlipDetail detail : entry.getValue()) {
                        if (remainingQty == 0) {
                            break;
                        }
                        int rollbackQty = Math.min(remainingQty, detail.getReadyToShipQty());
                        int afterQty = detail.getReadyToShipQty() - rollbackQty;

                        ChangeQtyLogBO change = new ChangeQtyLogBO();
                        change.setBeforeQty(detail.getReadyToShipQty());
                        change.setAfterQty(afterQty);
                        change.setProductId(detail.getProductId());
                        changeList.add(change);

                        detail.setReadyToShipQty(afterQty);
                        remainingQty -= rollbackQty;
                    }

                    Validate.isTrue(remainingQty == 0, "{} PickingSlipDetail readyToShipQty is not match", pickingSlip.refNumLog());
                    return changeList.stream();
                })
                .filter(obj -> !Objects.equals(obj.getBeforeQty(), obj.getAfterQty()))
                .toList();

        // 拣货单 Rollback 记录日志
        OtcPickingSlipAuditLogHelper.recordLog(pickingSlip, RollbackConstant.ROLLBACK_READY_TO_SHIP_QTY,
                JsonUtil.toJson(psReadyToShipChangeList), rollback.getNote(), BaseTypeLogEnum.OPERATION.getType()
        );
    }

    /**
     * 获取可unpick的工单仓储位置信息
     *
     * @param workorderId 工单
     * @return /
     */
    public List<PickingSlipUnpickDetailVO> listByUnpickWithWorkorderId(Long workorderId) {
        return otcWorkorderSpecialService.unpickList(Collections.singletonList(workorderId));
    }
}
