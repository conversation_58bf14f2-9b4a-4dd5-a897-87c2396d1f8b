package cn.need.cloud.biz.service.warehouse;

import cn.need.cloud.biz.model.entity.warehouse.PalletTemplate;
import cn.need.cloud.biz.model.param.otb.update.pallet.PalletTemplateUpdateParam;
import cn.need.cloud.biz.model.param.warehouse.create.PalletTemplateCreateParam;
import cn.need.cloud.biz.model.query.warehouse.PalletTemplateQuery;
import cn.need.cloud.biz.model.vo.page.PalletTemplatePageVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletTemplateVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 打托模板 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface PalletTemplateService extends SuperService<PalletTemplate> {

    /**
     * 根据参数新增打托模板
     *
     * @param createParam 请求创建参数，包含需要插入的打托模板的相关信息
     * @return 打托模板ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(PalletTemplateCreateParam createParam);


    /**
     * 根据参数更新打托模板
     *
     * @param updateParam 请求创建参数，包含需要更新的打托模板的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(PalletTemplateUpdateParam updateParam);

    /**
     * 根据查询条件获取打托模板列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个打托模板对象的列表(分页)
     */
    List<PalletTemplatePageVO> listByQuery(PalletTemplateQuery query);

    /**
     * 根据查询条件获取打托模板列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个打托模板对象的列表(分页)
     */
    PageData<PalletTemplatePageVO> pageByQuery(PageSearch<PalletTemplateQuery> search);

    /**
     * 根据ID获取打托模板
     *
     * @param id 打托模板ID
     * @return 返回打托模板VO对象
     */
    PalletTemplateVO detailById(Long id);

    /**
     * 根据打托模板唯一编码获取打托模板
     *
     * @param refNum 打托模板唯一编码
     * @return 返回打托模板VO对象
     */
    PalletTemplateVO detailByRefNum(String refNum);

    /**
     * 获取产品默认打托模板
     *
     * @param productIds 产品集合
     * @return 产品默认打托模板
     */
    List<PalletTemplateVO> listByProductId(Collection<Long> productIds);

    /**
     * 设置默认模板
     *
     * @param palletTemplateId 打托模板id
     * @return 影响行数
     */
    Integer setDefault(Long palletTemplateId);

    /**
     * 根据产品版本id获取默认模板
     *
     * @param productVersionId 版本产品id
     */
    PalletTemplate getByProVersionIdAndDefaultFlag(Long productVersionId);

    /**
     * 根据id删除打托模板
     *
     * @param id          打托模板id
     * @param deletedNote 删除原因
     * @return 影响行数
     */
    Boolean remove(Long id, String deletedNote);
}