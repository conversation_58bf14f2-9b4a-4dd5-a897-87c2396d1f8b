package cn.need.cloud.biz.model.entity.inventory;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inventory_locked")
public class InventoryLocked extends SuperModel {


    /**
     * refTableId
     */
    @Schema(description = "refTableId")
    private Long refTableId;

    /**
     * refTableName
     */
    @Schema(description = "refTableName")
    private String refTableName;

    /**
     * refTableRefNum
     */
    @Schema(description = "refTableRefNum")
    private String refTableRefNum;

    /**
     * refTableShowName
     */
    @Schema(description = "refTableShowName")
    private String refTableShowName;

    /**
     * refTableShowRefNum
     */
    @Schema(description = "refTableShowRefNum")
    private String refTableShowRefNum;


    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;

    @TableField("finish_qty")
    private Integer finishQty;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 库存锁定状态 不能直接使用该字段做Where etc
     */
    @TableField("locked_status")
    private String lockedStatus;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

}
