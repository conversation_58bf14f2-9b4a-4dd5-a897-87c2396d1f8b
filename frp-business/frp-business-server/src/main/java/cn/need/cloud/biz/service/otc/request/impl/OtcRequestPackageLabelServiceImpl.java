package cn.need.cloud.biz.service.otc.request.impl;

import cn.need.cloud.biz.converter.otc.OtcRequestPackageLabelConverter;
import cn.need.cloud.biz.mapper.otc.OtcRequestPackageLabelMapper;
import cn.need.cloud.biz.model.entity.otc.OtcRequestPackageLabel;
import cn.need.cloud.biz.model.param.otc.create.request.OtcRequestPackageLabelCreateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestPackageLabelUpdateParam;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestPackageLabelQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPackageLabelPageVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageFullVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageLabelVO;
import cn.need.cloud.biz.service.base.FileStringUploadService;
import cn.need.cloud.biz.service.otc.request.OtcRequestPackageLabelService;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC请求包裹标签 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcRequestPackageLabelServiceImpl extends SuperServiceImpl<OtcRequestPackageLabelMapper, OtcRequestPackageLabel> implements OtcRequestPackageLabelService {


    @Resource
    private FileStringUploadService fileStringUploadService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(OtcRequestPackageLabelCreateParam createParam) {
        // 检查传入OTC请求包裹标签参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取OTC请求包裹标签转换器实例，用于将OTC请求包裹标签参数对象转换为实体对象
        OtcRequestPackageLabelConverter converter = Converters.get(OtcRequestPackageLabelConverter.class);

        // 将OTC请求包裹标签参数对象转换为实体对象并初始化
        OtcRequestPackageLabel entity = initOtcRequestPackageLabel(converter.toEntity(createParam));

        // 插入OTC请求包裹标签实体对象到数据库
        super.insert(entity);

        // 返回OTC请求包裹标签ID
        return entity.getId();
    }

    @Override
    public void insertBatchByRequestParam(Map<Long, OtcRequestPackageFullVO> idMap, Long otcRequestId, Long warehouseId) {

        // 获取OTC请求包裹标签转换器实例，用于将OTC请求包裹标签参数对象转换为实体对象
        OtcRequestPackageLabelConverter converter = Converters.get(OtcRequestPackageLabelConverter.class);

        //填充OtcRequestPackageLabel
        List<OtcRequestPackageLabel> detailEntityList = idMap.entrySet().stream().map(entry -> {
            List<OtcRequestPackageLabel> entityList = converter.toEntity(entry.getValue().getLabelList());
            entityList.forEach(entity -> {
                        entity.setOtcRequestPackageId(entry.getKey());
                        entity.setOtcRequestId(otcRequestId);
                        entity.setWarehouseId(warehouseId);

                        //todo: 这里先临时将 FileIdRawDataType 设置成 rawDataType
                        //这个值应该是上传文件来处理，现在设置有问题，通过这个值来上传文件了，后面需要修改为 RawDataType
                        //如果是FileLink 的时候，这个才应该有值，这个名字应该修改为 FileLinkRawDataType
                        if (ObjectUtil.isEmpty(entity.getFileIdRawDataType())) {
                            entity.setFileIdRawDataType(entity.getRawDataType());
                        }

                        //提前设置id 文件上传对应
                        entity.setId(IdWorker.getId());
                    }
            );
            return entityList;
        }).flatMap(Collection::stream).toList();

        // 批量上传文件 并填充实体的link
        fileStringUploadService.uploadLabelBatch(detailEntityList);

        super.insertBatch(detailEntityList);

    }


    @Override
    public void updateBatchByPackageList(List<OtcRequestPackageFullVO> updateList, Long warehouseId) {
        // 获取OTC请求包裹标签转换器实例，用于将OTC请求包裹标签参数对象转换为实体对象
        OtcRequestPackageLabelConverter detailConverter = Converters.get(OtcRequestPackageLabelConverter.class);
        //根据入参 映射 并OtcRequestBaseDetailVO转换成OtcRequestPackageLabel 实体
        Map<Long, List<OtcRequestPackageLabel>> newPackageIdMap = updateList.stream().collect(Collectors.toMap(OtcRequestPackageFullVO::getId, o -> detailConverter.toEntity(o.getLabelList())));

        //获取所有数据
        List<OtcRequestPackageLabel> listByPackageIdList = getListByPackageIdList(newPackageIdMap.keySet());
        //根据otcRequestPackageId分组 映射原始数据
        Map<Long, List<OtcRequestPackageLabel>> oldPackageIdMap = listByPackageIdList.stream().collect(Collectors.groupingBy(OtcRequestPackageLabel::getOtcRequestPackageId, Collectors.toList()));
        if (newPackageIdMap.size() != oldPackageIdMap.size()) {
            throw new BusinessException("multiBoxPackageProduct mismatch");
        }
        //创建一个集合收集要更新的数据
        ArrayList<OtcRequestPackageLabel> updatePackageLabelList = new ArrayList<>();
        //创建一个集合收集要删除的数据
        ArrayList<Long> deletePackageLabelIdList = new ArrayList<>();
        //创建一个集合收集要新增的数据
        ArrayList<OtcRequestPackageLabel> insertPackageLabelList = new ArrayList<>();

        //创建一个集合收集要上传的数据
        ArrayList<OtcRequestPackageLabel> uploadLabelList = new ArrayList<>();
        //循环入参 对比 分出要更新 删除 新增的 list
        newPackageIdMap.forEach((packageId, packageLabelList) -> {
            List<OtcRequestPackageLabel> oldList = oldPackageIdMap.get(packageId);
            //根据老集合 lineNum映射 oldId
            Map<Integer, Long> oldMap = oldList.stream().collect(Collectors.toMap(OtcRequestPackageLabel::getLineNum, OtcRequestPackageLabel::getId));
            //根据新集合 lineNum映射 新对象
            Map<Integer, OtcRequestPackageLabel> newMap = packageLabelList.stream().collect(Collectors.toMap(OtcRequestPackageLabel::getLineNum, o -> o));

            //取lineNum 交集
            Set<Integer> intersection = new HashSet<>(oldMap.keySet());
            intersection.retainAll(newMap.keySet());
            //给要更新的赋 老的id  并收集到集合中
            List<OtcRequestPackageLabel> updatePartDetailList = packageLabelList.stream().filter(o -> intersection.contains(o.getLineNum())).toList();
            updatePartDetailList.forEach(o -> o.setId(oldMap.get(o.getLineNum())));
            //需要上传文件的元素
            uploadLabelList.addAll(updatePartDetailList);
            //需要更新的元素
            updatePackageLabelList.addAll(updatePartDetailList);

            // 需要删除的数据的lineNum
            Set<Integer> delList = new HashSet<>(oldMap.keySet());
            delList.removeAll(newMap.keySet());
            if (ObjectUtil.isNotEmpty(delList)) {
                List<Long> idList = oldList.stream().filter(o -> delList.contains(o.getLineNum())).map(OtcRequestPackageLabel::getId).toList();
                //给要删除的 收集到集合中
                deletePackageLabelIdList.addAll(idList);
            }
            // 需要新增的数据的lineNum
            Set<Integer> addList = new HashSet<>(newMap.keySet());
            addList.removeAll(oldMap.keySet());
            if (ObjectUtil.isNotEmpty(addList)) {
                List<OtcRequestPackageLabel> list = packageLabelList.stream().filter(o -> addList.contains(o.getLineNum()))
                        .toList();
                // 填入otcRequestId otcRequestPackageId
                list.forEach(detailEntity -> {
                    detailEntity.setOtcRequestId(oldList.get(NumberUtils.INTEGER_ZERO).getOtcRequestId());
                    detailEntity.setOtcRequestPackageId(packageId);
                    //提前设置id 文件上传对应
                    detailEntity.setId(IdWorker.getId());
                });
                //需要上传文件的元素
                uploadLabelList.addAll(list);
                //给要新增的 收集到集合中
                insertPackageLabelList.addAll(list);
            }
        });
        // 批量上传文件 并填充实体的link
        fileStringUploadService.uploadLabelBatch(uploadLabelList);
        // 填充仓库id
        insertPackageLabelList.forEach(entity -> entity.setWarehouseId(warehouseId));
        updatePackageLabelList.forEach(entity -> entity.setWarehouseId(warehouseId));
        // 批量更新
        super.updateBatch(updatePackageLabelList);
        // 批量删除
        super.removeByIds(deletePackageLabelIdList);
        // 批量新增
        super.insertBatch(insertPackageLabelList);
    }

    /**
     * 根据包裹ID列表获取对应的OTC请求包装标签列表
     *
     * @param list 一组包裹ID的集合，用于检索对应的OTC请求包装标签
     * @return 包含与提供的包裹ID相匹配的OtcRequestPackageLabel对象的列表
     */
    private List<OtcRequestPackageLabel> getListByPackageIdList(Set<Long> list) {
        if (ObjectUtil.isEmpty(list)) {
            throw new BusinessException("PackageIdList cannot be empty");
        }
        return lambdaQuery().in(OtcRequestPackageLabel::getOtcRequestPackageId, list).list();

    }

    @Override
    public void deleteByRequestPackageIdList(List<Long> idList) {
        lambdaUpdate().set(OtcRequestPackageLabel::getRemoveFlag, DataState.ENABLED)
                .in(OtcRequestPackageLabel::getOtcRequestPackageId, idList).update();
    }

    /**
     * 初始化OTC请求包裹标签对象
     * 此方法用于设置OTC请求包裹标签对象的必要参数，确保其处于有效状态
     *
     * @param entity OTC请求包裹标签对象，不应为空
     * @return 返回初始化后的OTC请求包裹标签
     * @throws BusinessException 如果传入的OTC请求包裹标签为空，则抛出此异常
     */
    private OtcRequestPackageLabel initOtcRequestPackageLabel(OtcRequestPackageLabel entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("OtcRequestPackageLabel cannot be empty");
        }


        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(OtcRequestPackageLabelUpdateParam updateParam) {
        // 检查传入OTC请求包裹标签参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取OTC请求包裹标签转换器实例，用于将OTC请求包裹标签参数对象转换为实体对象
        OtcRequestPackageLabelConverter converter = Converters.get(OtcRequestPackageLabelConverter.class);

        // 将OTC请求包裹标签参数对象转换为实体对象
        OtcRequestPackageLabel entity = converter.toEntity(updateParam);

        // 执行更新OTC请求包裹标签操作
        return super.update(entity);

    }

    @Override
    public List<OtcRequestPackageLabelPageVO> listByQuery(OtcRequestPackageLabelQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<OtcRequestPackageLabelPageVO> pageByQuery(PageSearch<OtcRequestPackageLabelQuery> search) {
        Page<OtcRequestPackageLabel> page = Conditions.page(search, entityClass);
        List<OtcRequestPackageLabelPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtcRequestPackageLabelVO detailById(Long id) {
        OtcRequestPackageLabel entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtcRequestPackageLabel");
        }
        return buildOtcRequestPackageLabelVO(entity);
    }


    /**
     * 构建OTC请求包裹标签VO对象
     *
     * @param entity OTC请求包裹标签对象
     * @return 返回包含详细信息的OTC请求包裹标签VO对象
     */
    private OtcRequestPackageLabelVO buildOtcRequestPackageLabelVO(OtcRequestPackageLabel entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTC请求包裹标签VO对象
        return Converters.get(OtcRequestPackageLabelConverter.class).toVO(entity);
    }

}
