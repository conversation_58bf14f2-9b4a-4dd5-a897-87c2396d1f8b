package cn.need.cloud.biz.model.vo.otc.workorder;

import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipDetailPickVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC工单拣货详情信息 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC工单详情拣货 vo对象")
public class OtcWorkorderDetailPickVO extends OtcPickingSlipDetailPickVO {

    /**
     * 发货到c端工单id
     */
    @Schema(description = "发货到c端工单id")
    private Long otcWorkorderId;

    /**
     * 拣货单详情id
     */
    @Schema(description = "拣货单详情id")
    private Long otcPickingSlipDetailId;


    /**
     * 工单分配仓储位置id
     */
    @Schema(description = "工单分配仓储位置id")
    private Long otcWorkorderBinLocationId;

}