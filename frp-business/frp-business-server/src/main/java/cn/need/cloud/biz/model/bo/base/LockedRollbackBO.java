package cn.need.cloud.biz.model.bo.base;

import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 基础上架对象，拥有相同 上架数量、数量、乐观锁版本号 字段
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LockedRollbackBO {

    /**
     * 锁
     */
    private BinLocationDetailLocked locked;

    /**
     * 前数量
     */
    private Integer rollbackQty;

}