package cn.need.cloud.biz.util.log;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.cloud.biz.model.entity.otc.OtcPackageDetail;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorderDetail;
import cn.need.cloud.biz.model.vo.base.BasePackedVO;
import cn.need.cloud.biz.model.vo.base.ReadyToGoProductBinLocationPickVO;
import cn.need.cloud.biz.model.vo.log.PickLogVO;
import cn.need.cloud.biz.util.AllocationUtil;
import cn.need.cloud.biz.util.Builder;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.model.SuperModel;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/***
 * AuditLogBuilder.java
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
public class AuditLogUtil {

    /**
     * 产品版本前缀
     */
    public static final String PRODUCT_VERSION_PREFIX = "VersionInt:";

    /**
     * 打印状态前缀
     */
    public static final String PRINT_STATUS_PREFIX = "Print ";

    /**
     * 获取基础场景下的日志Builder
     *
     * @param model 实体模型
     * @return /
     */
    public static Builder<AuditShowLog> baseLog(SuperModel model) {
        return Builder.of(AuditShowLog::new)
                // 设置租户
                .with(AuditShowLog::setTenantId, TenantContextHolder.getTenantId())
                // 创建用户
                .with(AuditShowLog::setCreateBy, Objects.requireNonNull(Users.getUser()).getId())
                // 更新用户
                .with(AuditShowLog::setUpdateBy, Objects.requireNonNull(Users.getUser()).getId())
                // 是否展示
                .with(AuditShowLog::setShowFlag, Boolean.TRUE)
                // 关联表id
                .with(AuditShowLog::setRefTableId, model.getId())
                // 关联表类
                .with(AuditShowLog::setRefTableName, model.getClass().getSimpleName())
                // 关联表展示类
                .with(AuditShowLog::setRefTableShowName, model.getClass().getSimpleName())
                // 仓库id
                .with(AuditShowLog::setWarehouseId, WarehouseContextHolder.getWarehouseId());
    }

    /**
     * 获取通用场景下的日志Builder
     *
     * @param model 实体模型
     * @return /
     */
    public static Builder<AuditShowLog> commonLog(RefNumModel model) {
        return baseLog(model)
                // 关联表refNum
                .with(AuditShowLog::setRefTableRefNum, model.getRefNum())
                // 关联表展示refNum
                .with(AuditShowLog::setRefTableShowRefNum, model.getRefNum());
    }

    //////////////////////////////////////////BinLocationLog/////////////////////////////////////////////////

    /**
     * 库位日志 基础信息builder
     *
     * @param model 基类
     * @return /
     */
    public static Builder<BinLocationLog> baseBinLocationLog(RefNumModel model) {
        return Builder.of(BinLocationLog::new)
                // 设置租户
                .with(BinLocationLog::setTenantId, TenantContextHolder.getTenantId())
                // 创建用户
                .with(BinLocationLog::setCreateBy, Objects.requireNonNull(Users.getUser()).getId())
                // 更新用户
                .with(BinLocationLog::setUpdateBy, Objects.requireNonNull(Users.getUser()).getId())
                // 关联表id
                .with(BinLocationLog::setRefTableId, model.getId())
                // 关联表类
                .with(BinLocationLog::setRefTableName, model.getClass().getSimpleName())
                // 关联表展示类
                .with(BinLocationLog::setRefTableShowName, model.getClass().getSimpleName())
                // 仓库id
                .with(BinLocationLog::setWarehouseId, WarehouseContextHolder.getWarehouseId())
                // 关联表refNum
                .with(BinLocationLog::setRefTableRefNum, model.getRefNum())
                // 关联表展示refNum
                .with(BinLocationLog::setRefTableShowRefNum, model.getRefNum());
    }

    /**
     * 库位日志 builder
     *
     * @param model                  基类
     * @param afterBinLocationDetail 扣减之后的库位详情
     * @return /
     */
    public static Builder<BinLocationLog> binLocationLog(RefNumModel model, BinLocationDetail afterBinLocationDetail) {
        return Builder.of(BinLocationLog::new)
                // 设置租户
                .with(BinLocationLog::setTenantId, TenantContextHolder.getTenantId())
                // 创建用户
                .with(BinLocationLog::setCreateBy, Objects.requireNonNull(Users.getUser()).getId())
                // 更新用户
                .with(BinLocationLog::setUpdateBy, Objects.requireNonNull(Users.getUser()).getId())
                // 关联表id
                .with(BinLocationLog::setRefTableId, model.getId())
                // 关联表类
                .with(BinLocationLog::setRefTableName, model.getClass().getSimpleName())
                // 关联表展示类
                .with(BinLocationLog::setRefTableShowName, model.getClass().getSimpleName())
                // 仓库id
                .with(BinLocationLog::setWarehouseId, WarehouseContextHolder.getWarehouseId())
                // 关联表refNum
                .with(BinLocationLog::setRefTableRefNum, model.getRefNum())
                // 起始库位id
                .with(BinLocationLog::setSourceBinLocationId, afterBinLocationDetail.getBinLocationId())
                // 起始库位详情
                .with(BinLocationLog::setSourceBinLocationDetailId, afterBinLocationDetail.getId())
                // 起始库位详情发生变化的数量
                .with(BinLocationLog::setSourceAfterInStockQty, afterBinLocationDetail.getInStockQty())
                // 目的库位id
                .with(BinLocationLog::setDestBinLocationId, afterBinLocationDetail.getBinLocationId())
                // 目的库位详情id
                .with(BinLocationLog::setDestBinLocationDetailId, afterBinLocationDetail.getId())
                // 产品id
                .with(BinLocationLog::setProductId, afterBinLocationDetail.getProductId())
                // 产品版本
                .with(BinLocationLog::setProductVersionId, afterBinLocationDetail.getProductVersionId())
                // 关联表展示refNum
                .with(BinLocationLog::setRefTableShowRefNum, model.getRefNum());
    }

    /**
     * 相同库位日志 builder,
     * 需要设置好
     * 这里会自动赋值Dest 与 Source 相同
     *
     * @param model           基类
     * @param sameBinLocation 库位详情
     * @return /
     */
    public static Builder<BinLocationLog> sameBinLocationLog(RefNumModel model, BinLocationDetail sameBinLocation) {
        // 需要自己设置好Source相关信息
        return binLocationLog(model, sameBinLocation)
                // 设置Dest与Source相同
                .last(log -> {
                    log.setDestBinLocationId(log.getSourceBinLocationId());
                    log.setDestBinLocationDetailId(log.getSourceBinLocationDetailId());
                    log.setDestChangeInStockQty(log.getSourceChangeInStockQty());
                    log.setDestBeforeInStockQty(log.getSourceBeforeInStockQty());
                    log.setDestAfterInStockQty(log.getSourceAfterInStockQty());
                });
    }

    /**
     * 拣货日志封装
     *
     * @param pickList 拣货信息
     * @return /
     */
    public static <T extends ReadyToGoProductBinLocationPickVO> String pickLogDesc(Collection<T> pickList) {
        return JsonUtil.toJson(pickList.stream()
                .filter(obj -> obj.getChangePickQty() > 0)
                .map(obj -> {
                    PickLogVO log = BeanUtil.copyNew(obj, PickLogVO.class);
                    log.setQty(obj.getChangePickQty());
                    return log;
                })
                .toList());
    }

    /**
     * 分配打包数量
     *
     * @param packageDetailList         包裹详情
     * @param wkDetailGroupByProductMap 工单详情
     * @return /
     */
    public static List<BasePackedVO> allocationPackedList(List<OtcPackageDetail> packageDetailList, Map<Long, List<OtcWorkorderDetail>> wkDetailGroupByProductMap) {
        return packageDetailList.stream()
                .filter(obj -> wkDetailGroupByProductMap.containsKey(obj.getProductId()))
                .map(pkgDetail -> {
                    List<BasePackedVO> wkDetails = BeanUtil.copyNew(wkDetailGroupByProductMap.get(pkgDetail.getProductId()), BasePackedVO.class);
                    // 分配打包数量
                    AllocationUtil.checkAndAllocationPackedQty(wkDetails, pkgDetail.getQty());
                    Map<Long, OtcWorkorderDetail> wkDetailMap = StreamUtils.toMap(wkDetailGroupByProductMap.get(pkgDetail.getProductId()), IdModel::getId);

                    wkDetails.stream()
                            .filter(obj -> obj.getChangePackedQty() > 0)
                            .forEach(obj -> wkDetailMap.get(obj.getId()).setPackedQty(obj.getPackedQty()));
                    return wkDetails;
                })
                .flatMap(List::stream)
                .toList();
    }

}
