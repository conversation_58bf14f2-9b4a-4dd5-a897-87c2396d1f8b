package cn.need.cloud.biz.mapper.otb;

import cn.need.cloud.biz.model.entity.otb.OtbPickingSlipDetail;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipDetailQuery;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipSummaryVO;
import cn.need.cloud.biz.model.vo.page.OtbPickingSlipDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * otb拣货单详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtbPickingSlipDetailMapper extends SuperMapper<OtbPickingSlipDetail> {

    /**
     * 根据条件获取otb拣货单详情列表
     *
     * @param query 查询条件
     * @return otb拣货单详情集合
     */
    default List<OtbPickingSlipDetailPageVO> listByQuery(OtbPickingSlipDetailQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取otb拣货单详情分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return otb拣货单详情集合
     */
    List<OtbPickingSlipDetailPageVO> listByQuery(@Param("qo") OtbPickingSlipDetailQuery query, @Param("page") Page<?> page);

    /**
     * 产品+库位汇总查询
     *
     * @param pickingSlipIdList pickingSlipIdList
     * @return /
     */
    List<OtbPickingSlipSummaryVO> summary(@Param("psIds") List<Long> pickingSlipIdList);
}