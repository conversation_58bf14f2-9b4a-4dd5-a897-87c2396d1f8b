package cn.need.cloud.upms.cache;

import cn.need.cloud.upms.cache.bean.TenantCache;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
public class TenantRedisTemplate {

    public static final String REDIS_TENANT_KEY = "TENANT:CACHE";
    private final BoundHashOperations<String, String, TenantCache> operations;

    public TenantRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.operations = hash(redisTemplate);
    }

    public BoundHashOperations<String, String, TenantCache> hash() {
        return operations;
    }

    public BoundHashOperations<String, String, TenantCache> hash(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.boundHashOps(REDIS_TENANT_KEY);
    }
}
