package cn.need.cloud.log.util;

import cn.need.cloud.log.model.vo.RequestPathPatternVO;
import cn.need.cloud.log.model.vo.req.LoggerReqVO;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.lang.Validate;

import static cn.need.framework.common.core.lang.ObjectUtil.isNull;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
public class LoggerRecordValidate {

    public static void queryDate(LoggerReqVO condition) {
     /*   if (isNull(condition)) {
            throw new ParameterException("查询条件不能为空");
        }

        if (isNull(condition.getStart())) {
            throw new ParameterException("开始时间不能为空");
        }

        if (isNull(condition.getEnd())) {
            throw new ParameterException("结束时间不能为空");
        }*/
    }

    public static void pattern(RequestPathPatternVO vo) {
        Validate.notEmpty("请求路径不能为空");
        Validate.notEmpty("请求来源不能为空");
        Validate.notEmpty("操作功能不能为空");
        Validate.notEmpty("操作模块不能为空");
        if (isNull(vo.getState())) {
            vo.setState(DataState.ENABLED);
        }
        if (isNull(vo.getRecordResponseData())) {
            vo.setRecordResponseData(DataState.ENABLED);
        }
    }
}
