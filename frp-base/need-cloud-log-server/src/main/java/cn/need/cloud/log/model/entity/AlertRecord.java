package cn.need.cloud.log.model.entity;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 预警日志数据
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("log_alert_record")
public class AlertRecord extends SuperModel {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 来源
     */
    @TableField("source")
    private String source;

    /**
     * 关健字
     */
    @TableField("keyword")
    private String keyword;

    /**
     * 功能模块
     */
    @TableField("module")
    private String module;

    /**
     * 业务功能说明
     */
    @TableField("function_desc")
    private String functionDesc;

    /**
     * 请求url地址
     */
    @TableField("request_url")
    private String requestUrl;

    /**
     * 请求参数内容
     */
    @TableField("request_body")
    private String requestBody;

    /**
     * 预警信息
     */
    @TableField("alert_info")
    private String alertInfo;
}
