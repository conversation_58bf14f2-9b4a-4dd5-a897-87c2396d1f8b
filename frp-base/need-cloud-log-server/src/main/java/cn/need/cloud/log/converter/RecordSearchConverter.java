package cn.need.cloud.log.converter;

import cn.need.cloud.log.client.dto.RecordSearchDTO;
import cn.need.cloud.log.model.entity.RecordSearch;
import cn.need.cloud.log.model.vo.RecordSearchVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * 日志记录查询信息  对象转换器
 *
 * <AUTHOR>
 */
public class RecordSearchConverter extends AbstractModelConverter<RecordSearch, RecordSearchVO, RecordSearchDTO> {

}