package cn.need.cloud.dict.client.dto.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DictionaryReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -5645058529938385427L;

    /**
     * 深度
     */
    private Integer depth;

    /**
     * 父级编码
     */
    private String parentCode;

    /**
     * 根级字典编码
     */
    private String rootCode;

}
