package cn.need.cloud.dict.client.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 行政区域 dto对象
 *
 * <AUTHOR>
 */
@Data
public class RegionDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    private Long id;

    /**
     * 行政区域编号
     */
    private String regionCode;

    /**
     * 行政区域名称
     */
    private String regionName;

    /**
     * 行政区域名称拼音
     */
    private String regionNamePy;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 简称拼音
     */
    private String shortNamePy;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 维度
     */
    private BigDecimal latitude;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 深度，用来记录树结构的层级关系
     */
    private Integer depth;

    /**
     * 路径，用来记录树结构数据编码的路径，用','分隔
     */
    private String path;

    /**
     * 排序字段
     */
    private Double sorting;

    /**
     * 父级编码
     */
    private String parentCode;


    /**
     * 父级区域编码
     */
    private String parentRegionCode;

    /**
     * 区域编码全
     */
    private String fullRegionCode;

    /**
     * 区域编码全名称
     */
    private String fullRegionName;

}