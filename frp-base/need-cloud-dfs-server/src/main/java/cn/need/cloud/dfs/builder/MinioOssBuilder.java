package cn.need.cloud.dfs.builder;

import cn.need.cloud.dfs.props.Oss;
import cn.need.framework.common.oss.props.OssProperties;
import cn.need.framework.common.oss.rule.OssRule;
import cn.need.framework.common.oss.template.MinioTemplate;
import cn.need.framework.common.oss.template.OssTemplate;
import io.minio.MinioClient;
import lombok.SneakyThrows;
import okhttp3.HttpUrl;

/**
 * Minio云存储构建类
 *
 * <AUTHOR>
 */
public class MinioOssBuilder {

    @SneakyThrows
    public static OssTemplate template(Oss oss, OssRule ossRule) {
        HttpUrl.get(oss.getEndpoint());
        MinioClient minioClient = new MinioClient(oss.getEndpoint(), oss.getAccessKey(), oss.getSecretKey());

//     TODO 8.5.7 版本写法与 need-common-oss 组件不兼容        MinioClient.builder()
//                .endpoint(oss.getEndpoint())
//                .credentials(oss.getAccessKey(), oss.getSecretKey())
//                .build();
        OssProperties ossProperties = new OssProperties();
        ossProperties.setEndpoint(oss.getEndpoint());
        ossProperties.setAccessKey(oss.getAccessKey());
        ossProperties.setSecretKey(oss.getSecretKey());
        ossProperties.setBucketName(oss.getBucketName());
        return new MinioTemplate(minioClient, ossRule, ossProperties);
    }

}
