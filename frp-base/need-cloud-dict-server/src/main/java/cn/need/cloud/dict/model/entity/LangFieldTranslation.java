package cn.need.cloud.dict.model.entity;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 平台多语言字段定义
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dict_lang_field_translation")
public class LangFieldTranslation extends SuperModel {


    /**
     * 语言编码
     */
    @TableField("lang_code")
    private String langCode;

    /**
     * 语言翻译
     */
    @TableField("lang_field_translation")
    private String langFieldTranslation;

    /**
     * 语言字段id
     */
    @TableField("lang_field_id")
    private Long langFieldId;

    /**
     * 语音提示id
     */
    @TableField("lang_field_tips_id")
    private Long langFieldTipsId;

    /**
     * 语言提示
     */
    @TableField("lang_field_tips")
    private String langFieldTips;

}
