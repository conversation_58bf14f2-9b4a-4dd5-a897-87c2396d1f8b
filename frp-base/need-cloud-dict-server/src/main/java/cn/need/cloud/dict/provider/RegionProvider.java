package cn.need.cloud.dict.provider;

import cn.need.cloud.dict.client.api.RegionClient;
import cn.need.cloud.dict.client.api.path.DictClientPath;
import cn.need.cloud.dict.client.api.path.RegionClientPath;
import cn.need.cloud.dict.client.dto.RegionDTO;
import cn.need.cloud.dict.client.dto.req.RegionReqDTO;
import cn.need.cloud.dict.converter.RegionConverter;
import cn.need.cloud.dict.model.entity.Region;
import cn.need.cloud.dict.service.RegionService;
import cn.need.cloud.dict.util.GeneralUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.exception.unchecked.ParameterException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * 行政区域功能客户端实现.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(RegionClientPath.PREFIX)
@RequiredArgsConstructor
public class RegionProvider implements RegionClient {

    private final RegionService regionService;


    @PostMapping(RegionClientPath.SAVE)
    @Operation(hidden = true)
    @Override
    public Result<Integer> save(@RequestBody RegionDTO dto) {
        log.info("-----> {}, dto={}", RegionClientPath.PREFIX.concat(RegionClientPath.SAVE), JsonUtil.toJson(dto));
        try {
            //1. 校验数据
            validateRegion(dto);
            //2. 优先根据父级编码，获取父级信息
            Region parent = GeneralUtil.isParentEmpty(dto) ? null : Validate.notNull(regionService.getByRegionCode(dto.getParentCode()),
                    "不能根据父级编码[" + dto.getParentCode() + "]，获取到对应的行政区域数据");
            //再根据行政区域编码，获取已存在的数据
            Region data = regionService.getByRegionCode(dto.getRegionCode());
            //构建行政区域数据
            Region region = GeneralUtil.buildRegion(dto, parent, data);
            int result = isNotNull(data) ? regionService.update(region) : regionService.insert(region);
            return Result.ok(result);
        } catch (ParameterException e) {
            log.error(e.getMessage(), e);
            return Result.fail(GeneralUtil.getExceptionCode(e), e.getMessage());
        }
    }

    @PostMapping(RegionClientPath.SAVE_BATCH)
    @Operation(hidden = true)
    @Override
    public Result<Integer> saveBatch(@RequestBody List<RegionDTO> dtoList) {
        log.info("-----> {}, dtoList={}", RegionClientPath.PREFIX.concat(RegionClientPath.SAVE_BATCH), JsonUtil.toJson(dtoList));
        try {
            //1. 校验数据
            Validate.notEmpty(dtoList, "行政区域数据集合不能为空");
            List<String> regionCodes = Lists.arrayList();
            dtoList.forEach(it -> {
                validateRegion(it);
                if (regionCodes.contains(it.getRegionCode())) {
                    throw new BusinessException(400, "行政区域[" + it.getRegionCode() + "]重复！");
                }
                //添加到集合中
                regionCodes.add(it.getRegionCode());
            });
            //2. 将行政区域按照深度分组，批量执行保存
            Map<Integer, List<RegionDTO>> listMap = ObjectUtil.toMapList(dtoList, RegionDTO::getDepth);
            //3. 按照深度由低到高，依次执行批量处理
            AtomicInteger result = new AtomicInteger(0);
            listMap.keySet().stream().sorted().forEach(it -> result.addAndGet(regionService.saveBatch(it, listMap.get(it))));
            //4. 开启异步线程，将数据初始化至缓存
            CompletableFuture.runAsync(() -> {
                int count = regionService.initialize();
                log.info("----------->> 初始化行政区域至缓存，执行完成！count={}", count);
            }, ThreadUtil.globalThreadPool());
            return Result.ok(result.get());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.fail(GeneralUtil.getExceptionCode(e), e.getMessage());
        }
    }

    @GetMapping(RegionClientPath.DELETE_BY_CODE)
    @Operation(hidden = true)
    @Override
    public Result<Integer> deleteByCode(@RequestParam("regionCode") String regionCode) {
        log.info("-----> {}, regionCode={}", RegionClientPath.PREFIX.concat(RegionClientPath.DELETE_BY_CODE), regionCode);
        try {
            //根据编号，获取行政区域id
            Region entity = regionService.getByRegionCode(regionCode);
            int result = isNull(entity) ? 0 : regionService.removeById(entity.getId());
            return Result.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.fail(GeneralUtil.getExceptionCode(e), e.getMessage());
        }
    }

    @PostMapping(RegionClientPath.GET_REGION)
    @Operation(hidden = true)
    @Override
    public Result<PageData<RegionDTO>> getRegion(@RequestBody PageSearch<RegionReqDTO> search) {
        log.info("-----> {}, search={}", DictClientPath.PREFIX.concat(RegionClientPath.GET_REGION), JsonUtil.toJson(search));
        try {
            //构建查询条件，只获取有效的数据
            LambdaQueryWrapper<Region> wrapper = Wrappers.lambdaQuery();
            RegionReqDTO param = nullToDefault(search.getCondition(), new RegionReqDTO());
            //深度不为空
            if (isNotNull(param.getDepth())) {
                wrapper.eq(Region::getDepth, param.getDepth());
            }
            //父级编码不为空
            if (ObjectUtil.isNotEmpty(param.getParentCode())) {
                wrapper.eq(Region::getParentId, ObjectUtil.convert(param.getParentCode(), -1L));
            }
            //执行分页查询，获取行政区域数据
            IPage<Region> page = regionService.page(new Page<>(search.getCurrent(), search.getSize()), wrapper);
            //响应数据
            return Result.ok(Converters.get(RegionConverter.class).toDTOPage(page));
        } catch (Exception e) {
            log.error("根据分页条件，获取行政区域信息异常：{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @PostMapping(RegionClientPath.FIND_BY_CONDITION)
    @Operation(hidden = true)
    @Override
    public Result<List<RegionDTO>> findByCondition(@RequestBody RegionReqDTO condition) {
        return Result.ok(BeanUtil.copyNew(regionService.findByCondition(condition), RegionDTO.class));
    }

    /**
     * 校验行政区域数据
     */
    private void validateRegion(RegionDTO dto) {
        Validate.notNull(dto, "行政区域数据不能为空");
        //去空格处理
        BeanUtil.trimProperty(dto);
        Validate.notEmpty(dto.getRegionCode(), "行政区域编码不能为空");
        Validate.notEmpty(dto.getRegionName(), "行政区域名称不能为空");
        Validate.notNull(dto.getDepth(), "行政区域深度不能为空");
        //深度大于1的情况，校验父级编码或父级id不能为空
        if (dto.getDepth() > 1) {
            Validate.notEmpty(dto.getParentCode(), "父级行政区域不能为空");
        }
    }

}
