package cn.need.cloud.upms.service.permissions;

import cn.need.cloud.upms.mapper.permissions.BusinessRoleMapper;
import cn.need.cloud.upms.model.entity.permissions.BusinessRole;
import cn.need.cloud.upms.model.vo.permissions.BusinessAndRoleVO;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serial;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务角色信息 服务实现
 *
 * <AUTHOR>
 */
@Service
public class BusinessRoleServiceImpl extends SuperServiceImpl<BusinessRoleMapper, BusinessRole> implements BusinessRoleService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertOrUpdateBatch(String businessType, Long businessId, List<Long> roleIds) {
        // 删除指定角色对应的业务关联关系
        removeByBusinessId(businessType, businessId);
        // 新增角色对应的业务关联关系
        return ObjectUtil.isEmpty(roleIds) ? 0 : insertBatch(
                roleIds.stream().map(c -> new BusinessRole() {
                    @Serial
                    private static final long serialVersionUID = 5601922003932681653L;

                    {
                        setBusinessType(businessType);
                        setBusinessId(businessId);
                        setRoleId(c);
                    }
                }).collect(Collectors.toList())
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertBatch(String businessType, Long roleId, List<Long> businessIds) {
        return ObjectUtil.isEmpty(businessIds) ? 0 : insertBatch(
                businessIds.stream().map(c -> new BusinessRole() {
                    @Serial
                    private static final long serialVersionUID = 2818371887404604324L;

                    {
                        setRoleId(roleId);
                        setBusinessId(c);
                        setBusinessType(businessType);
                    }
                }).collect(Collectors.toList())
        );
    }

    @Override
    public List<Long> roleIdsByBusinessId(String businessType, Long businessId) {
        return lambdaQuery()
                .eq(BusinessRole::getBusinessType, businessType)
                .eq(BusinessRole::getBusinessId, businessId)
                .list().stream().map(BusinessRole::getRoleId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<Long> roleIdsByBusinessIdAndRoleGroups(String businessType, Long businessId, List<String> roleGroups) {
        return mapper.roleIdsByBusinessIdAndRoleGroups(businessType, businessId, roleGroups);
    }

    @Override
    public List<BusinessAndRoleVO> roleIdsByBusinessIdsAndType(String businessType, List<Long> businessIdList) {
        if (ObjectUtil.isEmpty(businessIdList)) {
            return Collections.emptyList();
        }
        return mapper.roleIdsByBusinessIdsAndType(businessType, businessIdList);
    }

    @Override
    public List<Long> businessIdsByRoleId(String businessType, List<Long> roleId) {
        return lambdaQuery()
                .eq(BusinessRole::getBusinessType, businessType)
                .in(BusinessRole::getRoleId, roleId)
                .list().stream().map(BusinessRole::getBusinessId).distinct().collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByBusinessIds(String businessType, List<Long> businessIds) {
        if (ObjectUtil.isEmpty(businessIds)) {
            return false;
        }
        LambdaUpdateChainWrapper<BusinessRole> wrapper = lambdaUpdate().eq(BusinessRole::getBusinessType, businessType).in(BusinessRole::getBusinessId, businessIds);
        return wrapper.remove();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByRoleIdAndBusinessId(String businessType, Long roleId, Long businessId) {
        LambdaUpdateChainWrapper<BusinessRole> wrapper = lambdaUpdate().eq(BusinessRole::getBusinessId, businessId);
        if (ObjectUtil.isNotEmpty(roleId)) {
            wrapper.eq(BusinessRole::getRoleId, roleId);
        }
        return wrapper.remove();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByBusinessIdAndRoleIds(String businessType, Long businessId, List<Long> roleIds) {
        return lambdaUpdate().eq(BusinessRole::getBusinessId, businessId).eq(BusinessRole::getBusinessType, businessType).in(BusinessRole::getRoleId, roleIds).remove();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByBusinessIdsAndRoleIds(String businessType, List<Long> businessIds, List<Long> roleIds) {
        return lambdaUpdate().eq(BusinessRole::getBusinessType, businessType)
                .in(BusinessRole::getRoleId, roleIds)
                .in(BusinessRole::getBusinessId, businessIds)
                .remove();
    }

    @Override
    public List<BusinessRole> listByBusinessIds(List<Long> userIds) {
        return ObjectUtil.isEmpty(userIds) ? Lists.arrayList() : lambdaQuery().in(BusinessRole::getBusinessId, userIds).list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByRoleIds(String businessType, Long roleId) {
        if (ObjectUtil.isEmpty(roleId)) {
            return false;
        }
        LambdaUpdateChainWrapper<BusinessRole> wrapper = lambdaUpdate().eq(BusinessRole::getRoleId, roleId);
        return wrapper.remove();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean removeByRoleIdAndUserIds(Long roleId, List<Long> userIds) {
        if (ObjectUtil.isEmpty(roleId) || ObjectUtil.isEmpty(userIds)) {
            return false;
        }
        return lambdaUpdate().in(BusinessRole::getBusinessId, userIds).eq(BusinessRole::getRoleId, roleId).remove();
    }

}
