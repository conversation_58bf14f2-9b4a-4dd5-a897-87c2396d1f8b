package cn.need.cloud.upms.model.vo.permissions;

import cn.need.framework.common.mybatis.model.SuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 系统角色信息 simple vo对象
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "系统角色信息 simple vo对象")
public class RoleSimpleVO extends SuperVO {

    /**
     * 角色编号
     */
    @Schema(description = "角色编号")
    private String roleCode;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleName;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleNameEn;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段")
    private Integer sorting;

}