package cn.need.cloud.upms.mapper.permissions;

import cn.need.cloud.upms.client.dto.RoleDTO;
import cn.need.cloud.upms.model.entity.permissions.Role;
import cn.need.cloud.upms.model.query.role.RoleQuery;
import cn.need.cloud.upms.model.vo.permissions.RolePageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统角色信息 Mapper接口
 *
 * <AUTHOR>
 */
public interface RoleMapper extends SuperMapper<Role> {


    /**
     * 通过搜索条件查询角色 工作流
     *
     * @param filter 搜索角色
     * @return 角色列表
     */
    List<Role> findGroupsByNameFilterForWorkFlow(@Param("filter") String filter);

    /**
     * 查询角色信息
     *
     * @param page    分页
     * @param roleDTO 查询信息
     * @return Page<Role>
     */
    List<Role> pageByCondition(Page<RoleDTO> page, @Param("roleDTO") RoleDTO roleDTO);


    /**
     * 根据条件获取系统角色信息列表
     *
     * @param query 查询条件
     * @return 系统角色信息集合
     */
    default List<RolePageVO> listByQuery(RoleQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取系统角色信息分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 系统角色信息集合
     */
    List<RolePageVO> listByQuery(@Param("qo") RoleQuery query, @Param("page") Page<?> page);
}
