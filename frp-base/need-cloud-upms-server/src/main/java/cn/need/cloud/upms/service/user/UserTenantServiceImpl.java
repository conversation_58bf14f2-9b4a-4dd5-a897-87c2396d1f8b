package cn.need.cloud.upms.service.user;

import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.cloud.upms.client.constant.BusinessRoleType;
import cn.need.cloud.upms.converter.user.UserTenantConverter;
import cn.need.cloud.upms.mapper.user.UserTenantMapper;
import cn.need.cloud.upms.model.entity.permissions.Role;
import cn.need.cloud.upms.model.entity.user.UserTenant;
import cn.need.cloud.upms.model.param.tenant.UserPasswordUpdateParam;
import cn.need.cloud.upms.model.param.tenant.UserTenantCreateParam;
import cn.need.cloud.upms.model.param.tenant.UserTenantUpdateParam;
import cn.need.cloud.upms.model.query.tenant.UserTenantQuery;
import cn.need.cloud.upms.model.vo.tenant.UserTenantPageVO;
import cn.need.cloud.upms.model.vo.tenant.UserTenantVO;
import cn.need.cloud.upms.service.permissions.BusinessRoleService;
import cn.need.cloud.upms.service.permissions.RoleService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.dict.cache.UserRepertory;
import cn.need.framework.common.dict.entity.UserCache;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.netty.util.internal.StringUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static cn.need.framework.common.core.lang.ObjectUtil.isEmpty;
import static cn.need.framework.common.core.lang.ObjectUtil.isNotEmpty;

/**
 * <p>
 * 用户租户信息 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Service
public class UserTenantServiceImpl extends SuperServiceImpl<UserTenantMapper, UserTenant> implements UserTenantService {

    @Lazy
    @Resource
    private UserService userService;

    @Resource
    private BusinessRoleService businessRoleService;

    @Resource
    private RoleService roleService;

    @Resource
    private TenantCacheService tenantCacheService;

    @Resource
    private UserRepertory userRepertory;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(UserTenantCreateParam createParam) {

        // 将用户租户信息参数对象转换为实体对象并初始化
        UserTenant entity = buildUserTenant(createParam);

        // 插入用户租户信息实体对象到数据库
        super.insert(entity);

        // 更新用户缓存
        updateRedis(entity);

        // 保存关联角色信息
        businessRoleService.insertOrUpdateBatch(BusinessRoleType.USER_TENANT, entity.getId(), createParam.getRoleIdList());

        // 返回用户租户信息ID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(UserTenantUpdateParam updateParam) {

        // 将用户租户信息参数对象转换为实体对象并初始化
        UserTenant entity = buildUserTenant(updateParam);

        // 执行更新用户租户信息操作
        int update = super.update(entity);

        // 更新用户缓存
        updateRedis(getById(entity.getId()));

        // 返回影响数据条数
        return update;
    }

    @Override
    public List<UserTenantPageVO> listByQuery(UserTenantQuery query) {
        // 调用Mapper方法，根据查询条件获取用户租户页面信息列表
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<UserTenantPageVO> pageByQuery(PageSearch<UserTenantQuery> search) {
        // 根据查询条件和实体类获取分页对象
        Page<UserTenant> page = Conditions.page(search, entityClass);
        // 调用mapper方法，根据查询条件和分页信息获取数据列表
        List<UserTenantPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        // 将获取的数据列表和分页信息封装到PageData对象中返回
        return new PageData<>(buildUserTenantPageVO(dataList), page);
    }

    @Override
    public UserTenantVO detailById(Long id) {

        // 根据ID获取UserTenant实体
        UserTenant entity = getById(id);

        // 检查获取的实体是否为空，如果为空则抛出业务异常
        if (isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in UserTenant");
        }

        // 将获取到的实体转换为UserTenantVO对象并返回
        return buildUserTenantVO(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateTenantStatus(Long id) {
        // 根据ID获取租户信息
        UserTenant info = getById(id);
        // 更新租户的激活状态：如果当前状态为激活（1），则改为未激活（0），反之亦然
        info.setActiveFlag(info.getActiveFlag() == 1 ? 0 : 1);
        // 调用父类的update方法更新租户信息
        return super.update(info);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer resetUserPassword(UserPasswordUpdateParam userPasswordUpdateParam) {

        // 根据用户ID获取用户租户信息
        UserTenant userTenant = getById(userPasswordUpdateParam.getId());

        // 如果用户租户信息为空，抛出业务异常
        if (isEmpty(userTenant)) {
            throw new BusinessException("id: " + userPasswordUpdateParam.getId() + " not found in UserTenant");
        }

        // 调用用户服务的修改密码方法，传入用户ID、旧密码和新密码
        return userService.resetUserPassWord(userTenant.getUserId(), userPasswordUpdateParam.getNewPassword());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByTenantId(Long tenantId) {
        lambdaUpdate().eq(UserTenant::getTenantId, tenantId).remove();
    }

    @Override
    public List<UserTenant> listByUserId(Long userId) {
        return lambdaQuery().eq(UserTenant::getUserId, userId).list();
    }

    @Override
    public Integer modifyUserPassword(UserPasswordUpdateParam userPasswordUpdateParam) {
        // 根据用户ID获取用户租户信息
        UserTenant userTenant = getById(Users.id());

        // 如果用户租户信息为空，抛出业务异常
        if (isEmpty(userTenant)) {
            throw new BusinessException("id: " + userPasswordUpdateParam.getId() + " not found in UserTenant");
        }

        // 调用用户服务的修改密码方法，传入用户ID、旧密码和新密码
        return userService.modifyUserPassword(userTenant.getUserId(), userPasswordUpdateParam.getPassword(), userPasswordUpdateParam.getNewPassword());
    }

    @Override
    public void initUserCache() {

        // 获取所有用户租户列表
        List<UserTenant> userTenantList = list();

        // 检查用户租户列表是否不为空
        if (ObjectUtil.isNotEmpty(userTenantList)) {

            // 将用户租户列表转换为用户缓存列表，设置用户名、姓名和昵称
            List<UserCache> list = userTenantList.stream().map(userTenant -> {
                String name = userTenant.getFirstName() + StringUtil.SPACE + userTenant.getLastName();
                UserCache userCache = BeanUtil.copyNew(userTenant, UserCache.class);
                userCache.setUsername(name);
                userCache.setName(name);
                userCache.setNickName(name);
                return userCache;
            }).collect(Collectors.toList());

            // 调用用户存储初始化方法，传入转换后的用户缓存列表和初始化参数
            userRepertory.initialization(list, 1000);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeRoleByTenantId(Long id, Collection<String> delPartnerType) {
        // 检查待删除的合作伙伴类型是否非空
        if (ObjectUtil.isNotEmpty(delPartnerType)) {
            // 根据合作伙伴类型获取角色列表
            List<Role> roleList = roleService.listByGroupType(null, delPartnerType);
            // 设置当前操作的租户ID
            TenantContextHolder.setTenantId(id);
            // 获取当前租户下的所有用户租户列表
            List<UserTenant> userTenantList = list();
            // 如果用户租户列表和角色列表均非空，则执行删除操作
            if (ObjectUtil.isNotEmpty(userTenantList) && ObjectUtil.isNotEmpty(roleList)) {
                businessRoleService.removeByBusinessIdsAndRoleIds(
                        BusinessRoleType.USER_TENANT
                        , userTenantList.stream().map(UserTenant::getId).toList()
                        , roleList.stream().map(Role::getId).toList()
                );
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeAndNote(Long id, String note) {
        // 执行删除
        return super.removeAndNote(id, note);
    }

    /**
     * 更新Redis中的用户信息
     * 此方法用于将用户和租户信息更新到Redis缓存中它通过将UserTenant对象转换为UserCache对象，
     * 并设置用户的用户名、名称和昵称为用户姓氏和名字的组合，然后将该用户信息添加到用户仓库中
     *
     * @param userTenant 用户和租户信息，包含用户的基本信息和租户关联信息
     */
    public void updateRedis(UserTenant userTenant) {
        // 将UserTenant对象复制为UserCache对象，便于操作和缓存
        UserCache userCache = BeanUtil.copyNew(userTenant, UserCache.class);
        // 设置用户名为用户姓氏和名字的组合，便于统一用户标识
        userCache.setUsername(userTenant.getFirstName() + userTenant.getLastName());
        // 设置名称为用户姓氏和名字的组合，用于显示用户的全名
        userCache.setName(userTenant.getFirstName() + userTenant.getLastName());
        // 设置昵名为用户姓氏和名字的组合，用于用户在系统中的昵称
        userCache.setNickName(userTenant.getFirstName() + userTenant.getLastName());
        // 将用户信息添加到用户仓库中，以更新Redis缓存
        userRepertory.add(userCache);
    }


    // =============================   私有方法   =========================

    /**
     * 验证创建用户租户关系的参数，确保数据完整性和符合业务规则。
     *
     * @param createParam 创建用户租户关系的参数，包含用户和租户信息。
     * @throws BusinessException 如果参数为空或用户已存在，则抛出业务异常。
     */
    private void verifyParams(UserTenantCreateParam createParam) {

        // 检查创建参数对象是否为空
        if (isEmpty(createParam)) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 检查用户ID是否为空，如果为空则直接返回，避免不必要的检查
        if (isEmpty(createParam.getUserId())) {
            return;
        }

        // 检查用户租户关系是否已存在，防止重复创建
        if (isNotEmpty(getByUserIdAndTenantId(createParam.getUserId(), createParam.getTenantId()))) {
            throw new BusinessException("user already exists");
        }
    }

    /**
     * 根据用户ID和租户ID获取用户租户信息
     * 此方法用于查询特定用户在特定租户中的信息，通过用户ID和租户ID进行精确匹配
     *
     * @param userId   用户ID，用于定位特定用户
     * @param tenantId 租户ID，用于定位特定租户
     * @return 返回匹配的用户租户信息对象，如果没有找到匹配的信息，则返回null
     */
    private UserTenant getByUserIdAndTenantId(Long userId, Long tenantId) {
        // 使用lambda表达式进行查询，确保查询条件为用户ID和租户ID都匹配
        return lambdaQuery().eq(UserTenant::getUserId, userId).eq(UserTenant::getTenantId, tenantId).one();
    }

    /**
     * 根据创建参数构建用户租户对象
     * 此方法主要用于将用户输入的创建参数转换为用户租户对象，并进行初始化配置
     *
     * @param createParam 用户租户创建参数，包含用户租户的相关信息
     * @return 返回初始化后的用户租户对象
     */
    private UserTenant buildUserTenant(UserTenantCreateParam createParam) {

        // 验证创建参数的有效性，确保参数符合预期规范
        verifyParams(createParam);

        // 将创建参数转换为用户租户实体对象
        UserTenant entity = Converters.get(UserTenantConverter.class).toEntity(createParam);

        // 如果用户ID为空，则根据用户租户创建参数插入用户及其相关账户和角色信息
        if (isEmpty(entity.getUserId())) {

            // 根据用户租户创建参数插入用户及其相关账户和角色信息
            entity.setUserId(userService.insertByUserTenant(createParam));
        } else {
            // 二次验证邮箱
            userService.validEmail(entity.getUserId(), createParam.getEmail());
        }

        // 设置用户租户的活跃状态为活跃，确保新创建的用户租户是活跃的
        entity.setActiveFlag(1);

        // 返回初始化后的用户租户对象
        return entity;
    }

    /**
     * 根据更新参数构建并更新用户租户信息。
     * 该方法首先验证输入参数是否为空，并确保用户存在。
     * 然后将更新参数转换为 UserTenant 实体，并更新用户的角色信息。
     *
     * @param updateParam 更新用户租户信息的参数，包含用户的更新信息和角色列表。
     * @return 返回更新后的 UserTenant 实体。
     */
    private UserTenant buildUserTenant(UserTenantUpdateParam updateParam) {
        // 验证输入参数是否为空
        if (isEmpty(updateParam) || isEmpty(updateParam.getId())) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 根据 ID 获取用户信息
        UserTenant user = getById(updateParam.getId());
        // 验证用户信息是否为空
        if (isEmpty(user)) {
            throw new BusinessException("User not found");
        }

        // 获取 UserTenant 转换器
        UserTenantConverter converter = Converters.get(UserTenantConverter.class);

        // 将更新参数转换为 UserTenant 实体
        UserTenant entity = converter.toEntity(updateParam);

        // 批量插入或更新用户的角色信息
        businessRoleService.insertOrUpdateBatch(BusinessRoleType.USER_TENANT, entity.getId(), updateParam.getRoleIdList());

        // 返回更新后的 UserTenant 实体
        return entity;
    }

    /**
     * 构建用户租户页面信息列表
     * <p>
     * 该方法主要用于将用户租户页面信息列表中的每个租户信息与缓存中的租户信息进行匹配和关联
     * 如果列表不为空，则遍历列表中的每个用户租户页面信息对象，从缓存中获取对应的租户信息，
     * 并将其设置为用户租户页面信息对象的所有者合作伙伴信息
     *
     * @param dataList 用户租户页面信息列表，用于展示用户相关的租户信息
     * @return 返回构建后的用户租户页面信息列表如果输入列表为空，则直接返回空列表
     */
    private List<UserTenantPageVO> buildUserTenantPageVO(List<UserTenantPageVO> dataList) {
        // 检查输入列表是否不为空
        if (isNotEmpty(dataList)) {
            // 遍历列表中的每个用户租户页面信息对象
            dataList.forEach(c -> {
                // 将缓存中的租户信息复制到新的TenantPageVO对象，并设置为用户租户页面信息对象的所有者合作伙伴
                c.setOwnerPartner(tenantCacheService.getById(c.getTenantId()));
            });
        }
        // 返回构建后的用户租户页面信息列表
        return dataList;
    }

    /**
     * 根据UserTenant实体构建UserTenantVO对象
     * 此方法主要用于将数据库中的UserTenant对象转换为视图对象（VO），以便于在不同层次之间传递数据
     * 它还负责为用户角色ID列表设置值，这是展示用户角色信息所必需的
     *
     * @param entity UserTenant实体对象，代表用户租户信息
     * @return 返回构建好的UserTenantVO对象，如果输入实体为空，则返回null
     */
    private UserTenantVO buildUserTenantVO(UserTenant entity) {
        // 检查传入的UserTenant实体是否为空，为空则直接返回null
        if (isEmpty(entity)) {
            return null;
        }

        // 使用Converters工具类将UserTenant实体转换为UserTenantVO对象
        UserTenantVO userTenantVO = Converters.get(UserTenantConverter.class).toVO(entity);

        // 获取缓存的租户信息，通过tenantCacheService根据租户ID获取租户信息
        TenantCache tenantCache = tenantCacheService.getById(userTenantVO.getTenantId());

        // 设置用户的角色ID列表，通过businessRoleService根据用户ID获取角色ID列表
        userTenantVO.setRoleIdList(businessRoleService.roleIdsByBusinessIdAndRoleGroups(BusinessRoleType.USER_TENANT, entity.getId(), tenantCache.getPartnerType()));

        // 返回构建好的UserTenantVO对象
        return userTenantVO;
    }

}
