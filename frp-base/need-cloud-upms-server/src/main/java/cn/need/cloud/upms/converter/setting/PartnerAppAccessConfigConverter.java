package cn.need.cloud.upms.converter.setting;


import cn.need.cloud.upms.client.dto.setting.PartnerAppAccessConfigDTO;
import cn.need.cloud.upms.model.entity.setting.PartnerAppAccessConfig;
import cn.need.cloud.upms.model.vo.setting.PartnerAppAccessConfigVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 企业伙伴app权限配置 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
public class PartnerAppAccessConfigConverter extends AbstractModelConverter<PartnerAppAccessConfig, PartnerAppAccessConfigVO, PartnerAppAccessConfigDTO> {

}
