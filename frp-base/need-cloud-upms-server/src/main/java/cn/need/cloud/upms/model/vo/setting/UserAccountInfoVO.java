package cn.need.cloud.upms.model.vo.setting;

import cn.need.cloud.upms.model.vo.tenant.TenantPageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 租户企业对应信息 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "用户账号信息 vo对象")
public class UserAccountInfoVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * 电子邮件
     */
    @Schema(description = "电子邮件")
    private String email;

    /**
     * 名字
     */
    @Schema(description = "名字")
    private String firstName;

    /**
     * 姓氏
     */
    @Schema(description = "姓氏")
    private String lastName;

    /**
     * duoName
     */
    @Schema(description = "duoName")
    private String duoName;

    /**
     * 电话号码
     */
    @Schema(description = "电话号码")
    private String phone;

    /**
     * 激活标志
     */
    @Schema(description = "激活标志")
    private Boolean activeFlag;

    /**
     * 所在企业信息集
     */
    @Schema(description = "所在企业信息集")
    private List<TenantPageVO> tenantList;


}