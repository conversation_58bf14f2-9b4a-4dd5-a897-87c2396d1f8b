package cn.need.cloud.upms.model.entity.setting;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 租户企业对应信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("frp_tenant_partner")
public class TenantPartner extends SuperModel {


    @TableField("tenant_id")
    private Long tenantId;

    @TableField("partner_id")
    private Long partnerId;

}
