package cn.need.cloud.upms.model.vo.setting;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = " vo对象")
public class BasePartnerVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;
    /**
     * 主键id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * 交易伙伴名称
     */
    @Schema(description = "name")
    private String name;

    /**
     * abbrName
     */
    @Schema(description = "abbrName")
    private String abbrName;

    /**
     * 联系人邮箱
     */
    @Schema(description = "联系人邮箱")
    private String contactEmail;

    /**
     * 合作伙伴类型
     */
    @Schema(description = "合作伙伴类型")
    private String partnerType;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Boolean activeFlag;
}
