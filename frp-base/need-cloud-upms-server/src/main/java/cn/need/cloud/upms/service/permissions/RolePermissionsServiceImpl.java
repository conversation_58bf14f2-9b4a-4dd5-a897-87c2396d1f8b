package cn.need.cloud.upms.service.permissions;

import cn.need.cloud.upms.mapper.permissions.RolePermissionsMapper;
import cn.need.cloud.upms.model.entity.permissions.Permissions;
import cn.need.cloud.upms.model.entity.permissions.RolePermissions;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.compress.utils.Sets;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.need.framework.starter.security.constant.SecurityConstants.CACHE_ROLE_PERMISSIONS_PREFIX;

/**
 * 系统角色权限信息 服务实现
 *
 * <AUTHOR>
 */
@Service
public class RolePermissionsServiceImpl extends SuperServiceImpl<RolePermissionsMapper, RolePermissions> implements RolePermissionsService {

    @Resource
    private PermissionsService permissionsService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchSave(Long roleId, String terminal, List<Long> permissionsIds) {
        // 删除指定角色对应的权限关联关系
        removeByRoleIds(Stream.of(roleId).collect(Collectors.toList()), terminal);

        if (ObjectUtil.isNotEmpty(permissionsIds)) {
            //补充父级id
            List<Long> list = permissionsService.listParentId(permissionsIds);

            // 新增指定角色对应的权限关联关系
            insertBatch(
                    list.stream().map(c -> new RolePermissions() {{
                        setRoleId(roleId);
                        setPermissionsId(c);
                        setTerminal(terminal);
                    }}).collect(Collectors.toList()));

            // 更新角色权限缓存
            updateRolePermissionsRedis(roleId);
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByRoleIds(List<Long> roleIds, String terminal) {
        lambdaUpdate().in(RolePermissions::getRoleId, roleIds).eq(RolePermissions::getTerminal, terminal).remove();
        roleIds.forEach(this::updateRolePermissionsRedis);
    }

    @Override
    public List<Long> permissionsListIdIdByRoleId(Long roleId, String terminal) {
        return permissionsListIdIdByRoleIds(Collections.singletonList(roleId), terminal);
    }

    @Override
    public List<Long> permissionsListIdIdByRoleIds(Collection<Long> roleIds, String terminal) {
        return lambdaQuery()
                .in(RolePermissions::getRoleId, roleIds)
                .eq(ObjectUtil.isNotEmpty(terminal), RolePermissions::getTerminal, terminal)
                .list().stream().map(RolePermissions::getPermissionsId).distinct().toList();
    }

    @Override
    public List<RolePermissions> listByRoleId(Long roleId) {
        return lambdaQuery()
                .eq(ObjectUtil.gtZero(roleId), RolePermissions::getRoleId, roleId)
                .list();
    }

    @Override
    public void updateRolePermissionsRedis(Long roleId) {

        // 验证角色ID的有效性
        if (roleId == null || roleId <= 0) {
            throw new IllegalArgumentException("Invalid roleId");
        }

        // 初始化路由路径列表
        Set<String> routePathList = Sets.newHashSet();

        // 根据角色ID获取角色权限列表
        List<RolePermissions> rolePermissionsList = listByRoleId(roleId);
        if (ObjectUtil.isNotEmpty(rolePermissionsList)) {

            // 提取并去重权限ID
            List<Long> permissionsIds = rolePermissionsList.stream()
                    .map(RolePermissions::getPermissionsId)
                    .distinct()
                    .toList();

            // 根据权限ID列表获取权限信息
            List<Permissions> permissionsList = permissionsService.listByIds(permissionsIds);
            if (ObjectUtil.isNotEmpty(permissionsList)) {

                // 过滤并收集路由路径
                permissionsList.stream()
                        .filter(it -> ObjectUtil.isNotEmpty(it) && ObjectUtil.isNotEmpty(it.getRoutePath()))
                        .forEach(it -> routePathList.addAll(it.getRoutePath()));
            }

        }

        // 尝试将路由路径列表存储到Redis中
        try {
            redisTemplate.opsForValue().set(CACHE_ROLE_PERMISSIONS_PREFIX + roleId, routePathList);
        } catch (Exception e) {
            // 记录日志或进行其他异常处理
            log.error("Failed to update role permissions in Redis for roleId: {}", roleId, e);
            throw new RuntimeException("Failed to update role permissions in Redis", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByPermissionsId(Long id) {
        // 根据权限ID获取关联的角色ID列表
        List<Long> roleIds = listRoleIdByPermissionsId(id);

        // 如果角色ID列表不为空，则执行删除操作和缓存更新
        if (ObjectUtil.isNotEmpty(roleIds)) {
            // 删除与指定权限ID关联的所有角色权限信息
            lambdaUpdate().in(RolePermissions::getPermissionsId, id).remove();
            // 遍历角色ID列表，更新每个角色的权限缓存
            roleIds.forEach(this::updateRolePermissionsRedis);
        }
    }


    /**
     * 根据权限ID获取拥有该权限的角色ID列表
     *
     * @param permissionsId 权限ID
     * @return 角色ID列表，如果无角色拥有该权限则返回空列表
     */
    @Override
    public List<Long> listRoleIdByPermissionsId(Long permissionsId) {
        // 查询拥有指定权限的所有角色权限记录
        List<RolePermissions> rolePermissions = lambdaQuery().eq(RolePermissions::getPermissionsId, permissionsId).list();

        // 如果查询结果不为空，则提取角色ID，去重后返回；否则返回空列表
        return ObjectUtil.isNotEmpty(rolePermissions) ?
                rolePermissions.stream().map(RolePermissions::getRoleId).distinct().toList() : Lists.newArrayList();
    }


}
