package cn.need.cloud.upms.client.dto.req;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 租户信息请求参数
 *
 * <AUTHOR>
 */
@Data
public class TenantReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户id数组
     */
    private List<Long> idList;

    /**
     * 关联站点查询条件：省
     */
    private String province;

    /**
     * 关联站点查询条件：省编码
     */
    private String provinceCode;

    /**
     * 关联站点查询条件：市
     */
    private String city;

    /**
     * 关联站点查询条件：市编码
     */
    private String cityCode;
}
