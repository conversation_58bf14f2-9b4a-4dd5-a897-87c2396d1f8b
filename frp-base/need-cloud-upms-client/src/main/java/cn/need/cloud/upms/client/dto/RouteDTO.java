package cn.need.cloud.upms.client.dto;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 接口权限路由 dto对象
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RouteDTO extends SuperDTO {


    /**
     * 所属服务
     */
    private String serverName;

    /**
     * 路径
     */
    private Long path;

    /**
     * 备注
     */
    private String remark;

}