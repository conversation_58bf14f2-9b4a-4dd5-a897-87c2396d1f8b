package cn.need.cloud.log.cache;

import cn.need.cloud.log.cache.bean.LoggerPatternCache;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
public class LoggerPatternRedisTemplate {

    public static final String REDIS_LOGGER_KEY = "LOGGER:CACHE";
    private final BoundHashOperations<String, String, LoggerPatternCache> operations;

    public LoggerPatternRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.operations = hash(redisTemplate);
    }

    public BoundHashOperations<String, String, LoggerPatternCache> hash() {
        return operations;
    }

    public BoundHashOperations<String, String, LoggerPatternCache> hash(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.boundHashOps(REDIS_LOGGER_KEY);
    }
}
