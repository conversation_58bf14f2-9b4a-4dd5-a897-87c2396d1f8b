package cn.need.framework.common.generator.custom;

import cn.need.framework.common.generator.BaseGenerator;

/**
 * CoracleMarketingGenerator Tester.
 *
 * <AUTHOR>
 * @since 04/19/2018
 */
public class
UneedMarketingGeneratorTest {

    /**
     * 测试自动生成代码
     */
    public static void main(String[] args) {
        BaseGenerator generator = new NeedCloudGenerator(getLocalConfigPath());
        generator.generate();
    }

    /**
     * 获取本地配置文件路径
     */
    private static String getLocalConfigPath() {
        if (System.getProperty("os.name").startsWith("Windows")) {
            return "D:\\frp-api\\need-common\\need-common-generator\\src\\main\\resources\\config\\generator.properties";
        }
        return "/Users/<USER>/uneed/gitlab/frp-api/need-common/need-common-generator/src/main/resources/config/generator.properties";
    }

} 
