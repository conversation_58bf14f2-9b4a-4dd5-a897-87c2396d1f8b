package cn.need.framework.common.support.entity;

import cn.need.framework.common.annotation.dict.AreaField;
import cn.need.framework.common.annotation.enums.AreaProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * demo实体类
 *
 * <AUTHOR>
 */
@Data
public class AreaEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 9029334805579829026L;

    /**
     * 国家
     */
    private String country;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区/县
     */
    private String district;

    /**
     * 街道/乡镇
     */
    private String street;

    /**
     * 社区/村
     */
    private String town;


    /**
     * 国家
     */
    @AreaField
    private String countryName;

    /**
     * 省
     */
    @AreaField
    private String provinceName;

    /**
     * 市
     */
    @AreaField
    private String cityName;

    /**
     * 区
     */
    @AreaField
    private String districtName;

    /**
     * 街道
     */
    @AreaField
    private String streetName;

    /**
     * 乡
     */
    @AreaField
    private String townName;

    /**
     * 组合名称
     */
    @AreaField(field = "city", space = "-", property = AreaProperty.FULL_NAME)
    private String fullName;
}
