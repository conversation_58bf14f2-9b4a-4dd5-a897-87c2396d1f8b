package cn.need.framework.common.support.util;

import cn.need.framework.common.annotation.enums.DictProperty;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.date.TimeMeter;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.map.Maps;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.dict.api.DictRepertory;
import cn.need.framework.common.dict.constant.DictConstant;
import cn.need.framework.common.dict.entity.Dict;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.entity.DictDemo;
import cn.need.framework.common.support.entity.DictEntity;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.need.framework.common.core.lang.ObjectUtil.nullToDefault;

/**
 * need to describe something here.
 *
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = {"classpath:spring-redis.xml"})
@Slf4j
public class DictUtilTest {

    @Autowired
    private DictRepertory repertory;

    /**
     * 初始化数据字典
     */
    @Test
    @Order(0)
    public void init() {
        TimeMeter meter = new TimeMeter();
        List<Dict> list = buildDictData();
        log.warn(" >>>>>>>> test build data finished. time=[{}]ms", meter.sign("one"));
        repertory.initialization(list);
        log.warn(" >>>>>>>> test initialization finished. time=[{}]ms", meter.sign("one", "two"));
    }


    @Test
    @Order(1)
    public void getNameTest() {
        Assertions.assertEquals("服务类型", DictUtil.getName("SERVER_TYPE"));
        Assertions.assertEquals("单服务", DictUtil.getName("ONE"));
        Assertions.assertEquals("3级字典-one", DictUtil.getName("THIRD_ONE"));

        Assertions.assertEquals("有效性", DictUtil.getName("ACTIVE", "ACTIVE"));
        Assertions.assertEquals("有效", DictUtil.getName("ENABLE", "ACTIVE"));
        Assertions.assertEquals("3级字典-one", DictUtil.getName("THIRD_ONE", "ACTIVE"));
    }

    @Test
    @Order(2)
    public void getValueTest() {
        Assertions.assertEquals("ServerType", DictUtil.getValue("SERVER_TYPE"));
        Assertions.assertEquals("ONE", DictUtil.getValue("ONE"));
        Assertions.assertEquals("3-1", DictUtil.getValue("THIRD_ONE"));

        Assertions.assertEquals("Active", DictUtil.getValue("ACTIVE", "ACTIVE"));
        Assertions.assertEquals("ENABLE", DictUtil.getValue("ENABLE", "ACTIVE"));
        Assertions.assertEquals("3-1", DictUtil.getValue("THIRD_ONE", "ACTIVE"));
    }

    @Test
    @Order(3)
    public void getPropertyValueTest() {
        Assertions.assertEquals("SERVER_TYPE", DictUtil.getPropertyValue("SERVER_TYPE", DictProperty.CODE));
        Assertions.assertEquals("服务类型", DictUtil.getPropertyValue("SERVER_TYPE", DictProperty.NAME));
        Assertions.assertEquals("ServerType", DictUtil.getPropertyValue("SERVER_TYPE", DictProperty.VALUE));
        Assertions.assertEquals("服务类型常量", DictUtil.getPropertyValue("SERVER_TYPE", DictProperty.DESCRIPTION));
        Assertions.assertTrue(ObjectUtil.equals(5, DictUtil.getPropertyValue("SERVER_TYPE", DictProperty.SORTING)));
        Assertions.assertTrue(ObjectUtil.equals(Boolean.TRUE, DictUtil.getPropertyValue("SERVER_TYPE", DictProperty.ENABLE)));
        Assertions.assertEquals(StringPool.EMPTY, DictUtil.getPropertyValue("SERVER_TYPE", DictProperty.PARENT));
        Assertions.assertEquals(DictConstant.DEFAULT_ROOT, DictUtil.getPropertyValue("SERVER_TYPE", DictProperty.ROOT));
        List<String> subset = nullToDefault(DictUtil.getPropertyValue("SERVER_TYPE", DictProperty.SUBSET), Lists.arrayList());
        Assertions.assertEquals(2, subset.size());


        Assertions.assertEquals("ENABLE", DictUtil.getPropertyValue("ENABLE", "ACTIVE", DictProperty.CODE));
        Assertions.assertEquals("有效", DictUtil.getPropertyValue("ENABLE", "ACTIVE", DictProperty.NAME));
        Assertions.assertEquals("ENABLE", DictUtil.getPropertyValue("ENABLE", "ACTIVE", DictProperty.VALUE));
        Assertions.assertEquals("有效", DictUtil.getPropertyValue("ENABLE", "ACTIVE", DictProperty.DESCRIPTION));
        Assertions.assertTrue(ObjectUtil.equals(1, DictUtil.getPropertyValue("ENABLE", "ACTIVE", DictProperty.SORTING)));
        Assertions.assertTrue(ObjectUtil.equals(Boolean.TRUE, DictUtil.getPropertyValue("ENABLE", "ACTIVE", DictProperty.ENABLE)));
        Assertions.assertEquals("ACTIVE", DictUtil.getPropertyValue("ENABLE", "ACTIVE", DictProperty.PARENT));
        Assertions.assertEquals("ACTIVE", DictUtil.getPropertyValue("ENABLE", "ACTIVE", DictProperty.ROOT));
        List<String> subset2 = nullToDefault(DictUtil.getPropertyValue("ENABLE", "ACTIVE", DictProperty.SUBSET), Lists.arrayList());
        Assertions.assertEquals(4, subset2.size());

        Assertions.assertTrue(ObjectUtil.equals(Boolean.FALSE, DictUtil.getPropertyValue("SAT", "SERVICE_DAY", DictProperty.ENABLE)));
    }

    @Test
    @Order(4)
    public void listTest() {
        Assertions.assertEquals(0, DictUtil.list("SERVER_TYPE").size());
        Assertions.assertEquals(7, DictUtil.list("").size());
        Assertions.assertEquals(3, DictUtil.list("DICT_TYPE").size());

        Assertions.assertEquals(4, DictUtil.list(Lists.arrayList("SERVER_TYPE", "ONE", "THIRD_ONE", "THIRD_TWO")).size());
        Assertions.assertEquals(2, DictUtil.list(Lists.arrayList("SERVER_TYPE", "ONE", "THIRD_ONE", "THIRD_TWO"), "TABLE_GENERATE_STATUS").size());
    }

    @Test
    @Order(5)
    public void subListTest() {
        Assertions.assertEquals(2, DictUtil.subList("SERVER_TYPE").size());
        Assertions.assertEquals(1, DictUtil.subList("SERVER_TYPE", true).size());
        Assertions.assertEquals(2, DictUtil.subList("SERVER_TYPE", false).size());
        Assertions.assertEquals(4, DictUtil.subList("ONE").size());
        Assertions.assertEquals(0, DictUtil.subList("SERVER_TYPE", "SERVER_TYPE").size());

        Assertions.assertEquals(0, DictUtil.subList("ACTIVE").size());
        Assertions.assertEquals(2, DictUtil.subList("ACTIVE", "ACTIVE").size());
        Assertions.assertEquals(4, DictUtil.subList("ENABLE", "ACTIVE").size());

        Assertions.assertEquals(7, DictUtil.subList("SERVICE_DAY", "SERVICE_DAY").size());
        Assertions.assertEquals(5, DictUtil.subList("SERVICE_DAY", "SERVICE_DAY", true).size());
        Assertions.assertEquals(7, DictUtil.subList("SERVICE_DAY", "SERVICE_DAY", false).size());
    }

    @Test
    @Order(6)
    public void setTest() {
        TimeMeter meter = new TimeMeter();
        DictDemo dictDemo = new DictDemo();
        dictDemo.setServerType("COMPOSE");
        dictDemo.setActive("ENABLE");
        dictDemo.setServiceDay("SERVICE_DAY");
        dictDemo.setServiceDayName("SERVICE_DAY");
        dictDemo.setThirdOne("THIRD_ONE");
        log.warn(" >>>>>>>> test set before. data={}", JsonUtil.toJson(dictDemo));
        DictUtil.set(dictDemo);
        log.warn(" <<<<<<<< test set after. data={}", JsonUtil.toJson(dictDemo));
        log.warn(" >>>>>>>> test set finished. time=[{}]ms", meter.sign());

        Assertions.assertEquals("组合服务", dictDemo.getServerTypeName());
        Assertions.assertEquals("组合服务", dictDemo.getServerTypeDescription());
        Assertions.assertEquals("ENABLE", dictDemo.getActiveCode());
        Assertions.assertEquals("有效", dictDemo.getActiveName());
        Assertions.assertEquals("有效", dictDemo.getActiveName2());
        Assertions.assertEquals("ACTIVE", dictDemo.getActiveRoot());
        Assertions.assertTrue(ObjectUtil.equals(Boolean.TRUE, dictDemo.getActiveEnabled()));
        Assertions.assertEquals(1, dictDemo.getActiveSorting());
        Assertions.assertEquals(4, dictDemo.getActiveEnableSubset().size());
        Assertions.assertEquals("服务日", dictDemo.getServiceDayName());
        Assertions.assertTrue(ObjectUtil.equals(Boolean.TRUE, dictDemo.getServiceDayEnable()));
        Assertions.assertEquals("周一", dictDemo.getMonday());
        Assertions.assertEquals("3级字典-one", dictDemo.getThirdOneName());
        Assertions.assertEquals("3-1", dictDemo.getThirdOneValue());
        Assertions.assertEquals("NOT_GENERATED", dictDemo.getThirdOneParent());
    }

    @Test
    @Order(7)
    public void setTest2() {
        TimeMeter meter = new TimeMeter();
        List<DictDemo> demoList = Lists.arrayList();
        DictDemo dictDemo = new DictDemo();
        dictDemo.setServerType("SERVER_TYPE");
        dictDemo.setActive("ENABLE");
        dictDemo.setServiceDay("SERVICE_DAY");
        dictDemo.setServiceDayName("SERVICE_DAY");
        dictDemo.setThirdOne("THIRD_ONE");
        demoList.add(dictDemo);
        DictDemo dictDemo2 = new DictDemo();
        dictDemo2.setServerType("ONE");
        dictDemo2.setActive("ENABLE");
        dictDemo2.setServiceDay("FRI");
        dictDemo2.setServiceDayName("FRI");
        dictDemo2.setThirdOne("THIRD_TWO");
        demoList.add(dictDemo2);
        DictDemo dictDemo3 = new DictDemo();
        dictDemo3.setServerType("COMPOSE");
        dictDemo3.setActive("DISABLE");
        dictDemo3.setServiceDay("SAT");
        dictDemo3.setServiceDayName("SAT");
        dictDemo3.setThirdOne("THIRD_FOUR");
        demoList.add(dictDemo3);
        log.warn(" >>>>>>>> test set before. data={}", JsonUtil.toJson(demoList));
        DictUtil.set(demoList);
        log.warn(" <<<<<<<< test set after. data={}", JsonUtil.toJson(demoList));
        log.warn(" >>>>>>>> test set finished. time=[{}]ms", meter.sign());

        Assertions.assertEquals("服务类型", dictDemo.getServerTypeName());
        Assertions.assertEquals("服务类型常量", dictDemo.getServerTypeDescription());
        Assertions.assertEquals("ENABLE", dictDemo.getActiveCode());
        Assertions.assertEquals("有效", dictDemo.getActiveName());
        Assertions.assertEquals("有效", dictDemo.getActiveName2());
        Assertions.assertEquals("ACTIVE", dictDemo.getActiveRoot());
        Assertions.assertTrue(ObjectUtil.equals(Boolean.TRUE, dictDemo.getActiveEnabled()));
        Assertions.assertEquals(1, dictDemo.getActiveSorting());
        Assertions.assertEquals(4, dictDemo.getActiveEnableSubset().size());
        Assertions.assertEquals("服务日", dictDemo.getServiceDayName());
        Assertions.assertTrue(ObjectUtil.equals(Boolean.TRUE, dictDemo.getServiceDayEnable()));
        Assertions.assertEquals("周一", dictDemo.getMonday());
        Assertions.assertEquals("3级字典-one", dictDemo.getThirdOneName());
        Assertions.assertEquals("3-1", dictDemo.getThirdOneValue());
        Assertions.assertEquals("NOT_GENERATED", dictDemo.getThirdOneParent());

        Assertions.assertEquals("单服务", dictDemo2.getServerTypeName());
        Assertions.assertEquals("单服务", dictDemo2.getServerTypeDescription());
        Assertions.assertEquals("ENABLE", dictDemo2.getActiveCode());
        Assertions.assertEquals("有效", dictDemo2.getActiveName());
        Assertions.assertEquals("有效", dictDemo2.getActiveName2());
        Assertions.assertEquals("ACTIVE", dictDemo2.getActiveRoot());
        Assertions.assertTrue(ObjectUtil.equals(Boolean.TRUE, dictDemo2.getActiveEnabled()));
        Assertions.assertEquals(1, dictDemo2.getActiveSorting());
        Assertions.assertEquals(4, dictDemo2.getActiveEnableSubset().size());
        Assertions.assertEquals("周五", dictDemo2.getServiceDayName());
        Assertions.assertTrue(ObjectUtil.equals(Boolean.TRUE, dictDemo2.getServiceDayEnable()));
        Assertions.assertEquals("周一", dictDemo2.getMonday());
        Assertions.assertEquals("3级字典-two", dictDemo2.getThirdOneName());
        Assertions.assertEquals("3-2", dictDemo2.getThirdOneValue());
        Assertions.assertEquals("NOT_GENERATED", dictDemo2.getThirdOneParent());

        Assertions.assertEquals("组合服务", dictDemo3.getServerTypeName());
        Assertions.assertEquals("组合服务", dictDemo3.getServerTypeDescription());
        Assertions.assertEquals("DISABLE", dictDemo3.getActiveCode());
        Assertions.assertEquals("失效", dictDemo3.getActiveName());
        Assertions.assertEquals("失效", dictDemo3.getActiveName2());
        Assertions.assertEquals("ACTIVE", dictDemo3.getActiveRoot());
        Assertions.assertTrue(ObjectUtil.equals(Boolean.TRUE, dictDemo3.getActiveEnabled()));
        Assertions.assertEquals(2, dictDemo3.getActiveSorting());
        Assertions.assertEquals(0, dictDemo3.getActiveEnableSubset().size());
        Assertions.assertEquals("周六", dictDemo3.getServiceDayName());
        Assertions.assertTrue(ObjectUtil.equals(Boolean.FALSE, dictDemo3.getServiceDayEnable()));
        Assertions.assertEquals("周一", dictDemo3.getMonday());
        Assertions.assertEquals("3级字典-four", dictDemo3.getThirdOneName());
        Assertions.assertEquals("3-4", dictDemo3.getThirdOneValue());
        Assertions.assertEquals("NOT_GENERATED", dictDemo3.getThirdOneParent());
    }

    @Test
    @Order(8)
    public void setTest3() {
        TimeMeter meter = new TimeMeter();
        DictEntity dictEntity = new DictEntity();
        dictEntity.setDictType("CUSTOM");
        dictEntity.setServiceDay("SUN");
        DictUtil.set(dictEntity);
        log.warn(" >>>>>>>> test set finished. time=[{}]ms", meter.sign("one"));

        DictEntity dictEntity2 = new DictEntity();
        dictEntity2.setDictType("SYSTEM");
        dictEntity2.setServiceDay("MON");
        DictUtil.set(dictEntity2);
        log.warn(" >>>>>>>> test set finished. time=[{}]ms", meter.sign("one", "two"));

        Assertions.assertEquals("自定义", dictEntity.getDictTypeName());
        Assertions.assertEquals("周日", dictEntity.getServiceDayName());
        Assertions.assertEquals("系统", dictEntity2.getDictTypeName());
        Assertions.assertEquals("周一", dictEntity2.getServiceDayName());
    }

    @Test
    @Order(9)
    public void setTest4() {
        TimeMeter meter = new TimeMeter();
        DictEntity dictEntity = new DictEntity();
        dictEntity.setDictType("CUSTOM");
        dictEntity.setServiceDay("SUN");
        DictUtil.set(dictEntity);
        log.warn(" >>>>>>>> test set finished. time=[{}]ms", meter.sign("one"));

        List<DictEntity> entityList = buildDictEntity(10);
        log.warn(" >>>>>>>> test set finished. time=[{}]ms", meter.sign("one", "two"));
        DictUtil.set(entityList);
        log.warn(" >>>>>>>> test set finished. time=[{}]ms", meter.sign("two", "thr"));

        Assertions.assertEquals("自定义", dictEntity.getDictTypeName());
        Assertions.assertEquals("周日", dictEntity.getServiceDayName());
        Assertions.assertEquals("系统", entityList.get(4).getDictTypeName());
        Assertions.assertEquals("周五", entityList.get(4).getServiceDayName());
    }

    @Test
    @Order(10)
    public void setTest5() {
        TimeMeter meter = new TimeMeter();
        DictEntity dictEntity = new DictEntity();
        dictEntity.setDictType("CUSTOM");
        dictEntity.setServiceDay("SUN");
        DictUtil.set(dictEntity);
        log.warn(" >>>>>>>> test set finished. time=[{}]ms", meter.sign("one"));

        List<DictEntity> entityList = buildDictEntity(1000);
        log.warn(" >>>>>>>> test set finished. time=[{}]ms", meter.sign("one", "two"));
        DictUtil.set(entityList);
        log.warn(" >>>>>>>> test set finished. time=[{}]ms", meter.sign("two", "thr"));

        Assertions.assertEquals("自定义", dictEntity.getDictTypeName());
        Assertions.assertEquals("周日", dictEntity.getServiceDayName());
        Assertions.assertEquals("系统", entityList.get(4).getDictTypeName());
        Assertions.assertEquals("周五", entityList.get(4).getServiceDayName());
    }

    /**
     * 清空数据字典
     */
    @Test
    @Order(11)
    public void hashTest() {
        Set<String> set = nullToDefault(DictUtil.getHash("").keys(), Lists.hashSet());
        Assertions.assertEquals(7, set.size());

        Set<String> set2 = nullToDefault(DictUtil.getHash("TABLE_GENERATE_STATUS").keys(), Lists.hashSet());
        Assertions.assertEquals(10, set2.size());
    }

    /**
     * 测试拷贝
     */
    @Test
    @Order(12)
    public void copy() {
        JSONObject json = new JSONObject();
        json.put("a", 123);
        json.put("b", 456);
        Map<String, Object> map = Maps.hashMap();
        map.put("k", "kk");
        map.put("v", 11);
        JSONArray array = new JSONArray();
        array.add(map);
        json.put("array", array);
        System.out.println("json = " + json);
        Result<JSONObject> result = new Result<>(true, 0, "成功", json);
        System.out.println("result = " + result);
        System.out.println();
        Result<JSONObject> result2 = new Result<>();
        BeanUtil.copy(result, result2);
        System.out.println("result = " + result2);
        System.out.println("json = " + result2.getData());

        map.put("kv", "kv");
        json.getJSONArray("array").getJSONObject(0).putAll(map);
        System.out.println();
        System.out.println("json = " + json);


    }

    /**
     * 清空数据字典
     */
    @Test
    @Order(99)
    public void clear() {
        TimeMeter meter = new TimeMeter();
        repertory.clear();
        log.warn(" >>>>>>>> test clear finished. time=[{}]ms", meter.sign());
    }

    private List<DictEntity> buildDictEntity(int size) {
        List<DictEntity> list = Lists.arrayList();
        int flag = 1;
        for (int i = 0; i < size; i++) {
            if (flag > 7) {
                flag = 1;
            }
            DictEntity entity = new DictEntity();
            switch (flag) {
                case 1:
                    entity.setDictType("SYSTEM");
                    entity.setServiceDay("MON");
                    break;
                case 2:
                    entity.setDictType("CUSTOM");
                    entity.setServiceDay("TUE");
                    break;
                case 3:
                    entity.setDictType("SYSTEM");
                    entity.setServiceDay("WED");
                    break;
                case 4:
                    entity.setDictType("CUSTOM");
                    entity.setServiceDay("THUR");
                    break;
                case 5:
                    entity.setDictType("SYSTEM");
                    entity.setServiceDay("FRI");
                    break;
                case 6:
                    entity.setDictType("CUSTOM");
                    entity.setServiceDay("SAT");
                    break;
                case 7:
                    entity.setDictType("SYSTEM");
                    entity.setServiceDay("SUN");
                    break;
                default:
            }
            list.add(entity);
            flag++;
        }
        return list;
    }

    private List<Dict> buildDictData() {
        List<Dict> list = Lists.arrayList();
        //增加根级字典
        Dict dict = new Dict();
        dict.setCode("ACTIVE");
        dict.setName("有效性");
        dict.setValue("Active");
        dict.setEnable(true);
        dict.setDescription("有效性常量");
        dict.setSorting(1);
        dict.setRoot("ACTIVE");
        list.add(dict);

        Dict dict2 = new Dict();
        dict2.setCode("DICT_TYPE");
        dict2.setName("字典类型");
        dict2.setValue("DictType");
        dict2.setEnable(true);
        dict2.setDescription("字典类型常量");
        dict2.setSorting(2);
        dict2.setRoot("DICT_TYPE");
        list.add(dict2);

        Dict dict3 = new Dict();
        dict3.setCode("SERVICE_DAY");
        dict3.setName("服务日");
        dict3.setValue("ServiceDay");
        dict3.setEnable(true);
        dict3.setDescription("服务日常量");
        dict3.setSorting(3);
        dict3.setRoot("SERVICE_DAY");
        list.add(dict3);

        Dict dict4 = new Dict();
        dict4.setCode("TABLE_GENERATE_STATUS");
        dict4.setName("排班生成状态");
        dict4.setValue("TableGenerateStatus");
        dict4.setEnable(true);
        dict4.setDescription("排班生成状态常量");
        dict4.setSorting(4);
        dict4.setRoot("TABLE_GENERATE_STATUS");
        list.add(dict4);

        Dict dict5 = new Dict();
        dict5.setCode("SERVER_TYPE");
        dict5.setName("服务类型");
        dict5.setValue("ServerType");
        dict5.setEnable(true);
        dict5.setDescription("服务类型常量");
        dict5.setSorting(5);
        list.add(dict5);

        buildDictSubsetData(list);
        return list;
    }

    /**
     * 子集字典
     */
    private void buildDictSubsetData(List<Dict> list) {
        Dict active = new Dict();
        active.setCode("ENABLE");
        active.setName("有效");
        active.setValue("ENABLE");
        active.setEnable(true);
        active.setDescription("有效");
        active.setSorting(1);
        active.setParent("ACTIVE");
        active.setRoot("ACTIVE");
        list.add(active);
        buildDictSubsetData(list, active.getCode(), active.getRoot());
        Dict active2 = new Dict();
        active2.setCode("DISABLE");
        active2.setName("失效");
        active2.setValue("DISABLE");
        active2.setEnable(true);
        active2.setDescription("失效");
        active2.setSorting(2);
        active2.setParent("ACTIVE");
        active2.setRoot("ACTIVE");
        list.add(active2);


        Dict dictType = new Dict();
        dictType.setCode("SYSTEM");
        dictType.setName("系统");
        dictType.setValue("SYSTEM");
        dictType.setEnable(true);
        dictType.setDescription("系统");
        dictType.setSorting(1);
        dictType.setParent("DICT_TYPE");
        dictType.setRoot("DICT_TYPE");
        list.add(dictType);
        Dict dictType2 = new Dict();
        dictType2.setCode("CUSTOM");
        dictType2.setName("自定义");
        dictType2.setValue("CUSTOM");
        dictType2.setEnable(true);
        dictType2.setDescription("自定义");
        dictType2.setSorting(2);
        dictType2.setParent("DICT_TYPE");
        dictType2.setRoot("DICT_TYPE");
        list.add(dictType2);


        Dict serviceDay = new Dict();
        serviceDay.setCode("MON");
        serviceDay.setName("周一");
        serviceDay.setValue("MON");
        serviceDay.setEnable(true);
        serviceDay.setDescription("周一");
        serviceDay.setSorting(1);
        serviceDay.setParent("SERVICE_DAY");
        serviceDay.setRoot("SERVICE_DAY");
        list.add(serviceDay);
        Dict serviceDay2 = new Dict();
        serviceDay2.setCode("TUE");
        serviceDay2.setName("周二");
        serviceDay2.setValue("TUE");
        serviceDay2.setEnable(true);
        serviceDay2.setDescription("周二");
        serviceDay2.setSorting(2);
        serviceDay2.setParent("SERVICE_DAY");
        serviceDay2.setRoot("SERVICE_DAY");
        list.add(serviceDay2);
        Dict serviceDay3 = new Dict();
        serviceDay3.setCode("WED");
        serviceDay3.setName("周三");
        serviceDay3.setValue("WED");
        serviceDay3.setEnable(true);
        serviceDay3.setDescription("周三");
        serviceDay3.setSorting(3);
        serviceDay3.setParent("SERVICE_DAY");
        serviceDay3.setRoot("SERVICE_DAY");
        list.add(serviceDay3);
        Dict serviceDay4 = new Dict();
        serviceDay4.setCode("THUR");
        serviceDay4.setName("周四");
        serviceDay4.setValue("THUR");
        serviceDay4.setEnable(true);
        serviceDay4.setDescription("周四");
        serviceDay4.setSorting(4);
        serviceDay4.setParent("SERVICE_DAY");
        serviceDay4.setRoot("SERVICE_DAY");
        list.add(serviceDay4);
        Dict serviceDay5 = new Dict();
        serviceDay5.setCode("FRI");
        serviceDay5.setName("周五");
        serviceDay5.setValue("FRI");
        serviceDay5.setEnable(true);
        serviceDay5.setDescription("周五");
        serviceDay5.setSorting(5);
        serviceDay5.setParent("SERVICE_DAY");
        serviceDay5.setRoot("SERVICE_DAY");
        list.add(serviceDay5);
        Dict serviceDay6 = new Dict();
        serviceDay6.setCode("SAT");
        serviceDay6.setName("周六");
        serviceDay6.setValue("SAT");
        serviceDay6.setEnable(false);
        serviceDay6.setDescription("周六");
        serviceDay6.setSorting(6);
        serviceDay6.setParent("SERVICE_DAY");
        serviceDay6.setRoot("SERVICE_DAY");
        list.add(serviceDay6);
        Dict serviceDay7 = new Dict();
        serviceDay7.setCode("SUN");
        serviceDay7.setName("周日");
        serviceDay7.setValue("SUN");
        serviceDay7.setEnable(false);
        serviceDay7.setDescription("周日");
        serviceDay7.setSorting(7);
        serviceDay7.setParent("SERVICE_DAY");
        serviceDay7.setRoot("SERVICE_DAY");
        list.add(serviceDay7);

        Dict tableGenerateStatus = new Dict();
        tableGenerateStatus.setCode("NOT_GENERATED");
        tableGenerateStatus.setName("未生成");
        tableGenerateStatus.setValue("NOT_GENERATED");
        tableGenerateStatus.setEnable(true);
        tableGenerateStatus.setDescription("未生成");
        tableGenerateStatus.setSorting(1);
        tableGenerateStatus.setParent("TABLE_GENERATE_STATUS");
        tableGenerateStatus.setRoot("TABLE_GENERATE_STATUS");
        list.add(tableGenerateStatus);
        buildDictSubsetData(list, tableGenerateStatus.getCode(), tableGenerateStatus.getRoot());
        Dict tableGenerateStatus2 = new Dict();
        tableGenerateStatus2.setCode("GENERATED");
        tableGenerateStatus2.setName("已生成");
        tableGenerateStatus2.setValue("GENERATED");
        tableGenerateStatus2.setEnable(true);
        tableGenerateStatus2.setDescription("已生成");
        tableGenerateStatus2.setSorting(2);
        tableGenerateStatus2.setParent("TABLE_GENERATE_STATUS");
        tableGenerateStatus2.setRoot("TABLE_GENERATE_STATUS");
        list.add(tableGenerateStatus2);
        Dict tableGenerateStatus3 = new Dict();
        tableGenerateStatus3.setCode("NOT_UPDATED");
        tableGenerateStatus3.setName("未更新");
        tableGenerateStatus3.setValue("NOT_UPDATED");
        tableGenerateStatus3.setEnable(true);
        tableGenerateStatus3.setDescription("未更新");
        tableGenerateStatus3.setSorting(3);
        tableGenerateStatus3.setParent("TABLE_GENERATE_STATUS");
        tableGenerateStatus3.setRoot("TABLE_GENERATE_STATUS");
        list.add(tableGenerateStatus3);
        Dict tableGenerateStatus4 = new Dict();
        tableGenerateStatus4.setCode("UPDATED");
        tableGenerateStatus4.setName("已更新");
        tableGenerateStatus4.setValue("UPDATED");
        tableGenerateStatus4.setEnable(true);
        tableGenerateStatus4.setDescription("已更新");
        tableGenerateStatus4.setSorting(4);
        tableGenerateStatus4.setParent("TABLE_GENERATE_STATUS");
        tableGenerateStatus4.setRoot("TABLE_GENERATE_STATUS");
        list.add(tableGenerateStatus4);
        Dict tableGenerateStatus5 = new Dict();
        tableGenerateStatus5.setCode("UNKNOWN");
        tableGenerateStatus5.setName("未知的");
        tableGenerateStatus5.setValue("UNKNOWN");
        tableGenerateStatus5.setEnable(false);
        tableGenerateStatus5.setDescription("未知的");
        tableGenerateStatus5.setSorting(5);
        tableGenerateStatus5.setParent("TABLE_GENERATE_STATUS");
        tableGenerateStatus5.setRoot("TABLE_GENERATE_STATUS");
        list.add(tableGenerateStatus5);

        Dict serverType = new Dict();
        serverType.setCode("ONE");
        serverType.setName("单服务");
        serverType.setValue("ONE");
        serverType.setEnable(true);
        serverType.setDescription("单服务");
        serverType.setSorting(1);
        serverType.setParent("SERVER_TYPE");
        list.add(serverType);
        buildDictSubsetData(list, serverType.getCode(), serverType.getRoot());
        Dict serverType2 = new Dict();
        serverType2.setCode("COMPOSE");
        serverType2.setName("组合服务");
        serverType2.setValue("COMPOSE");
        serverType2.setEnable(false);
        serverType2.setDescription("组合服务");
        serverType2.setSorting(2);
        serverType2.setParent("SERVER_TYPE");
        list.add(serverType2);
    }

    public void buildDictSubsetData(List<Dict> list, String parent, String root) {
        Dict dict = new Dict();
        dict.setCode("THIRD_ONE");
        dict.setName("3级字典-one");
        dict.setValue("3-1");
        dict.setEnable(true);
        dict.setDescription("3级字典-one");
        dict.setSorting(1);
        dict.setParent(parent);
        dict.setRoot(root);
        list.add(dict);

        Dict dict2 = new Dict();
        dict2.setCode("THIRD_TWO");
        dict2.setName("3级字典-two");
        dict2.setValue("3-2");
        dict2.setEnable(true);
        dict2.setDescription("3级字典-two");
        dict2.setSorting(2);
        dict2.setParent(parent);
        dict2.setRoot(root);
        list.add(dict2);

        Dict dict3 = new Dict();
        dict3.setCode("THIRD_THREE");
        dict3.setName("3级字典-three");
        dict3.setValue("3-3");
        dict3.setEnable(true);
        dict3.setDescription("3级字典-three");
        dict3.setSorting(3);
        dict3.setParent(parent);
        dict3.setRoot(root);
        list.add(dict3);

        Dict dict4 = new Dict();
        dict4.setCode("THIRD_FOUR");
        dict4.setName("3级字典-four");
        dict4.setValue("3-4");
        dict4.setEnable(true);
        dict4.setDescription("3级字典-four");
        dict4.setSorting(4);
        dict4.setParent(parent);
        dict4.setRoot(root);
        list.add(dict4);
    }

}
