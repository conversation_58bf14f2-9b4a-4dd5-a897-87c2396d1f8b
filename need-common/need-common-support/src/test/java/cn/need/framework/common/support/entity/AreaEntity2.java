package cn.need.framework.common.support.entity;

import cn.need.framework.common.annotation.dict.AreaField;
import cn.need.framework.common.annotation.enums.AreaDepth;
import cn.need.framework.common.annotation.enums.AreaProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * demo实体类
 *
 * <AUTHOR>
 */
@Data
public class AreaEntity2 implements Serializable {

    @Serial
    private static final long serialVersionUID = 9029334805579829026L;

    /**
     * 国家
     */
    private String country;

    /**
     * 省
     */
    private String province;

    /**
     * 市区
     */
    private String city;

    /**
     * 街道
     */
    private String street;

    /**
     * 乡
     */
    private String town;


    /**
     * 国家
     */
    @AreaField(depth = AreaDepth.ROOT)
    private String countryName;

    /**
     * 省
     */
    @AreaField(depth = AreaDepth.FIRST)
    private String provinceName;

    /**
     * 市区
     */
    @AreaField(depth = AreaDepth.SECOND)
    private String cityName;

    /**
     * 街道
     */
    @AreaField(depth = AreaDepth.THIRD)
    private String streetName;

    /**
     * 乡
     */
    @AreaField(depth = AreaDepth.FIFTH)
    private String townName;

    /**
     * 组合名称
     */
    @AreaField(field = "street", depth = AreaDepth.THIRD, space = "-", property = AreaProperty.FULL_NAME)
    private String fullName;
}
