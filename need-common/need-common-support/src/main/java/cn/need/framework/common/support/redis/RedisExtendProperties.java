package cn.need.framework.common.support.redis;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;


/**
 * Minio参数配置类
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "spring.redis.custom")
public class RedisExtendProperties {


    /**
     * 地址前缀
     */
    private String addressPrefix = "redis://";;

    private boolean skipSslVerification = true;

    private String sslTrustStore;

    private String sslKeyStore;

    private String sslTrustStorePassword;

    private String sslKeyStorePassword;

    private int connectionPoolSize;

    private int connectionMinimumIdleSize;


}
