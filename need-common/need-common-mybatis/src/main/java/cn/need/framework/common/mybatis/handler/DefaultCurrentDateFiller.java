package cn.need.framework.common.mybatis.handler;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
public class DefaultCurrentDateFiller implements CurrentDateFiller<LocalDateTime> {


    @Override
    public LocalDateTime currentDate() {
        return ZonedDateTime.now(ZoneOffset.UTC).toLocalDateTime();
    }

    @Override
    public Class<LocalDateTime> dateClass() {
        return LocalDateTime.class;
    }
}
