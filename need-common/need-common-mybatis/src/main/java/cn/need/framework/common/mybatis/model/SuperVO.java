package cn.need.framework.common.mybatis.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * VO对象承载公共字段的超类，该类用来定义数据的创建人、创建时间等数据。
 *
 * <AUTHOR>
 * @since 2022/7/14

 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SuperVO implements Serializable {

    private static final long serialVersionUID = 8628352349152808902L;

    /**
     * 创建人id
     */
    private Long id;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 最后更新人id
     */
    private Long updateBy;

    /**
     * 最后更新人名称
     */
    private String updateByName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 最后更新时间
     */
    private String updateTime;

}
