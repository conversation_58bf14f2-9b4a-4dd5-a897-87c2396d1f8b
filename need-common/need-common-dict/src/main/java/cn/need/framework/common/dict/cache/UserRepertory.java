package cn.need.framework.common.dict.cache;

import cn.need.framework.common.dict.entity.UserCache;
import org.springframework.data.redis.core.BoundHashOperations;

import java.util.Collection;
import java.util.List;

/**
 * 用户缓存操作接口，包含了在缓存中对用户数据进行增、删、改、查等操作
 *
 * <AUTHOR>
 * @since 2022/8/25

 */
public interface UserRepertory {

    /**
     * 将单个用户缓存对象添加至redis缓存中
     *
     * @param user 用户缓存对象
     */
    void add(UserCache user);

    /**
     * 批量添加用户缓存对象至redis缓存中
     *
     * @param users 用户缓存对象集合
     */
    void add(Collection<UserCache> users);

    /**
     * 根据数据id从redis缓存中获取用户缓存对象
     *
     * @param id 数据主键id
     * @return UserCache 数用户缓存对象
     */
    UserCache getById(Long id);

    /**
     * 根据用户名从redis缓存中获取用户缓存对象
     *
     * @param username 数据编码
     * @return T 用户缓存对象
     */
    UserCache getByUsername(String username);

    /**
     * 根据siebel从redis缓存中获取用户缓存对象
     *
     * @param siebelId 数据编码
     * @return T 用户缓存对象
     */
    UserCache getBySiebelId(String siebelId);

    /**
     * 从redis缓存中获取所有用户缓存对象集合
     *
     * @return List<T> 用户缓存对象集合
     */
    List<UserCache> list();

    /**
     * 根据数据id集合从redis缓存中获取用户缓存对象集合
     *
     * @param ids 数据主键id集合
     * @return List<T> 用户缓存对象集合
     */
    List<UserCache> listByIds(Collection<Long> ids);

    /**
     * 根据用户名集合从redis缓存中获取用户缓存对象集合
     *
     * @param usernames 用户名集合
     * @return List<T> 用户缓存对象集合
     */
    List<UserCache> listByUsernames(Collection<String> usernames);

    /**
     * 根据siebelId集合从redis缓存中获取用户缓存对象集合
     *
     * @param siebelIds siebelId集合
     * @return List<T> 用户缓存对象集合
     */
    List<UserCache> listBySiebelIds(Collection<String> siebelIds);

    /**
     * 根据数据id从redis缓存中删除对应的用户缓存对象
     *
     * @param id 数据主键id
     */
    void delById(Long id);

    /**
     * 根据数据id集合从redis缓存中删除对应的用户缓存对象集合
     *
     * @param ids 数据主键id集合
     */
    void delByIds(Collection<Long> ids);

    /**
     * 根据数用户名从redis缓存中删除对应的用户缓存对象
     *
     * @param username 用户名
     */
    void delByUsername(String username);

    /**
     * 根据用户名集合从redis缓存中删除对应的用户缓存对象集合
     *
     * @param usernames 用户名集合
     */
    void delByUsernames(Collection<String> usernames);

    /**
     * 初始化用户缓存数据，会先清空对应的用户缓存集合，再将用户缓存对象集合缓存到redis中
     *
     * @param users 初始化的用户缓存对象集合
     */
    default void initialization(Collection<UserCache> users) {
        initialization(users, 0);
    }

    /**
     * 初始化用户缓存数据，会先清空对应的用户缓存集合，再将用户缓存对象集合缓存到redis中
     *
     * @param users     初始化的用户缓存对象集合
     * @param groupSize 分组数量 <= 0，表示不分组
     */
    void initialization(Collection<UserCache> users, int groupSize);

    /**
     * 清空redis中的用户缓存对象
     */
    void clear();

    /**
     * 获取RedisTemplate的BoundHashOperations对象，系统会为用户缓存数据集合构建一个hash对象，hashKey为用户用户ID
     *
     * @return BoundHashOperations<String, String, UserCache>
     */
    BoundHashOperations<String, String, UserCache> getHash();

}
