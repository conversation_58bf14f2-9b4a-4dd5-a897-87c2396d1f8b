package cn.need.framework.common.dict.impl;

import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.date.TimeMeter;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.map.Maps;
import cn.need.framework.common.dict.api.ConfigRepertory;
import cn.need.framework.common.dict.constant.DictConstant;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Map;
import java.util.Set;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * Class description goes here.
 *
 * <AUTHOR>
 */
@Slf4j
public class RedisConfigRepertory implements ConfigRepertory {

    /**
     * redisTemplate对象，需要注入
     */
    private final RedisTemplate<String, String> redisTemplate;
    /**
     * 配置数据在redis中的key值，可以注入
     */
    private final String redisKey;

    /**
     * 带redisTemplate参数的构造函数
     *
     * @param redisTemplate redisTemplate对象
     */
    public RedisConfigRepertory(RedisTemplate<String, String> redisTemplate) {
        this(redisTemplate, null);
    }

    /**
     * 带redisTemplate参数的构造函数
     *
     * @param redisTemplate redisTemplate对象
     * @param prefix        配置数据在redis中key的前缀
     */
    public RedisConfigRepertory(@NonNull RedisTemplate<String, String> redisTemplate, String prefix) {
        this.redisTemplate = redisTemplate;
        this.redisKey = isEmpty(prefix) ? DictConstant.CONFIG_REDIS_KEY : DictConstant.CONFIG_REDIS_KEY.concat(StringPool.COLON).concat(prefix);
    }

    /// ///////////////////////////////////// 接口方法实现 /////////////////////////////////////////////////////////////////////

    @Override
    public String get(String key) {
        return get(key, null);
    }

    @Override
    public String get(String key, String grouping) {
        return isEmpty(key) ? StringPool.EMPTY : nullToDefault(getHash(grouping).get(key), StringPool.EMPTY);
    }

    @Override
    public Map<String, String> grouping(String grouping) {
        return nullToDefault(getHash(grouping).entries(), Maps.hashMap());
    }

    @Override
    public void add(String key, String value) {
        add(key, value, null);
    }

    @Override
    public void add(String key, String value, String grouping) {
        if (isEmpty(key)) {
            return;
        }
        getHash(grouping).put(key, nullToDefault(value, StringPool.EMPTY));
    }

    @Override
    public void add(Map<String, String> configMap) {
        add(configMap, null);
    }

    @Override
    public void add(Map<String, String> configMap, String grouping) {
        Map<String, String> map = filterData(configMap);
        if (isEmpty(map)) {
            return;
        }
        getHash(grouping).putAll(map);
    }

    @Override
    public void del(String key) {
        del(key, null);
    }

    @Override
    public void del(String key, String grouping) {
        if (isEmpty(key)) {
            return;
        }
        getHash(grouping).delete(key);
    }

    @Override
    public void delGrouping(String grouping) {
        this.redisTemplate.delete(this.redisKey.concat(StringPool.COLON).concat(StringUtil.emptyToDefault(grouping, DictConstant.DEFAULT_GROUPING)));
    }

    /**
     * 清空所有配置信息
     */
    @Override
    public void clear() {
        TimeMeter meter = new TimeMeter();
        String pattern = this.redisKey.concat(StringPool.COLON).concat(StringPool.ASTERISK);
        Set<String> keys = nullToDefault(redisTemplate.keys(pattern), Lists.hashSet());
        keys.forEach(it -> {
            if (log.isDebugEnabled()) {
                log.debug("----------->> 删除系统配置分组：{}", it);
            }
            if (isNotEmpty(it)) {
                redisTemplate.delete(it);
            }
        });
        if (log.isDebugEnabled()) {
            log.debug("----------->> <<<清空>>>所有【系统配置】数据完成！分组数量：{}，用时：{}ms", keys, meter.sign());
        }
    }

    /// ///////////////////////////////////// 私有方法 /////////////////////////////////////////////////////////////////////

    @Override
    public void initialization(Map<String, Map<String, String>> configMap) {
        TimeMeter meter = new TimeMeter();
        if (log.isDebugEnabled()) {
            log.debug("============ 初始化缓存【系统配置】数据【开始】，初始化分组数量：{} ============", isNotNull(configMap) ? configMap.size() : 0);
        }
        //清空缓存数据
        clear();
        //将数据保存至redis
        if (isNotNull(configMap)) {
            configMap.forEach((key, map) -> {
                TimeMeter subMeter = new TimeMeter();
                add(map, key);
                if (log.isDebugEnabled()) {
                    log.debug("----------- 添加系统配置分组至redis，key：{}，数量：{}，耗时{}ms ============", key, map.size(), subMeter.sign());
                }
            });
        }
        if (log.isDebugEnabled()) {
            log.debug("============ 初始化缓存【系统配置】数据<<<完成>>>，总耗时{}ms ============", meter.sign());
        }
    }

    @Override
    public BoundHashOperations<String, String, String> getHash(String grouping) {
        return this.redisTemplate.boundHashOps(buildHashKey(grouping));
    }

    //////////////////////////////////////// 私有属性 /////////////////////////////////////////////////////////////////////

    /**
     * 过滤配置map的数据
     */
    private Map<String, String> filterData(Map<String, String> configMap) {
        Map<String, String> map = Maps.hashMap();
        configMap.forEach((key, value) -> {
            if (isEmpty(key)) {
                return;
            }
            map.put(key, nullToDefault(value, StringPool.EMPTY));
        });
        return map;
    }

    /**
     * 根级分组值获取redisTemplate的hashKey值
     */
    private String buildHashKey(String grouping) {
        grouping = StringUtil.emptyToDefault(grouping, DictConstant.DEFAULT_GROUPING);
        // 如果租户id不为空，并且key没有加工过就添加租户id的标识
//        if (Users.tenantId() != null && !grouping.contains("-")) {
//            // 添加租户标识
//            grouping = Users.tenantId() + "-" + grouping;
//        }
        return this.redisKey.concat(StringPool.COLON).concat(grouping);
    }

}
