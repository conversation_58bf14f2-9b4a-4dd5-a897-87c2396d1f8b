package cn.need.framework.common.oss.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Oss枚举类
 *
 * <AUTHOR>
 */
@SuppressWarnings("SpellCheckingInspection")
@Getter
@AllArgsConstructor
public enum OssEnum {

    /**
     * minio
     */
    MINIO("minio", "minio"),

    /**
     * qiniu
     */
    QINIU("qiniu", "qiniu"),

    /**
     * ali
     */
    ALI("ali", "ali"),

    /**
     * tencent
     */
    TENCENT("tencent", "tencent"),

    /**
     * amazon s3
     */
    AMAZON("amazons3", "amazons3"),

    /**
     * 本地文件上传
     */
    FILE("file", "file");

    /**
     * 名称
     */
    final String name;
    /**
     * 类型
     */
    final String category;

    /**
     * 匹配枚举值
     *
     * @param name 名称
     * @return OssEnum
     */
    public static OssEnum of(String name) {
        if (name == null) {
            return null;
        }
        OssEnum[] values = OssEnum.values();
        for (OssEnum ossEnum : values) {
            if (ossEnum.name.equals(name)) {
                return ossEnum;
            }
        }
        return null;
    }

}
