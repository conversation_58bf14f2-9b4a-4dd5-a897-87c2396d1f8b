package cn.need.framework.common.annotation.enums;

import lombok.Getter;

/**
 * 是否嵌套条件
 *
 * <AUTHOR>
 * @since 1.1.0
 */
@Getter
public enum NestedType {

    /**
     * 不嵌套
     */
    NON("non", "不做嵌套条件"),

    /**
     * 嵌套or条件
     */
    OR("or", "例：(name like '%val%' or code like '%val%')"),

    /**
     * 嵌套and条件
     */
    AND("and", "例：(name like '%val%' and code like '%val%')");

    private final String code;
    private final String description;

    NestedType(String code, String description) {
        this.code = code;
        this.description = description;
    }

}
